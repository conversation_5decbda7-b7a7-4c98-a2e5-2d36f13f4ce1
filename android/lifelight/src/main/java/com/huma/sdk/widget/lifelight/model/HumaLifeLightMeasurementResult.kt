package com.huma.sdk.widget.lifelight.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.huma.sdk.module.source.ModuleSource
import com.huma.sdk.module_kit.ability.ModuleDataType
import com.huma.sdk.module_kit.ability.generic.BloodPressureModuleDataType
import com.huma.sdk.module_kit.ability.generic.HeartRateModuleDataType
import com.huma.sdk.module_kit.ability.generic.RespiratoryRateModuleDataType
import com.huma.sdk.unitskit.utils.MAX_BLOOD_PRESSURE_DIASTOLIC_LEVEL_MMHG
import com.huma.sdk.unitskit.utils.MAX_BLOOD_PRESSURE_SYSTOLIC_LEVEL_MMHG
import com.huma.sdk.unitskit.utils.MAX_RESPIRATORY_RATE_RPM
import com.huma.sdk.unitskit.utils.MAX_RESTING_HEART_RATE
import com.huma.sdk.unitskit.utils.MIN_BLOOD_PRESSURE_DIASTOLIC_LEVEL_MMHG
import com.huma.sdk.unitskit.utils.MIN_BLOOD_PRESSURE_SYSTOLIC_LEVEL_MMHG
import com.huma.sdk.unitskit.utils.MIN_RESPIRATORY_RATE_RPM
import com.huma.sdk.unitskit.utils.MIN_RESTING_HEART_RATE
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class HumaLifeLightMeasurementResult(
    val bpSystolic: Int,
    val bpDiastolic: Int,
    val heartRate: Int,
    val respiratoryRate: Int,
) : Parcelable {
    @IgnoredOnParcel
    val isBloodPressureMeasurementValid = bpDiastolic in bpDiastolicRange &&
        bpSystolic in bpSystolicRange

    @IgnoredOnParcel
    val isHeartRateMeasurementValid = heartRate in heartRateRange

    @IgnoredOnParcel
    val isRespiratoryRateMeasurementValid = respiratoryRate in respiratoryRateRange

    @IgnoredOnParcel
    val isAllMeasurementValid: Boolean = isBloodPressureMeasurementValid &&
        isHeartRateMeasurementValid &&
        isRespiratoryRateMeasurementValid

    @IgnoredOnParcel
    val isAllMeasurementInvalid: Boolean = !isBloodPressureMeasurementValid &&
        !isHeartRateMeasurementValid &&
        !isRespiratoryRateMeasurementValid

    fun toModuleResultList(): List<ModuleDataType<out Any>> {
        val timestamp = System.currentTimeMillis()
        return listOfNotNull(
            run {
                if (!isBloodPressureMeasurementValid) {
                    return@run null
                }
                BloodPressureModuleDataType(
                    BloodPressureModuleDataType.BloodPressureData(
                        bpSystolic,
                        bpDiastolic,
                    ),
                    timestamp,
                    ModuleSource.LIFELIGHT.requestKey,
                )
            },
            run {
                if (!isHeartRateMeasurementValid) {
                    return@run null
                }
                HeartRateModuleDataType(
                    HeartRateModuleDataType.HeartData(
                        heartRate
                    ),
                    timestamp,
                    ModuleSource.LIFELIGHT.requestKey,
                )
            },
            run {
                if (!isRespiratoryRateMeasurementValid) {
                    return@run null
                }
                RespiratoryRateModuleDataType(
                    RespiratoryRateModuleDataType.RespiratoryRateData(
                        respiratoryRate,
                    ),
                    timestamp,
                    ModuleSource.LIFELIGHT.requestKey,
                )
            },
        )
    }

    companion object {
        internal val heartRateRange =
            MIN_RESTING_HEART_RATE.toInt()..MAX_RESTING_HEART_RATE.toInt()
        internal val bpSystolicRange =
            MIN_BLOOD_PRESSURE_SYSTOLIC_LEVEL_MMHG.toInt()..MAX_BLOOD_PRESSURE_SYSTOLIC_LEVEL_MMHG.toInt()
        internal val bpDiastolicRange =
            MIN_BLOOD_PRESSURE_DIASTOLIC_LEVEL_MMHG.toInt()..MAX_BLOOD_PRESSURE_DIASTOLIC_LEVEL_MMHG.toInt()
        internal val respiratoryRateRange =
            MIN_RESPIRATORY_RATE_RPM.toInt()..MAX_RESPIRATORY_RATE_RPM.toInt()
    }
}
