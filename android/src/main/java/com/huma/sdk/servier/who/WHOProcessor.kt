package com.huma.sdk.servier.who

import android.content.Context
import com.huma.sdk.core.utils.ext.asList
import com.huma.sdk.core.utils.ext.orIfNullOrEmpty
import com.huma.sdk.foundation.resources.R
import com.huma.sdk.module.base.questionnaire.converter.QuestionnaireRetrievePrimitiveResponseMapper
import com.huma.sdk.module.converter.AboutConverter
import com.huma.sdk.module.converter.ScheduleConverter
import com.huma.sdk.module.ext.textOrBlank
import com.huma.sdk.module.processor.ImportModuleProcessor
import com.huma.sdk.module_kit.Module
import com.huma.sdk.module_kit.factory.ModuleConfigId
import com.huma.sdk.questionnaire.ext.asForm
import com.huma.sdk.shared.deployment.domain.entity.ModuleConfig
import com.huma.sdk.shared.deployment.storage.DeploymentStorage

class WHOProcessor(
    private val context: Context,
    private val scheduleConverter: ScheduleConverter,
    private val aboutConverter: AboutConverter,
    private val deviceName: String
) : ImportModuleProcessor(
    moduleId = MODULE_ID,
    primitiveResultToEntityMappers = QuestionnaireRetrievePrimitiveResponseMapper().asList()
) {

    companion object {
        const val MODULE_ID = "WHO"
    }

    override fun processConfig(moduleConfig: List<ModuleConfig>): List<Pair<ModuleConfigId, Module>> {
        return moduleConfig
            .filter { it.moduleId == MODULE_ID }
            .mapNotNull { config ->
                val moduleId = config.id ?: return@mapNotNull null
                val scheduleData = config.schedule?.let { scheduleConverter.invoke(it) }
                val aboutData = aboutConverter.invoke(config)
                registerModuleConfigId(moduleId)
                moduleId to WHOModule(
                    order = config.order ?: Int.MAX_VALUE,
                    version = config.version,
                    moduleName = config.moduleName.orIfNullOrEmpty(
                        context.getString(R.string.module_who_name)
                    ),
                    deviceName = deviceName,
                    deploymentId = DeploymentStorage.deploymentId,
                    moduleConfigId = moduleId,
                    scheduleData = scheduleData,
                    staticEvent = config.staticEvent,
                    aboutData = aboutData,
                    form = config.configBody.asForm(config.footnote),
                    disclaimer = config.footnote.textOrBlank(),
                    learnArticleIds = config.learnArticleIds ?: emptyList(),
                    learnArticles = config.learnArticles ?: emptyList()
                )
            }
    }
}
