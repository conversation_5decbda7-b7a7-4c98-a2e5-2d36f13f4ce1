package com.huma.sdk.servier.surgicalchecklist.provider

import androidx.annotation.Keep
import com.huma.sdk.core.di.koin.HumaSdkKoinContext
import com.huma.sdk.servier.surgicalchecklist.di.surgicalChecklistModule
import com.huma.sdk.servier.surgicalchecklist.presentation.SurgicalChecklistWidget
import com.huma.sdk.shared.deployment.storage.DeploymentStorage
import com.huma.sdk.widget.kit.Widget
import com.huma.sdk.widget.kit.provider.WidgetParams
import com.huma.sdk.widget.kit.provider.WidgetProvider

@Keep
class SurgicalChecklistWidgetProvider : WidgetProvider {

    override fun initializer() {
        HumaSdkKoinContext.loadModules(surgicalChecklistModule)
    }

    override fun provideWidgets(params: List<WidgetParams>): List<Widget> {
        val widgetParams = params.filter { it.second == SurgicalChecklistWidget.TYPE }.map { it.first }
        return DeploymentStorage.deployment.builder?.tabs?.flatMap { tabs ->
            tabs.widgets?.filter { widget -> widgetParams.contains(widget.id) }?.map {
                SurgicalChecklistWidget(id = it.id, type = it.type, order = it.order, config = it.config)
            } ?: emptyList()
        } ?: emptyList()
    }
}
