package com.huma.sdk.servier.surgicalchecklist.presentation.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.text.parseAsHtml
import com.huma.sdk.ui.R
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.components.composable.chip.action.ActionChip
import com.huma.sdk.ui.components.composable.chip.action.style.DefaultActionChipStyle
import com.huma.sdk.ui.components.composable.shimmer.shimmerBrush
import com.huma.sdk.ui.humastyle.HumaTypeStyler

@Composable
fun SurgeryDate(
    surgeryDateTitle: String,
    surgeryDateDescription: String,
    isDateEditable: Boolean = true,
    isShimmering: Boolean = false,
    isSurgeryDateVisible: Boolean = true,
    onEditClick: () -> Unit = {}
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(end = 16.dp)
        ) {
            Box {
                BaseText(
                    text = surgeryDateTitle.parseAsHtml(),
                    style = HumaTypeStyler.body
                )
                if (isShimmering) {
                    Spacer(
                        modifier = Modifier
                            .matchParentSize()
                            .clip(RoundedCornerShape(8.dp))
                            .background(shimmerBrush())
                    )
                }
            }
            if (isSurgeryDateVisible) {
                Box(modifier = Modifier.padding(top = 4.dp)) {
                    BaseText(
                        text = surgeryDateDescription,
                        style = HumaTypeStyler.caption1,
                    )
                    if (isShimmering) {
                        Spacer(
                            modifier = Modifier
                                .matchParentSize()
                                .clip(RoundedCornerShape(8.dp))
                                .background(shimmerBrush())
                        )
                    }
                }
            }
        }
        if (isDateEditable) {
            Box {
                ActionChip(
                    text = "Edit",
                    actionChipType = DefaultActionChipStyle.secondary,
                    onClick = onEditClick
                )
                if (isShimmering) {
                    Spacer(
                        modifier = Modifier
                            .matchParentSize()
                            .clip(RoundedCornerShape(16.dp))
                            .background(shimmerBrush())
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun SurgeryDatePreview() {
    SurgeryDate(stringResource(R.string.widget_surgical_checklist_surgery_date), "11-11-1111")
}

@Preview
@Composable
fun SurgeryDateWithoutEditPreview() {
    SurgeryDate(
        surgeryDateTitle = stringResource(id = R.string.widget_surgical_checklist_surgery_date),
        surgeryDateDescription = "11-11-1111",
        isDateEditable = false
    )
}

@Preview
@Composable
fun SurgeryDateShimmerPreview() {
    SurgeryDate(
        stringResource(R.string.widget_surgical_checklist_surgery_date),
        "11-11-1111",
        isShimmering = true
    )
}

@Preview
@Composable
fun SurgeryDateWithoutEditShimmerPreview() {
    SurgeryDate(
        surgeryDateTitle = stringResource(R.string.widget_surgical_checklist_surgery_date),
        surgeryDateDescription = "11-11-1111",
        isDateEditable = false,
        isShimmering = true
    )
}
