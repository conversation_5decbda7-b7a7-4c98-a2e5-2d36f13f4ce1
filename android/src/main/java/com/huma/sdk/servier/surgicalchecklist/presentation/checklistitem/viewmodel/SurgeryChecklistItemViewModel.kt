package com.huma.sdk.servier.surgicalchecklist.presentation.checklistitem.viewmodel

import com.huma.sdk.servier.surgicalchecklist.domain.usecase.MarkSurgeryItemCompletedUseCase
import com.huma.sdk.servier.surgicalchecklist.domain.usecase.MarkSurgeryItemNotCompletedUseCase
import com.huma.sdk.ui.viewmodel.MVIViewModel
import org.threeten.bp.Instant

class SurgeryChecklistItemViewModel(
    private val markSurgeryItemCompletedUseCase: MarkSurgeryItemCompletedUseCase,
    private val markSurgeryItemNotCompletedUseCase: MarkSurgeryItemNotCompletedUseCase,
) : MVIViewModel<State, PartialState, Intent, Unit>(State.Idle(false)) {
    fun markAsCompleted(keyActionId: String, model: String, startDateTime: Instant, endDateTime: Instant) {
        intent(
            Intent.MarkSurgeryItemAsComplete(
                keyActionId,
                model,
                startDateTime,
                endDateTime,
                markSurgeryItemCompletedUseCase
            )
        )
    }

    fun markAsNotCompleted(keyActionId: String) {
        intent(Intent.MarkSurgeryItemAsNotComplete(keyActionId, markSurgeryItemNotCompletedUseCase))
    }

    fun setState(state: Boolean) {
        intent(Intent.SetState(state))
    }
}
