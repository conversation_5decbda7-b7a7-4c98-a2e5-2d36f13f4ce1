package com.huma.sdk.servier.surgicalchecklist.presentation.view

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.huma.sdk.ui.R
import com.huma.sdk.ui.components.base.Palette.Base.GRAY_GALLERY
import com.huma.sdk.ui.components.base.toComposeColor
import com.huma.sdk.ui.components.composable.card.CardContainer

@Composable
fun SurgicalChecklistCard(
    title: String,
    completedCount: Int,
    totalCount: Int,
    surgeryDateTitle: String,
    surgeryDateDescription: String,
    isProgressVisible: Boolean = true,
    isSurgeryDateVisible: Boolean = true,
    isSurgeryDateEditable: Boolean = false,
    isLoading: Boolean = false,
) {
    CardContainer {
        Column(modifier = Modifier.padding(16.dp)) {
            Header(
                title = title,
                completedCount = completedCount,
                totalCount = totalCount,
                isProgressVisible = isProgressVisible,
                isShimmering = isLoading
            )
            HorizontalDivider(
                modifier = Modifier.padding(vertical = 16.dp),
                color = GRAY_GALLERY.toComposeColor()
            )
            if (isSurgeryDateVisible) {
                SurgeryDate(
                    surgeryDateTitle = surgeryDateTitle,
                    surgeryDateDescription = surgeryDateDescription,
                    isDateEditable = isSurgeryDateEditable,
                    isShimmering = isLoading
                )
            }
        }
    }
}

@Preview
@Composable
fun SurgicalChecklistCardPreview() {
    SurgicalChecklistCard(
        title = "Title",
        completedCount = 4,
        totalCount = 10,
        surgeryDateTitle = stringResource(R.string.widget_surgical_checklist_surgery_date),
        surgeryDateDescription = "11-11-1111",
    )
}

@Preview
@Composable
fun SurgicalChecklistCardShimmerPreview() {
    SurgicalChecklistCard(
        title = "Title",
        completedCount = 4,
        totalCount = 10,
        surgeryDateTitle = stringResource(R.string.widget_surgical_checklist_surgery_date),
        surgeryDateDescription = "11-11-1111",
        isLoading = true
    )
}
// endregion
