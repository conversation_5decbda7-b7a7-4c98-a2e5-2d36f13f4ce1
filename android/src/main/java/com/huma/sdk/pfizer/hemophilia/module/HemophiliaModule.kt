package com.huma.sdk.pfizer.hemophilia.module

import android.content.Context
import androidx.annotation.Keep
import com.huma.sdk.core.di.DEVICE_NAME
import com.huma.sdk.core.di.koin.HumaSdkKoinComponent
import com.huma.sdk.core.utils.commons.Range
import com.huma.sdk.module.delegate.result.moduleResultDelegate
import com.huma.sdk.module.delegate.result.moduleResultObserverDelegate
import com.huma.sdk.module.ext.defaultPostDelegate
import com.huma.sdk.module.medicationv2.core.data.converters.RemoteMedicationToMedicationV2Converter
import com.huma.sdk.module_kit.ModuleWithDetails
import com.huma.sdk.module_kit.ModuleWithInput
import com.huma.sdk.module_kit.ModuleWithResult
import com.huma.sdk.module_kit.analytics.sources.ModuleEventSource
import com.huma.sdk.module_kit.core.BaseModule
import com.huma.sdk.module_kit.core.delegate.details.section.Section
import com.huma.sdk.module_kit.core.delegate.input.extra.ExtraButton
import com.huma.sdk.module_kit.data.ModuleResult
import com.huma.sdk.module_kit.display.card.CardConfig
import com.huma.sdk.module_kit.display.input.ui.data.output.ModuleInputResult
import com.huma.sdk.module_kit.display.input.ui.data.output.ModuleInputSubmitParams
import com.huma.sdk.module_kit.factory.ModuleId
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaBodyLocationType
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryReason
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjurySeverity
import com.huma.sdk.pfizer.hemophilia.model.HemophiliaInjuryType
import com.huma.sdk.pfizer.hemophilia.module.converter.HemophiliaModulePrimitiveToEntityConverter
import com.huma.sdk.pfizer.hemophilia.module.converter.HemophiliaModuleValueToPrimitiveConverter
import com.huma.sdk.pfizer.hemophilia.module.primitive.HemophiliaModuleRetrievePrimitiveResponse
import com.huma.sdk.shared.deployment.storage.DeploymentStorage
import com.huma.sdk.shared.primitive.entity.RetrievePrimitiveResponse
import kotlinx.coroutines.flow.StateFlow
import kotlinx.serialization.KSerializer
import org.koin.core.component.inject
import org.koin.core.qualifier.named

@Keep
class HemophiliaModule(
    override val order: Int,
    override val moduleConfigId: String,
    override val version: Int,
) : BaseModule(order), ModuleWithDetails, ModuleWithInput,
    ModuleWithResult<HemophiliaModuleResult>, HumaSdkKoinComponent {
    private val deviceName: String by inject(named(DEVICE_NAME))
    private val medicationV2Converter: RemoteMedicationToMedicationV2Converter by inject()

    override val moduleName: String = "Bleed Log"
    override val analyticsName: String = "Bleed Log"
    override val moduleId: String = MODULE_ID
    override val responsePrimitiveClasses: Map<ModuleId, Class<*>>
        get() = mapOf(MODULE_ID to HemophiliaModuleRetrievePrimitiveResponse::class.java)
    override val responsePrimitiveClassesSerializers: Map<ModuleId, KSerializer<out RetrievePrimitiveResponse>>
        get() = mapOf(MODULE_ID to HemophiliaModuleRetrievePrimitiveResponse.serializer())

    private val postDelegate by defaultPostDelegate(
        HemophiliaModuleValueToPrimitiveConverter(
            moduleConfigId = moduleConfigId,
            deploymentId = DeploymentStorage.deploymentId,
            version = version,
            deviceName = deviceName,
        ),
        HemophiliaModulePrimitiveToEntityConverter(moduleConfigId), moduleId, moduleConfigId
    )

    override val moduleCardConfig: CardConfig = CardConfig()
    override val sections: List<Section> = emptyList()
    override val extraButtons: List<ExtraButton> = emptyList()

    override fun openDetails(context: Context) {
        // Do nothing
    }

    override val inputButtonText: String = ""
    override val disclaimer: String = ""

    override fun openInput(
        context: Context,
        source: ModuleEventSource,
        onSubmit: (ModuleInputResult) -> Unit
    ) {
        // Do nothing
    }

    override fun onModuleInputSubmit(
        result: ModuleInputResult,
        params: ModuleInputSubmitParams?,
    ) {
        postDelegate.postValue(result.values, params = params)
    }

    private val resultMap: (RetrievePrimitiveResponse) -> ModuleResult<HemophiliaModuleResult>? =
        { result ->
            (result as? HemophiliaModuleRetrievePrimitiveResponse)?.let { record ->
                val bodyInjuryType = record.bodyPartInjury?.let { bodyPart ->
                    HemophiliaInjuryType.entries.firstOrNull {
                        it.name == bodyPart
                    }
                } ?: return@let null
                val bodyLocation = record.bodyLocation?.let { bodyPart ->
                    HemophiliaBodyLocationType.entries.firstOrNull {
                        it.name == bodyPart
                    }
                }
                val extra = record.extraData ?: return@let null
                val accidentDate = extra.accidentDate ?: return@let null
                ModuleResult(
                    result,
                    HemophiliaModuleResult(
                        record.creationDateTime,
                        record.startDateTime,
                        bodyLocation,
                        bodyInjuryType,
                        record.customBodyPart,
                        accidentDate,
                        HemophiliaInjuryReason.forValue(extra.reason),
                        extra.note,
                        extra.photos,
                        extra.scale,
                        HemophiliaInjurySeverity.forValue(extra.severity),
                        extra.treatment?.let { medicationV2Converter(it) },
                        extra.factorUnits,
                    )
                )
            }
        }

    override suspend fun queryResult(range: Range?): List<ModuleResult<HemophiliaModuleResult>> {
        return moduleResultDelegate(range, resultMap)
    }

    override suspend fun observeResult(range: Range?): StateFlow<List<ModuleResult<HemophiliaModuleResult>>> {
        return moduleResultObserverDelegate(range, resultMap)
    }

    companion object {
        const val MODULE_ID = "HemophiliaJournal"
    }
}
