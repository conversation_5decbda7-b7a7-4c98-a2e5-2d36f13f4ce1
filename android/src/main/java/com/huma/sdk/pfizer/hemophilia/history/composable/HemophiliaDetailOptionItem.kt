package com.huma.sdk.pfizer.hemophilia.history.composable

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.huma.sdk.pfizer.R
import com.huma.sdk.ui.components.base.Palette
import com.huma.sdk.ui.components.base.toComposeColor
import com.huma.sdk.ui.components.composable.base_text.BaseText
import com.huma.sdk.ui.humastyle.HumaTypeStyler

@Composable
fun HemophiliaDetailOptionItem(
    name: String,
    description: String,
    modifier: Modifier = Modifier,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .padding(horizontal = 24.dp)
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
        ) {
            Spacer(Modifier.height(14.dp))
            BaseText(
                text = name,
                style = HumaTypeStyler.body,
            )
            Spacer(Modifier.height(4.dp))
            BaseText(
                text = description,
                style = HumaTypeStyler.caption1,
            )
            Spacer(Modifier.height(14.dp))
            HorizontalDivider(color = Palette.Base.GRAY_GALLERY.toComposeColor())
        }
        Image(
            painterResource(R.drawable.hsdk_ic_details_arrow),
            contentDescription = null,
        )
    }
}
