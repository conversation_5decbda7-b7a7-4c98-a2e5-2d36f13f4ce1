import logging

from apiflask import APIBlueprint
from flask import request, jsonify

from app.components.auth_helper.usecase import (
    QAHelperBillingProfileHistoryLogRequestObject,
    QAHelperBillingProfileHistoryLogUseCase,
    SubmissionAutomationHelperUseCase,
    SubmissionHelperRequestObject,
)
from sdk.common.utils.flask_request_utils import (
    get_request_json_dict_or_raise_exception,
)

rpm_helper_route = APIBlueprint(
    "qa_helper_rpm",
    __name__,
    url_prefix="/helper/qa",
    template_folder="templates",
    static_folder="static",
)

logger = logging.getLogger("QAHelper")


@rpm_helper_route.post("/history-log/user/<user_id>")
def add_billing_profile_history_log(user_id: str):
    logger.info(f"add_billing_profile_history_log for user {user_id} - request: {request.json}")
    body = get_request_json_dict_or_raise_exception(request)
    deployment_id = body.get("deploymentId")
    create_date_time = body.get("createDateTime")

    request_obj = QAHelperBillingProfileHistoryLogRequestObject.from_dict(
        dict(
            deploymentId=deployment_id,
            userId=user_id,
            createDateTime=create_date_time,
            data=body,
        )
    )
    response = QAHelperBillingProfileHistoryLogUseCase().execute(request_obj)
    return jsonify(response), 200


@rpm_helper_route.post("/submission/user/<user_id>/deployment/<deployment_id>")
def add_submission_automation_helper(user_id, deployment_id):
    body = get_request_json_dict_or_raise_exception(request)
    data = {
        SubmissionHelperRequestObject.USER_ID: user_id,
        SubmissionHelperRequestObject.DEPLOYMENT_ID: deployment_id,
        **body,
    }
    request_object = SubmissionHelperRequestObject.from_dict(data)
    SubmissionAutomationHelperUseCase().execute(request_object)
    return jsonify({}), 201
