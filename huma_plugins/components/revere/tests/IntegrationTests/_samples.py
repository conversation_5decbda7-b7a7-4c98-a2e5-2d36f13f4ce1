from unittest.mock import MagicMock

from sdk.authorization.dtos.role.role import <PERSON><PERSON><PERSON>
from sdk.authorization.dtos.user import RoleAssignmentDTO, UserDTO
from sdk.common.utils import inject
from sdk.deployment.repository.deployment_repository import DeploymentRepository
from sdk.tests.application_test_utils.test_utils import USER_ID

VALID_TEST_ID = "5eea217330fdc7bd52d352da"
INVALID_TEST_ID = "6eea217330fdc7bd52d352da"
VALID_DEPLOYMENT_ID = "5eea14f0747a4aa3addf1bae"
NOT_PROCESSED_WORD_LIST_ID = "5ee7796831a65ca9a9e287e5"
SENTENCE = "Didn't get that!"
SAMPLE_BUILD_NUMBER = "1151"


def get_sample_audio_submission_request():
    audio_s3 = {"bucket": "test", "key": "test.wav", "region": "cn"}
    data = {
        "type": "RevereTest",
        "deviceName": "iOS",
        "deploymentId": VALID_DEPLOYMENT_ID,
        "startDateTime": "2020-04-28T21:13:07Z",
        "audioResult": audio_s3,
        "initialWords": [
            "Parent",
            "Radio",
            "Nose",
            "River",
            "Helmet",
            "Music",
            "Sailor",
            "School",
            "Garden",
            "Coffee",
            "Drum",
            "House",
            "Machine",
            "Color",
            "Farmer",
        ],
    }
    return data


def mocked_g():
    def bind(binder):
        binder.bind_to_provider(DeploymentRepository, MagicMock())

    inject.clear_and_configure(bind)

    gl = MagicMock()
    role = RoleAssignmentDTO.create_role(RoleName.USER, VALID_DEPLOYMENT_ID)
    gl.user = UserDTO(id=USER_ID, roles=[role])
    return gl
