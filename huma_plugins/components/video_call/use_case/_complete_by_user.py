from huma_plugins.components.video_call.dtos.video_call import (
    VideoCallLogDTO,
    VideoCallDTO,
)
from huma_plugins.components.video_call.router.video_call_request import (
    CompleteVideoCallByUserRequestObject,
)
from sdk.common.exceptions import PermissionDenied
from sdk.common.utils.common_functions_utils import find
from ._base import BaseVideoUseCase


class CompleteVideoCallByUserUseCase(BaseVideoUseCase):
    request_object: CompleteVideoCallByUserRequestObject

    def process_request(self, req_obj: CompleteVideoCallByUserRequestObject):
        video_call = self._retrieve_video_call(req_obj.videoCallId)
        if req_obj.userId != video_call.userId:
            raise PermissionDenied

        reason = self._find_reason(video_call)

        self._add_video_call_log(
            req_obj.videoCallId,
            VideoCallLogDTO.EventType.ROOM_FINISHED.value,
            f"user:{req_obj.userId}",
            reason=reason,
        )
        self._complete_video_call(video_call)

    def _find_reason(self, video_call: VideoCallDTO) -> VideoCallDTO.CallStatus:
        reason = self.request_object.reason

        if reason is None:
            event = find(
                lambda x: x.event == VideoCallLogDTO.EventType.USER_JOINED.value
                and x.identity == f"user:{self.request_object.userId}",
                video_call.logs,
            )
            if event:
                reason = VideoCallDTO.CallStatus.ANSWERED
            else:
                reason = self._find_reason_from_logs(video_call.logs)

        return reason
