from datetime import datetime

from sdk.common.utils.convertible import required_field, meta, convertibleclass
from sdk.common.utils.validators import (
    validate_len,
    default_datetime_meta,
    validate_object_id,
)


@convertibleclass
class OrganizationAPISecretDTO:
    value: str = required_field(metadata=meta(validate_len(128, 128)))
    organizationId: str = required_field(metadata=meta(validate_object_id))
    createDateTime: datetime = required_field(metadata=default_datetime_meta())
