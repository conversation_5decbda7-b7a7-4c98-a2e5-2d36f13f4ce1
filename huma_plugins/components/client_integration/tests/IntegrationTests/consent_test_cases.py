from pathlib import Path

from flask import url_for

from huma_plugins.components.client_integration.component import (
    ClientIntegrationComponent,
)
from huma_plugins.components.client_integration.utils import generate_signature
from huma_plugins.tests.plugin_test_case import PluginsTestCase
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.deployment.component import DeploymentComponent
from sdk.organization.component import OrganizationComponent


VALID_DEPLOYMENT_ID = "5fe0b52b24f10259fa13bf1a"
VALID_SECRET = "05cf0b166482d52661a262fa3a0aa31040970e341943386fa2fa30d6935b199b8bdb3d63fafc290616b47d4702f7a0923f245d80d42aa63ba3459654007586a8"


class IntegrationConsentTestCase(PluginsTestCase):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        ClientIntegrationComponent(),
    ]

    fixtures = [
        Path(__file__).parent.joinpath("fixtures/organizations.json"),
        Path(__file__).parent.joinpath("fixtures/secrets.json"),
        Path(__file__).parent.joinpath("fixtures/users.json"),
    ]

    @classmethod
    def setUpClass(cls) -> None:
        super(IntegrationConsentTestCase, cls).setUpClass()
        cls.user_data = {"email": "<EMAIL>"}
        cls.sign_consent_url = url_for("integration_auth_route.sign_consent")

    @classmethod
    def _get_headers(cls, deployment_id: str = VALID_DEPLOYMENT_ID):
        signature = generate_signature(VALID_SECRET, deployment_id)
        return {
            "x-deployment-id": VALID_DEPLOYMENT_ID,
            "x-huma-signature": signature,
        }

    def test_sign_consent_successful(self):
        rsp = self.flask_client.post(
            self.sign_consent_url,
            json=self.user_data,
            headers=self._get_headers(),
        )
        self.assertEqual(201, rsp.status_code)
