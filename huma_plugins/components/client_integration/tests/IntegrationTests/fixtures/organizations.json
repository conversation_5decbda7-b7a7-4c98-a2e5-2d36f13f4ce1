{"organization": [{"_id": {"$oid": "63b67ff40d4b124707c3d29b"}, "termAndConditionUrl": "https://test.com", "privacyPolicyUrl": "https://test.com", "eulaUrl": "https://test.com", "name": "test", "status": "DRAFT", "enrollmentTarget": 0, "studyCompletionTarget": 0, "viewType": "RPM", "dashboardId": "OrganizationOverview", "features": {"newRolesSupport": true}, "submitterId": "63ce44193d6527f1c3ceafe3", "deploymentIds": ["5fe0b52b24f10259fa13bf1c"], "targetConsented": 0}, {"_id": {"$oid": "63b67ff40d4b124707c3d29c"}, "termAndConditionUrl": "https://test.com", "privacyPolicyUrl": "https://test.com", "eulaUrl": "https://test.com", "name": "another org", "status": "DRAFT", "enrollmentTarget": 0, "studyCompletionTarget": 0, "viewType": "RPM", "dashboardId": "OrganizationOverview", "features": {"newRolesSupport": true}, "submitterId": "63ce44193d6527f1c3ceafe3", "deploymentIds": ["5fe0b52b24f10259fa13bf1a"], "targetConsented": 0}], "deployment": [{"_id": {"$oid": "5fe0b52b24f10259fa13bf1c"}, "name": "org deployment"}, {"_id": {"$oid": "5fe0b52b24f10259fa13bf1a"}, "name": "Other deployment", "consent": {"id": {"$oid": "62b84be4051e445549b245f1"}, "enabled": "ENABLED", "revision": 0, "sections": [], "createDateTime": {"$date": "2024-08-16T11:44:35.252Z"}}}]}