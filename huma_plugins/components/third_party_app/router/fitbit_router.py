import logging

import i18n
import werkzeug.exceptions as wex
from apiflask import APIBlueprint
from flask import render_template, request

from huma_plugins.components.third_party_app.config.config import FitbitConfig
from huma_plugins.components.third_party_app.exceptions import (
    FitbitNotAllPermissionsGranted,
    InvalidFitbitCodeException,
)
from huma_plugins.components.third_party_app.models.third_party_app import ThirdPartyAppType
from huma_plugins.components.third_party_app.router.request_objects import BaseThirdPartyAppAuthorizeRequestObject
from huma_plugins.components.third_party_app.router.request_objects.fitbit_request_object import (
    AuthorizeFitbitRequestObject,
    FITBIT_CODE_KEY,
    FitBitHeader,
    FitbitRedirectHeadersObject,
    FitbitRedirectQueryObject,
    NotificationSchema,
    ProcessFitbitNotificationRequestObject,
    VerifySubscriptionRequestQuery,
)
from huma_plugins.components.third_party_app.use_cases.fitbit.authorize_fitbit_use_case import AuthorizeFitbitUseCase
from huma_plugins.components.third_party_app.use_cases.fitbit.fitbit_owner_exists_use_case import (
    FitbitOwnerExistsUseCase,
)
from huma_plugins.components.third_party_app.use_cases.fitbit.process_fitbit_notification_use_case import (
    ProcessFitbitNotificationUseCase,
)
from sdk.common.usecase.response_object import html_response
from sdk.common.utils import inject
from sdk.common.utils.convertible import (
    ConvertibleClassValidationError,
)
from sdk.phoenix.config.server_config import PhoenixServerConfig

fitbit_api = APIBlueprint(
    "fitbit",
    __name__,
    url_prefix="/api/extensions/v1/fitbit",
    template_folder="templates",
    tag="Internal",
)

logger = logging.getLogger("FitbitNotification")


@fitbit_api.get("/notify")
@fitbit_api.input(VerifySubscriptionRequestQuery.Schema, location="query")
@fitbit_api.doc(
    responses={
        204: {"description": "Code is valid"},
        404: {"description": "Invalid code"},
    }
)
def verify_subscriber(query_data: VerifySubscriptionRequestQuery):
    """Verify Subscription
    https://dev.fitbit.com/build/reference/web-api/developer-guide/using-subscriptions/#Verifying-a-Subscriber
    """
    code = query_data.verify
    server_config = inject.instance(PhoenixServerConfig)
    config: FitbitConfig = server_config.server.adapters.fitbit
    if not config.subscription or not config.subscription.enabled:
        return "", 404

    is_code_valid = code and code == config.subscription.verifyCode
    return "", 204 if is_code_valid else 404


def fitbit_notify_or_check(just_check_user: bool, fitbit_header: FitBitHeader, json_data: list[dict]):
    """Receive Subscription Notification
    https://dev.fitbit.com/build/reference/web-api/developer-guide/using-subscriptions/#Configuring-a-Subscriber
    """
    if fitbit_header.signature is None:
        raise wex.NotFound()

    server_config = inject.instance(PhoenixServerConfig)
    config: FitbitConfig = server_config.server.adapters.fitbit
    if not config.subscription or not config.subscription.enabled:
        return "", 404

    try:
        request_object = ProcessFitbitNotificationRequestObject.from_dict(
            {
                ProcessFitbitNotificationRequestObject.NOTIFICATIONS: json_data,
                ProcessFitbitNotificationRequestObject.FITBIT_SIGNATURE: fitbit_header.signature,
            }
        )
    except ConvertibleClassValidationError as ex:
        if ProcessFitbitNotificationRequestObject.FITBIT_SIGNATURE in ex.args[0]:
            raise wex.NotFound()
        raise ex
    if just_check_user:
        FitbitOwnerExistsUseCase().execute(request_object)
    else:
        ProcessFitbitNotificationUseCase().execute(request_object)
    return "", 204


@fitbit_api.post("/notify")
@fitbit_api.input(FitBitHeader.Schema, location="headers", arg_name="fitbit_header")
@fitbit_api.input(NotificationSchema(many=True))
@fitbit_api.doc(responses={204: "Success", 404: "Signature not found or Fitbit config is disabled"})
def receive_notification(fitbit_header: FitBitHeader, json_data: list[dict]):
    return fitbit_notify_or_check(just_check_user=False, fitbit_header=fitbit_header, json_data=json_data)


@fitbit_api.post("/user-exists")
@fitbit_api.input(FitBitHeader.Schema, location="headers", arg_name="fitbit_header")
@fitbit_api.input(NotificationSchema(many=True))
@fitbit_api.doc(
    responses={
        204: "Success",
        404: "Signature not found, Fitbit config is disabled, or Fitbit User doesn't exist",
    }
)
def fitbit_user_exists(fitbit_header: FitBitHeader, json_data: list[dict]):
    """
    Used by HCP not fitbit, in order to find the right backend in backend per customer
    """
    return fitbit_notify_or_check(just_check_user=True, fitbit_header=fitbit_header, json_data=json_data)


@fitbit_api.get("/redirect")
@fitbit_api.input(FitbitRedirectHeadersObject.Schema, location="headers")
@fitbit_api.input(FitbitRedirectQueryObject.Schema, location="query")
@fitbit_api.doc(**html_response())
def redirect(headers_data: FitbitRedirectHeadersObject, query_data: FitbitRedirectQueryObject):
    server_config = inject.instance(PhoenixServerConfig)
    config: FitbitConfig = server_config.server.adapters.fitbit
    title = i18n.t("Fitbit.Redirect.title", locale=headers_data.language)
    body = i18n.t("Fitbit.Redirect.body", locale=headers_data.language)
    button_text = i18n.t("Fitbit.Redirect.buttonText", locale=headers_data.language)
    button_redirect_url = config.customizedRedirectURI or request.url

    user_id = query_data.state
    fitbit_code_string = ""
    try:
        if query_data.code and user_id:
            request_object = AuthorizeFitbitRequestObject.from_dict(
                {
                    AuthorizeFitbitRequestObject.TYPE: ThirdPartyAppType.FITBIT,
                    AuthorizeFitbitRequestObject.CODE: query_data.code,
                    BaseThirdPartyAppAuthorizeRequestObject.USER_ID: user_id,
                }
            )
            AuthorizeFitbitUseCase().execute(request_object)
            fitbit_code_string = f"?{FITBIT_CODE_KEY}={query_data.code}"
    except InvalidFitbitCodeException:
        logger.info(f"Invalid Fitbit code for user {user_id} - {query_data.code}")
    except FitbitNotAllPermissionsGranted as ex:
        logger.info(f"Fitbit not all permissions granted for user {user_id}")
        return render_template(
            "fitbit_redirect_error.html",
            title="Oops...",
            body=ex.debug_message,
            button_text=button_text,
            redirect_url=button_redirect_url + fitbit_code_string,
        )

    return render_template(
        "fitbit_redirect.html",
        title=title,
        body=body,
        button_text=button_text,
        redirect_url=button_redirect_url + fitbit_code_string,
    )
