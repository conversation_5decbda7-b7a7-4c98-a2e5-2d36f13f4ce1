from datetime import datetime
from typing import Union, Optional

from sdk.common.exceptions.exceptions import DetailedException, ErrorCodes


class DCTErrorCodes:
    THIRD_PARTY_APP_ALREADY_EXISTS = 200001
    FITBIT_NOT_ALL_PERMISSIONS_GRANTED = 200002
    WITHINGS_NOT_ALL_PERMISSIONS_GRANTED = 200003


class InvalidThirdPartyAppTypeException(DetailedException):
    def __init__(self, app_type):
        super().__init__(
            code=ErrorCodes.INVALID_REQUEST,
            debug_message=f"Third Party App type [{app_type}] is invalid.",
            status_code=400,
        )


class InvalidFitbitCodeException(DetailedException):
    def __init__(self, code):
        super().__init__(
            code=ErrorCodes.INVALID_REQUEST,
            debug_message=f"Fitbit code [{code}] is invalid.",
            status_code=400,
        )


class ThirdPartyAppAlreadyExistsException(DetailedException):
    def __init__(self, app_type):
        super().__init__(
            code=DCTErrorCodes.THIRD_PARTY_APP_ALREADY_EXISTS,
            debug_message=f"Third Party App with type [{app_type}] already exists",
            status_code=400,
        )


class ThirdPartyAppStatusException(DetailedException):
    def __init__(self, app_type, status):
        super().__init__(
            code=ErrorCodes.INVALID_REQUEST,
            debug_message=f"Third Party App with type [{app_type}] is already [{status}]",
            status_code=400,
        )


class ThirdPartyAppDuplicateException(DetailedException):
    def __init__(self, app_type):
        super().__init__(
            code=ErrorCodes.INVALID_REQUEST,
            debug_message=f"Third Party App with type [{app_type}] already exists with the provided app user id ",
            status_code=400,
        )


class FitbitRevokeAccessException(DetailedException):
    def __init__(self, message: Optional[str] = None):
        super().__init__(
            code=ErrorCodes.INVALID_REQUEST,
            debug_message=message or "Failed to disconnect from fitbit",
            status_code=400,
        )


class FitbitNotAllPermissionsGranted(DetailedException):
    def __init__(self, token: str = None):
        super().__init__(
            code=DCTErrorCodes.FITBIT_NOT_ALL_PERMISSIONS_GRANTED,
            debug_message="Not all permissions are granted",
            status_code=400,
        )
        self.token = token


class FitbitTooManyRequests(DetailedException):
    def __init__(self, data: Union[list, dict], module: str, end_date: datetime):
        self.data = data
        self.module = module
        self.end_date = end_date

        super().__init__(
            code=ErrorCodes.INVALID_REQUEST,
            debug_message="Too many requests",
            status_code=400,
        )


class InvalidWithingsCodeException(DetailedException):
    def __init__(self, code):
        super().__init__(
            code=ErrorCodes.INVALID_REQUEST,
            debug_message=f"Withings code [{code}] is invalid.",
            status_code=400,
        )


class WithingsRevokeAccessException(DetailedException):
    def __init__(self, message: Optional[str] = None):
        super().__init__(
            code=ErrorCodes.INVALID_REQUEST,
            debug_message=message or "Failed to disconnect from Withings",
            status_code=400,
        )


class WithingsNotAllPermissionsGranted(DetailedException):
    def __init__(self, token: str = None):
        super().__init__(
            code=DCTErrorCodes.WITHINGS_NOT_ALL_PERMISSIONS_GRANTED,
            debug_message="Not all Withings permissions are granted",
            status_code=400,
        )
        self.token = token


class WithingsTooManyRequests(DetailedException):
    def __init__(self, data: Union[list, dict], module: str, end_date: datetime):
        self.data = data
        self.module = module
        self.end_date = end_date

        super().__init__(
            code=ErrorCodes.INVALID_REQUEST,
            debug_message="Too many requests to Withings API",
            status_code=400,
        )
