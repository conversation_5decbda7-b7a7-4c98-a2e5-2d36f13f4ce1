{"deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2c"}, "name": "Third Party App test deployment", "status": "DRAFT", "color": "0x007AFF", "moduleConfigs": [{"id": {"$oid": "670e683cb564d9554f42d238"}, "moduleId": "HeartRate", "status": "ENABLED", "configBody": {"isHRVEnabled": true, "dataSource": {"manualCollection": true, "fitbitCollection": true, "healthAppCollection": true}}}, {"id": {"$oid": "5fb58d9663d56e2c40724843"}, "moduleId": "Sleep", "status": "ENABLED"}, {"moduleId": "OxygenSaturation", "moduleName": "Oxygen Saturation", "status": "ENABLED", "id": {"$oid": "642c720d5ac8016189e20510"}, "notificationData": {"title": "Reminder", "body": "For Oxygen Saturation"}, "order": 2, "configBody": {"dataSource": {"fitbitCollection": true}}, "about": "", "version": 0, "updateDateTime": {"$date": "2021-04-21T20:23:09.940Z"}, "createDateTime": {"$date": "2021-04-21T20:23:09.940Z"}}, {"id": {"$oid": "5e94b2007773091c9a592650"}, "about": "string", "status": "ENABLED", "configBody": {}, "moduleId": "BloodPressure", "moduleName": "hu_BloodPressure_moduleName", "shortModuleName": "hu_BloodPressure_shortModuleName", "schedule": {"isoDuration": "P1W", "timesPerDuration": 1, "timesOfDay": ["BEFORE_DINNER"], "friendlyText": "hu_BloodPressure_schedule_friendlyText"}}], "keyActionsEnabled": true, "features": {"customAppConfig": {"thirdPartyApps": {"enabled": true, "apps": {"fitbit": true}}}}, "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}}], "huma_auth_user": [{"_id": {"$oid": "5ed803dd5f2f99da73684413"}, "status": 1, "emailVerified": false, "email": "<EMAIL>", "phoneNumber": "+8618621320546", "displayName": "admin", "userAttributes": {"familyName": "hey", "givenName": "test", "dob": "1988-02-20", "gender": "MALE"}, "updateDateTime": {"$date": 1591215069319}, "createDateTime": {"$date": 1591215069319}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999999", "displayName": "test", "userAttributes": {"familyName": "test", "givenName": "test", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}, {"_id": {"$oid": "5e84b0dab8dfa268b1180412"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "createDateTime": {"$date": "2021-05-09T10:00:00.000Z"}}, {"_id": {"$oid": "5e84b0dab8dfa268b1180536"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999998", "displayName": "testUser", "userAttributes": {"familyName": "testUser", "givenName": "testUser", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}, {"_id": {"$oid": "5e84b0dab8dfa268b1180537"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999977", "displayName": "testUser2", "userAttributes": {"familyName": "testUser2", "givenName": "testUser2", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}, {"_id": {"$oid": "5e84b0dab8dfa268b1180538"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999666", "displayName": "testUser3", "userAttributes": {"familyName": "testUser3", "givenName": "testUser3", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}], "user": [{"_id": {"$oid": "5ed803dd5f2f99da73684413"}, "updateDateTime": {"$date": 1591215069321}, "createDateTime": {"$date": 1591215069321}, "givenName": "test", "familyName": "hey", "gender": "MALE", "email": "<EMAIL>", "phoneNumber": "+8618621320546", "roles": [{"roleId": "SuperAdmin", "resource": "deployment/*", "userType": "SuperAdmin", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "updateDateTime": {"$date": 1591215069321}, "createDateTime": {"$date": 1591215069321}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999999999", "roles": [{"roleId": "Admin", "resource": "deployment/5d386cc6ff885918d96edb2c", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "5e84b0dab8dfa268b1180412"}, "createDateTime": {"$date": "2021-05-09T10:00:00.000Z"}, "givenName": "hr_user", "familyName": "hr_user", "email": "<EMAIL>", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "5e84b0dab8dfa268b1180536"}, "updateDateTime": {"$date": 1591215069321}, "createDateTime": {"$date": 1591215069321}, "givenName": "testUser", "familyName": "testUser", "email": "<EMAIL>", "phoneNumber": "+380999999998", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "5e84b0dab8dfa268b1180537"}, "updateDateTime": {"$date": 1591215069321}, "createDateTime": {"$date": 1591215069321}, "givenName": "testUser2", "familyName": "testUser2", "email": "<EMAIL>", "phoneNumber": "+380999999977", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "5e84b0dab8dfa268b1180538"}, "updateDateTime": {"$date": 1591215069321}, "createDateTime": {"$date": 1591215069321}, "givenName": "testUser3", "familyName": "testUser3", "email": "<EMAIL>", "phoneNumber": "+380999999666", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "isActive": true}], "timezone": "UTC"}], "third_party_app": [{"_id": {"$oid": "6356ab7b2326fe02f272ed56"}, "_cls": "MongoThirdPartyAppObject", "type": "WITHINGS", "userId": {"$oid": "5e84b0dab8dfa268b1180536"}, "appUserId": "withings_user_123", "refreshToken": "test_refresh_token", "accessToken": "test_access_token", "status": "CONNECTED", "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}, {"_id": {"$oid": "6356ab7b2326fe02f272ed53"}, "_cls": "MongoThirdPartyAppObject", "type": "FITBIT", "userId": {"$oid": "5e84b0dab8dfa268b1180537"}, "appUserId": "app user id", "refreshToken": "dddd", "accessToken": "access", "permissions": ["sleep"], "status": "CONNECTED", "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}, {"_id": {"$oid": "6356ab7b2326fe02f272ed10"}, "_cls": "MongoThirdPartyAppObject", "type": "FITBIT", "userId": {"$oid": "5e84b0dab8dfa268b1180412"}, "appUserId": "HR User app user id", "refreshToken": "refresh token", "accessToken": "access", "permissions": ["heartrate"], "status": "CONNECTED", "createDateTime": {"$date": "2021-06-09T10:00:00.000Z"}}, {"_id": {"$oid": "6356ab7b2326fe02f272ed55"}, "_cls": "MongoThirdPartyAppObject", "type": "FITBIT", "userId": {"$oid": "5e84b0dab8dfa268b1180538"}, "appUserId": "app user id 3", "refreshToken": "dddd", "accessToken": "access", "permissions": ["sleep"], "status": "DISCONNECTED", "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}], "third_party_app_log": [{"_id": {"$oid": "63d2c426d4a65f93293b62e3"}, "_cls": "MongoThirdPartyAppLogObject", "type": "FITBIT", "userId": {"$oid": "5e84b0dab8dfa268b1180537"}, "moduleLogs": [{"type": "sleep", "lastSyncDateTime": {"$date": {"$numberInt": "1674691200000"}}}], "updateDateTime": {"$date": "2025-01-01T00:00:00.000Z"}, "createDateTime": {"$date": "2025-01-01T00:00:00.000Z"}}], "calendar": [{"_id": {"$oid": "642c75af8f316b3acb43a9b0"}, "updateDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "createDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "startDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "enabled": true, "userId": {"$oid": "5e84b0dab8dfa268b1180536"}, "model": "UserModuleReminder", "_cls": "MongoCalendarEvent", "recurrencePattern": "DTSTART:20200407T212417\nRRULE:FREQ=MONTHLY;BYMONTHDAY=1,3;BYHOUR=10;BYMINUTE=20", "extraFields": {"moduleName": "OxygenSaturation", "durationIso": "PT10H20M", "moduleId": "OxygenSaturation", "moduleConfigId": "642c720d5ac8016189e20510", "specificMonthDays": [1]}}]}