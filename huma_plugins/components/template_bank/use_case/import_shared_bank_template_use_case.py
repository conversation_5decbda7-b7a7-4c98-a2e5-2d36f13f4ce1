from huma_plugins.components.export.file_import_helpers.import_deployment_config_from_file import (
    ImportDeploymentConfigFromFile,
)
from huma_plugins.components.export.use_case.import_deployment_config_use_case import (
    ImportDeploymentConfigUseCase,
)
from huma_plugins.components.template_bank.use_case.base_template_bank_use_case import (
    BaseTemplateBankUseCase,
)
from huma_plugins.components.template_bank.use_case.create_shared_bank_template import (
    SharedTemplateFile,
)
from huma_plugins.components.template_bank.use_case.template_bank_request_objects import (
    TemplateImportDeploymentConfigRequestObject,
)
from sdk.common.adapter.shared_file_storage_adapter import SharedFileStorageAdapter
from sdk.common.exceptions.exceptions import DeploymentTemplateDoesNotExist
from sdk.common.usecase.response_object import ResultIdResponseObject
from sdk.common.utils import inject
from sdk.common.utils.inject import autoparams


class ImportDeploymentConfigFromFileOverridable(ImportDeploymentConfigFromFile):
    @autoparams()
    def __init__(self, deployment_override: dict, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.deployment_override = deployment_override

    def _create_deployment(self, deployment_dict: dict) -> str:
        if self.deployment_override:
            deployment_dict.update(self.deployment_override)
        return super()._create_deployment(deployment_dict=deployment_dict)


class ImportSharedBankTemplateUseCase(ImportDeploymentConfigUseCase, BaseTemplateBankUseCase):
    def process_request(self, request_object: TemplateImportDeploymentConfigRequestObject) -> ResultIdResponseObject:
        data = self._download_template_file(request_object)

        deployment_override = request_object.deploymentOverride
        if deployment_override:
            deployment_override = deployment_override.to_dict()
        deployment_id = ImportDeploymentConfigFromFileOverridable(
            deployment_override=deployment_override,
        ).import_zip_bytes_stream(data)

        self._link_deployments([deployment_id])
        return ResultIdResponseObject(deployment_id)

    def _download_template_file(self, request_object):
        index_file = self.download_index_file().get(SharedTemplateFile.DEPLOYMENT_TEMPLATES, [])
        template = next(
            (template for template in index_file if template["id"] == request_object.templateId),
            None,
        )
        if template is None:
            raise DeploymentTemplateDoesNotExist
        share_file_storage = inject.instance(SharedFileStorageAdapter)
        data, _, _ = share_file_storage.download_file(self.shared_bucket, template[SharedTemplateFile.TEMPLATE_FILE])
        return data
