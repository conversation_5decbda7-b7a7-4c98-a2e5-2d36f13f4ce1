from datetime import datetime, timezone

import jsonschema
from jsonschema.validators import validate, validator_for

from sdk.cms.interfaces import CMSSchemaData, ICMSManager
from sdk.common.adapter.feature_toggle import FeatureToggleAdapter
from sdk.common.utils import inject
from . import QUESTIONNAIRE_COLLECTION_NAME
from ._collection import <PERSON><PERSON><PERSON>oll<PERSON>tion
from ._default import ASSET_COLLECTION_NAME, DEFAULT_COLLECTIONS, EXTRA_COLLECTIONS
from huma_plugins.components.cms.config import FEATURE_FLAG_CMS_STAGE_0


class CMSLibrary(ICMSManager):
    """
    A CMS Library of collections and schemas for them.
    To add or override a collection, use the `register` method.
    The schema file will then be used to validate the content of the collection.
    """

    _registered_schema: dict[str, CMSSchemaData] = {}
    _default_loaded = False

    def __init__(self, load_default: bool = True):
        if load_default:
            self._load_default_collections()
        self._load_extra_collections()

    def register_schema(self, schema: CMSSchemaData, safe: bool = True) -> bool:
        if schema.id in self._registered_schema:
            if safe:
                return False
            raise ValueError(f"Schema with id {schema.id} already registered")
        if not self._is_schema_valid(schema.schema):
            if self:
                return False
            raise ValueError(f"Schema with id {schema.id} is not valid")

        self._registered_schema[schema.id] = schema
        return True

    def register(self, collection: CMSCollection):
        """
        @deprecated: Use register_schema instead
        :param collection:
        :type collection: huma_plugins.components.cms.library._collection.CMSCollection
        :return:
        :rtype: bool | None
        """
        schema = CMSSchemaData(id=collection.id, name=collection.name, schema=collection.schema)
        return self.register_schema(schema=schema, safe=True)

    def get_schema(self, schema_id: str, safe: bool = True) -> CMSSchemaData | None:
        if not safe and schema_id not in self._registered_schema:
            raise ValueError(f"Schema `{schema_id}` is not registered")
        return self._registered_schema.get(schema_id)

    def get(self, schema_id: str) -> CMSCollection | None:
        """
        @deprecated: Use get_schema instead
        :param schema_id:
        :type schema_id: str
        :param safe:
        :type safe: bool
        :return:
        :rtype: CMSCollection|None
        """
        data = self.get_schema(schema_id, safe=True)
        return None if data is None else CMSCollection(id=data.id, name=data.name, schema=data.schema)

    @property
    def collections(self) -> dict[str, CMSSchemaData]:
        excluded_collections = [ASSET_COLLECTION_NAME]
        features: FeatureToggleAdapter = inject.instance(FeatureToggleAdapter)

        if features.is_enabled(FEATURE_FLAG_CMS_STAGE_0):
            excluded_collections.append(QUESTIONNAIRE_COLLECTION_NAME)

        # exclude `assets` collection to not appear in the UI
        return {k: v for k, v in self._registered_schema.items() if k not in excluded_collections}

    def get_all_schemas(self) -> dict[str, CMSSchemaData]:
        return self._registered_schema

    def validate_content(self, content: dict, schema_id: str):
        validate(content, schema=self.get(schema_id).schema)

    @staticmethod
    def _is_schema_valid(schema: dict) -> bool:
        try:
            validator_for(schema).check_schema(schema)
            return True
        except jsonschema.exceptions.SchemaError:
            return False

    def _load_default_collections(self):
        self._default_loaded_at = datetime.now(timezone.utc)
        for collection in DEFAULT_COLLECTIONS:
            self.register_schema(collection, True)

    def _load_extra_collections(self):
        features: FeatureToggleAdapter = inject.instance(FeatureToggleAdapter)
        for collection in EXTRA_COLLECTIONS:
            if collection.id not in self._registered_schema and features.is_enabled(f"hu-cms-{collection.id}"):
                self.register_schema(collection, True)

    def is_collection_registered(self, collection_id: str) -> bool:
        return collection_id in self._registered_schema

    def get_registered_collection_names(self) -> list[str]:
        return list(self._registered_schema.keys())
