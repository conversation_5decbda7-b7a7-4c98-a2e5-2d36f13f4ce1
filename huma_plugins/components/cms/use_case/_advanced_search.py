from functools import cached_property

from huma_plugins.components.cms.dtos import CMS<PERSON>ontentDTO
from huma_plugins.components.cms.repo import CMSRepository
from sdk.authorization.dtos.user import UserDTO
from sdk.authorization.services.authorization import AuthorizationService
from sdk.common.constants import CONTAINS
from sdk.common.localization.utils import Language
from sdk.common.usecase.response_object import ResponseObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.convertible import convertibleclass, default_field
from sdk.common.utils.inject import autoparams
from sdk.deployment.dtos.deployment import DeploymentDTO
from sdk.deployment.dtos.status import Status
from ._requests import AdvancedSearchContentRequestObject, SearchContentRequestObject
from ._search_content import SearchContentUseCase
from .advanced_query.recommendation_engine import RecommendationEngine
from .advanced_query.utils import scope_placeholder


@convertibleclass
class SearchContentResponseObject(ResponseObject):
    items: list[CMSContentDTO] = default_field()


class AdvancedSearchContentUseCase(UseCase):
    request_object: AdvancedSearchContentRequestObject

    @autoparams()
    def __init__(self, repo: CMSRepository):
        self._repo = repo

    def process_request(self, request_object: AdvancedSearchContentRequestObject) -> SearchContentResponseObject:
        content_list = self._retrieve_content()
        variables = self._build_scope()
        # To make advanced query consistent for user we use articles for both collections
        variables["articles"] = content_list
        if self.request_object.collection == "articles":
            variables["old_articles"] = self._retrieve_old_articles()
        engine = RecommendationEngine(variables=variables)
        filtered_content = engine.run(self.request_object.build_query())
        return SearchContentResponseObject(items=filtered_content)

    def _retrieve_content(self) -> list[CMSContentDTO]:
        unpublished = self.request_object.submitter.deployment.status is Status.DRAFT
        req_obj = SearchContentRequestObject(
            collection=self.request_object.collection,
            filters={"resources": {CONTAINS: f"deployment/{self.request_object.submitter.deployment_id()}"}},
            skip=0,
            limit=100,
            unpublished=unpublished,
        )
        content_list: list[CMSContentDTO] = SearchContentUseCase().execute(req_obj).items
        return self._apply_localization(content_list)

    def _apply_localization(self, content_list: list[CMSContentDTO]) -> list[CMSContentDTO]:
        authz_user = self.request_object.submitter
        valid_languages = [
            authz_user.get_language(),
            authz_user.get_deployment_language(),
            Language.EN,
        ]
        for content in content_list:
            valid_localizations = [
                localizable_data
                for localizable_data in content.data.get("localizable", [])
                if localizable_data.get("language") in valid_languages
            ]
            if valid_localizations:
                for language in valid_languages:
                    for localized_article in valid_localizations:
                        if language == localized_article["language"]:
                            content.data = localized_article
                            break
            else:
                content.data = content.data.get("localizable", [None])[0]
        return [content for content in content_list if content.data]

    def _build_scope(self) -> dict:
        """A list of variables that can be used in the query"""
        variables = scope_placeholder()
        user_data = {"createDateTime": self._user.createDateTime}
        if self._user.dateOfBirth:
            user_data["age"] = self._user.get_age()
        if self._user.surgeryDateTime:
            user_data["surgeryDate"] = self._user.surgeryDateTime

        variables["user"].update(user_data)

        primitives = (self._user.recentModuleResults or {}).values()
        for primitive in primitives:
            variables["data"].update(primitive[0])

        return variables

    @cached_property
    def _user(self) -> UserDTO:
        """Retrieve user profiles with recentModuleResults for query context"""
        service = AuthorizationService()
        return service.retrieve_user_profile(self.request_object.submitter.id)

    def _retrieve_old_articles(self):
        deployment = self._deployment
        learn = deployment.learn
        if not learn or not learn.sections:
            return []

        for article in learn.articles:
            article.content.isValid = True
        deployment.preprocess_for_configuration()
        return learn.articles

    @cached_property
    def _deployment(self) -> DeploymentDTO:
        deployment = self.request_object.submitter.deployment
        deployment.add_learn_article_read_status(self.request_object.submitter)
        return deployment
