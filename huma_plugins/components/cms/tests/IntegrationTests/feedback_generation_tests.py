from pathlib import Path
from unittest.mock import patch

from freezegun import freeze_time

from huma_plugins.components.cms.component import HeadlessCMSComponent
from huma_plugins.components.cms.tests.IntegrationTests._base import (
    DEPLOYMENT_ID,
    BaseCMSRouterTestCase,
)
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.common.utils import inject
from sdk.deployment.component import DeploymentComponent
from sdk.deployment.models.deployment import Deployment
from sdk.module_result.component import ModuleResultComponent
from sdk.module_result.dtos.module_feedback import ModuleFeedback, FeedbackText
from sdk.module_result.modules import SleepModule
from sdk.module_result.modules.sleep._feedback_engine import (
    INTERNAL_SLEEP_FEEDBACK_FEATURE,
)
from sdk.module_result.modules.sleep._primitive import (
    SleepChangeDirection,
    SleepFeedbackData,
)
from sdk.module_result.modules.sleep.models import Sleep
from sdk.module_result.router.module_result_requests import ModuleResultRequestObject
from sdk.module_result.use_cases.create_module_result_use_case import (
    CreateModuleResultUseCase,
)
from sdk.versioning.component import VersionComponent

NOTIFICATION_PATH = "sdk.module_result.modules.sleep._feedback_engine.prepare_and_send_push_notification"

USER_ID = "5e8f0c74b50aa9656c34789b"
SLEEP_MODULE_CONFIG_ID = "644bdea1dfb15c2fdcfd50bc"


def sample_sleep_primitive(awake_count=10, start_time: str = None, end_time: str = None):
    return {
        "userId": USER_ID,
        "deviceName": "Fitbit",
        "moduleId": "Sleep",
        "moduleConfigId": SLEEP_MODULE_CONFIG_ID,
        "deploymentId": DEPLOYMENT_ID,
        "isAggregated": False,
        "awakeCount": awake_count,
        "duration": 30000,
        "dateOfSleep": "2024-03-12",
        "uwake": 0,
        "startDateTime": start_time or "2024-03-12T22:00:00Z",
        "endDateTime": end_time or "2024-03-13T06:00:00Z",
        "type": "Sleep",
    }


def sample_request(auth_user, count, primitives: list = None) -> dict:
    return {
        "rawData": primitives or [sample_sleep_primitive(count)],
        "moduleId": "Sleep",
        "moduleConfigId": SLEEP_MODULE_CONFIG_ID,
        "user": auth_user.user,
        "deployment": auth_user.deployment,
        "submitter": auth_user,
    }


class CMSFeedbackRetrieverTestCase(BaseCMSRouterTestCase):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        ModuleResultComponent(additional_modules=[SleepModule()]),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
        HeadlessCMSComponent(),
    ]
    fixtures = [
        Path(__file__).parent.joinpath("fixtures/cms_dump.json"),
        Path(__file__).parent.joinpath("fixtures/module_results_dump.json"),
    ]
    override_config = {
        "server.adapters.inMemoryFeatureToggle.keys": {
            "hu-cms-recommendations": True,
            INTERNAL_SLEEP_FEEDBACK_FEATURE: True,
        },
    }

    def setUp(self):
        super().setUp()
        repo = inject.instance(AuthorizationRepository)
        user = repo.retrieve_simple_user_profile(user_id=USER_ID)
        self.user = AuthorizedUser(user, deployment_id=DEPLOYMENT_ID)

    @patch(NOTIFICATION_PATH)
    @freeze_time("2024-03-14")
    def test_feedback_added_for_new_sleep_submission_increased_value(self, send_push):
        req_data = sample_request(self.user, count=100)
        req_obj = ModuleResultRequestObject.from_dict(req_data)
        use_case = CreateModuleResultUseCase()

        rsp = use_case.execute(req_obj)

        primitive_id = rsp.ids[0]
        feedback = self._retrieve_feedback(primitive_id)
        self.assertIsNotNone(feedback)
        self.assertEqual("First recommendation", feedback[ModuleFeedback.TEXT][FeedbackText.TITLE])
        self.assertEqual("John", feedback[ModuleFeedback.TEXT][FeedbackText.REVIEWER_NAME])

        module_data = feedback[ModuleFeedback.MODULE_DATA]
        self.assertEqual(SleepChangeDirection.INCREASED.value, module_data["direction"])
        self.assertEqual(0, module_data["daysUntilRecommendationReady"])
        self.assertIsNotNone(module_data["timeStamp"])
        send_push.assert_not_called()

    @patch(NOTIFICATION_PATH)
    @freeze_time("2024-03-14")
    def test_feedback_added_for_new_sleep_submission_decreased_value(self, send_push):
        req_data = sample_request(self.user, count=1)
        req_obj = ModuleResultRequestObject.from_dict(req_data)
        use_case = CreateModuleResultUseCase()

        rsp = use_case.execute(req_obj)

        primitive_id = rsp.ids[0]
        feedback = self._retrieve_feedback(primitive_id)
        module_data = feedback[ModuleFeedback.MODULE_DATA]
        self.assertEqual("Second recommendation", feedback[ModuleFeedback.TEXT][FeedbackText.TITLE])
        self.assertEqual("James", feedback[ModuleFeedback.TEXT][FeedbackText.REVIEWER_NAME])

        self.assertIsNotNone(feedback)
        self.assertEqual(SleepChangeDirection.DECREASED.value, module_data["direction"])
        self.assertEqual(0, module_data["daysUntilRecommendationReady"])
        self.assertIsNotNone(module_data["timeStamp"])
        # Default Notification
        expected_template = {
            "title": "You slept less than usual",
            "body": "You slept less than usual. This can affect your health and well-being.",
        }
        data = {
            "moduleId": "Sleep",
            "moduleConfigId": SLEEP_MODULE_CONFIG_ID,
        }
        send_push.assert_called_once_with(
            self.user.user.id,
            "RECOMMENDATION",
            expected_template,
            data,
            run_async=True,
        )

    def test_feedback_when_two_submissions_added_in_one_day(self):
        self._clear_sleep_submissions(self.user.user.id)

        req_data = sample_request(self.user, 1)
        self._create_sleep_and_assert(req_data)

        primitive = sample_sleep_primitive(awake_count=1)
        primitive["startDateTime"] = "2024-03-13T14:00:00Z"
        primitive["endDateTime"] = "2024-03-13T15:00:00Z"

        req_data = sample_request(self.user, 1, [primitive])
        self._create_sleep_and_assert(req_data)

    def test_feedback_is_not_processed_without_config(self):
        self._clear_feedback_config()
        req_data = sample_request(self.user, count=1)
        req_obj = ModuleResultRequestObject.from_dict(req_data)
        use_case = CreateModuleResultUseCase()

        rsp = use_case.execute(req_obj)

        primitive_id = rsp.ids[0]
        feedback = self._retrieve_feedback(primitive_id)
        self.assertIsNone(feedback)

    @patch(NOTIFICATION_PATH)
    @freeze_time("2024-03-16")
    def test_feedbacks_properly_created_for_batch_sleep_data(self, send_push):
        # 1 primitive exist in the database in the last 7 days
        primitive_1 = sample_sleep_primitive(
            awake_count=5,
            start_time="2024-03-12T22:00:00Z",
            end_time="2024-03-13T06:00:00Z",
        )
        primitive_2 = sample_sleep_primitive(
            awake_count=5,
            start_time="2024-03-14T22:00:00Z",
            end_time="2024-03-15T06:00:00Z",
        )

        req_data = sample_request(self.user, 1, [primitive_1, primitive_2])
        req_obj = ModuleResultRequestObject.from_dict(req_data)
        use_case = CreateModuleResultUseCase()

        rsp = use_case.execute(req_obj)

        primitive_id_1, primitive_id_2 = rsp.ids[0], rsp.ids[1]
        feedback_1 = self._retrieve_feedback(primitive_id_1)
        feedback_2 = self._retrieve_feedback(primitive_id_2)

        self.assertIsNone(feedback_1)
        self.assertEqual(2, feedback_2["notificationData"]["order"])
        send_push.assert_called()

    def _create_sleep_and_assert(self, req_data: dict):
        req_obj = ModuleResultRequestObject.from_dict(req_data)
        use_case = CreateModuleResultUseCase()

        rsp = use_case.execute(req_obj)

        primitive_id_1 = rsp.ids[0]
        feedback = self._retrieve_feedback(primitive_id_1)
        module_data = feedback[ModuleFeedback.MODULE_DATA]
        self.assertEqual(2, module_data[SleepFeedbackData.DAYS_UNTIL_FEEDBACK])

    def _submit_sleep_data(self, user_id, count):
        req_data = sample_request(user_id, count)
        req_obj = ModuleResultRequestObject.from_dict(req_data)
        use_case = CreateModuleResultUseCase()

        return use_case.execute(req_obj)

    @staticmethod
    def _retrieve_feedback(primitive_id):
        return Sleep.objects.filter(mongoId=primitive_id).values("feedback").first()["feedback"]

    @staticmethod
    def _clear_sleep_submissions(user_id):
        Sleep.objects.filter(userId=user_id).delete()

    @staticmethod
    def _clear_feedback_config():
        deployment = Deployment.objects.filter(mongoId=DEPLOYMENT_ID).first()
        for module_config in deployment.moduleConfigs:
            if module_config["moduleId"] == "Sleep":
                module_config["feedbackTexts"] = None
                break
        deployment.save()
