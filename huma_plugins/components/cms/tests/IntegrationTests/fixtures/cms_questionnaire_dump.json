{"resources": ["deployment/63bfdc3e1e863fdf04752581"], "status": "DRAFT", "schema": "questionnaires", "data": {"title": "Test Questionnaire", "language": "en", "isHorizontalFlow": true, "trademarkText": "Trademark Text", "publisherName": "Test Publisher", "buttons": [{"type": "NEXT", "text": "Next"}, {"type": "SUBMIT", "text": "Submit"}], "pages": [{"order": 1, "title": "The Welcome Page", "description": "Welcome to test Pain questioner page", "type": "INFO", "buttons": [{"type": "NEXT", "text": "Next Page"}]}, {"order": 2, "id": "4fa85f64-5717-4562-b3fc-2c963f66afa6", "title": "Test Questionnaire 2nd Page", "description": "2nd Page of Questionnaire", "type": "QUESTION", "items": [{"order": 1, "format": "TEXTCHOICE", "text": "Frequency of Pain", "description": "We need to know the frequencies of pain", "options": [{"label": "Never", "value": "never"}, {"label": "Occasionally", "value": "occasionally"}, {"label": "Frequently", "value": "frequently"}, {"label": "Always", "value": "always"}], "selectionCriteria": "SINGLE", "required": true}, {"order": 2, "format": "TEXTCHOICE", "text": "Time of pain", "description": "We need to know when you feel the pain", "required": true, "options": [{"label": "Morning", "value": "morning"}, {"label": "<PERSON>on", "value": "noon"}, {"label": "Afternoon", "value": "afternoon"}, {"label": "Evening", "value": "evening"}, {"label": "Night", "value": "night"}, {"label": "Midnight", "value": "midnight"}], "selectionCriteria": "MULTIPLE"}]}], "submissionPage": {"order": 3, "type": "SUBMISSION", "title": "Test Submission", "description": "Thank you for the Test Submission", "buttonText": "Finish", "id": "4fa85f64-5717-4562-b3fc-2c963f66afa7"}}}