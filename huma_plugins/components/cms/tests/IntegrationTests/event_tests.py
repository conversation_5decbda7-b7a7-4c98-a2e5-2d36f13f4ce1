import logging
import unittest
from pathlib import Path
from unittest.mock import MagicMock, patch

from flask import url_for

from huma_plugins.components.cms.dtos.cms_learn_article import CMSLearnArticle
from huma_plugins.components.cms.tests.IntegrationTests._base import (
    BaseCMSRouterTestCase,
    NOT_EXISTS_REFERENCE_CONFIG_IN_CMS_USER_ID,
    REFER_TO_BAD_CONFIG_IN_CMS_USER_ID,
    USER,
)
from sdk.cms.events.delete_content_event import PostDeleteCMSContentEvent
from sdk.common.utils.common_functions_utils import find
from sdk.deployment.callbacks import on_cms_delete_remove_references
from sdk.inbox.models import MessageAttachment
from sdk.module_result.dtos.module_config import ModuleConfig

_FIXTURES_DIR = Path(__file__).parent.joinpath("fixtures")


CONTENT_ID = "5e8f0c74b50aa9656c34132c"
KEY_ACTION_WITH_CONTENT = "5f078582c565202bd6cb03af"
STORAGE_USE_CASE_PATH = "huma_plugins.components.cms.adapters.cms_converter.GetSignedUrlUseCaseV1.execute"

cms_repo_mocked = MagicMock()
deployment_repo_mocked = MagicMock()


class CMSEventsTestCase(BaseCMSRouterTestCase):
    _questionnaire_in_deployment_id = "677bccaa0bf0350bfba41545"

    def test_event_on_load_deployment_extend_questionnaire_definition(self):
        url = url_for("user_v1.retrieve_full_configuration_for_user", user_id=USER)

        rsp = self.flask_client.get(url, headers=self._user_headers)
        self.assertIsNotNone(rsp.json)
        self.assertEqual(rsp.status_code, 200)
        config_bodies = rsp.json.get("deployments")[0].get("moduleConfigs")
        questionnaire_config_bodies = [config for config in config_bodies if config.get("moduleId") == "Questionnaire"]

        self.assertIsInstance(questionnaire_config_bodies, list)
        target_config = [c for c in questionnaire_config_bodies if c.get("id") == "616e0156557daa4741a85d14"]
        self.assertEqual(len(target_config), 1)
        target_config = target_config.pop()
        self.assertIn("pages", target_config.get("configBody", {}))
        pages = target_config.get("configBody", {})["pages"]
        self.assertIsInstance(pages, list)
        self.assertGreater(len(pages), 1)

    @patch(STORAGE_USE_CASE_PATH)
    def test_cms_article_injected_in_key_action(self, storage_use_case_mock):
        storage_use_case_mock.return_value = MagicMock(url="https://storage.com")
        url = url_for("user_v1.retrieve_full_configuration_for_user", user_id=USER)
        rsp = self.flask_client.get(url, headers=self._user_headers)
        self.assertIsNotNone(rsp.json)
        self.assertEqual(rsp.status_code, 200)
        key_actions = rsp.json.get("deployments")[0].get("keyActions")
        self.assertEqual(len(key_actions), 2)
        key_action_with_valid_article, key_action_with_invalid_article = key_actions
        self.assertEqual(key_action_with_valid_article["learnArticleId"], "5e8f0c74b50aa9656c34132c")
        self.assertEqual(
            key_action_with_valid_article["learnArticle"]["id"], "5e8f0c74b50aa9656c34132c"
        )  # ket action returns article
        self.assertEqual(key_action_with_invalid_article["learnArticleId"], "5f1824ba504787d8d89ebecb")
        self.assertEqual(
            key_action_with_invalid_article["learnArticle"], {}
        )  # key action is returned despite no article being found

    @patch(STORAGE_USE_CASE_PATH)
    def test_cms_articles_injected_in_module_config(self, storage_use_case_mock):
        storage_use_case_mock.return_value = MagicMock(url="https://storage.com")
        url = url_for("user_v1.retrieve_full_configuration_for_user", user_id=USER)
        rsp = self.flask_client.get(url, headers=self._user_headers)
        self.assertIsNotNone(rsp.json)
        self.assertEqual(rsp.status_code, 200)
        configs = rsp.json.get("deployments")[0].get("moduleConfigs")
        blood_pressure_config = find(lambda c: c.get("moduleId") == "BloodPressure", configs)
        article_ids = blood_pressure_config[ModuleConfig.LEARN_ARTICLE_IDS]
        articles = blood_pressure_config[ModuleConfig.LEARN_ARTICLES]
        self.assertEqual(2, len(article_ids))  # 2 articles are referenced in the config
        self.assertEqual(1, len(articles))  # only 1 article is a valid CMS object
        self.assertEqual("5e8f0c74b50aa9656c34132c", articles[0].get("id"))
        self.assertIn(CMSLearnArticle.TITLE, articles[0])
        self.assertIn(CMSLearnArticle.THUMBNAIL_IMAGE_URL, articles[0])

    def test_cleanup_deleted_articles_from_deployment(self):
        event = PostDeleteCMSContentEvent(resources=["deployment/63bfdc3e1e863fdf04752581"], content_id=CONTENT_ID)
        on_cms_delete_remove_references(event)

        url = url_for("user_v1.retrieve_full_configuration_for_user", user_id=USER)
        rsp = self.flask_client.get(url, headers=self._user_headers)

        module_configs = rsp.json.get("deployments")[0].get("moduleConfigs")
        blood_pressure = find(lambda x: x.get("moduleId") == "BloodPressure", module_configs)
        self.assertNotIn(CONTENT_ID, blood_pressure["learnArticleIds"])
        k_actions = rsp.json.get("deployments")[0].get("keyActions")
        ka = find(lambda x: x.get("id") == KEY_ACTION_WITH_CONTENT, k_actions)
        self.assertIsNone(ka)

    def test_deleted_cms_content_updated_in_inbox(self):
        attachments_with_content = MessageAttachment.objects.filter(
            resource=CONTENT_ID, isResourceDeleted=False
        ).count()

        url = url_for("cms.delete_content", collection="articles", content_id=CONTENT_ID)
        rsp = self.flask_client.delete(url, headers=self._admin_headers)
        self.assertEqual(rsp.status_code, 204)

        deleted_count_after = MessageAttachment.objects.filter(resource=CONTENT_ID, isResourceDeleted=True).count()
        self.assertEqual(deleted_count_after, attachments_with_content)

    def test_event_on_load_deployment_questionnaire_definition(self):
        # ToDo:  Fetch specific module config? for increase the coverage?
        url = url_for("user_v1.retrieve_full_configuration_for_user", user_id=USER)
        rsp = self.flask_client.get(url, headers=self._user_headers)
        self.assertEqual(rsp.status_code, 200)
        self.assertIsNotNone(rsp.json)
        config_bodies = rsp.json.get("deployments")[0].get("moduleConfigs")
        self.assertIsInstance(config_bodies, list)
        target_configs = [
            ModuleConfig(**config)
            for config in config_bodies
            if config.get("id") == self._questionnaire_in_deployment_id
        ]
        self.assertEqual(len(target_configs), 1)
        target_config = target_configs.pop()
        self.assertEqual(target_config.id, self._questionnaire_in_deployment_id)
        pages = target_config.configBody.get("pages", None)
        self.assertIsInstance(pages, list)
        self.assertEqual(len(pages), 1)
        self.assertEqual(pages[0].get("id"), "7023c90f-bdba-45c6-bb93-831a075584ce")

    # ToDo: add test for bad reference for CMS Content ID that needs raise Exception
    def test_raise_exception_for_bad_reference_to_cms_content(self):
        url = url_for(
            "user_v1.retrieve_full_configuration_for_user",
            user_id=NOT_EXISTS_REFERENCE_CONFIG_IN_CMS_USER_ID,
        )
        rsp = self.flask_client.get(url, headers=self._not_exists_ref_in_cms_user_header)
        logging.debug(rsp.json)
        self.assertNotEqual(201, rsp.status_code)
        # self.assertEqual(rsp.status_code, 404, (rsp.status_code, rsp.json, rsp.headers))
        # self.assertEqual(rsp.json.get('code'), ErrorCodes.CMS_CONTENT_NOT_FOUND)

    # ToDo: add test for other deployment services tests to make sure they load deployment and moduleConfigs correctly
    def test_raise_exception_for_bad_config_in_referenced_cms_content(self):
        url = url_for(
            "user_v1.retrieve_full_configuration_for_user",
            user_id=REFER_TO_BAD_CONFIG_IN_CMS_USER_ID,
        )
        rsp = self.flask_client.get(url, headers=self._bad_config_in_cms_user_header)
        logging.debug(rsp.json)
        self.assertNotEqual(201, rsp.status_code)
        # self.assertEqual(404,rsp.status_code, (rsp.status_code, rsp.json, rsp.headers, g.user.id))
        # self.assertEqual(ErrorCodes.CMS_CONTENT_NOT_FOUND,rsp.json.get('code'), (ErrorCodes.CMS_CONTENT_NOT_FOUND, rsp.status_code, rsp.json))

    # ToDo: update the test to use endpoint for this test
    # @autoparams("event_bus")
    # def test_event_on_load_deployment_failed_extend_questionnaire_definition(
    #     self, event_bus: EventBusAdapter
    # ):
    #     event = RetrieveCMSQuestionnaireEvent()
    #     questionnaire_config_bodies: list[dict] = [
    #         item for item in event_bus.emit(event) or [] if item is not None
    #     ]
    #     self.assertIsInstance(questionnaire_config_bodies, list)
    #     self.assertEqual(len(questionnaire_config_bodies), 0)
    #


if __name__ == "__main__":
    unittest.main()
