import unittest
from datetime import datetime
from functools import cached_property
from unittest.mock import <PERSON><PERSON><PERSON>, Mock

from huma_plugins.components.cms.library import CMSLibrary
from huma_plugins.components.cms.dtos import CMSContentDTO, Status
from huma_plugins.components.cms.repo import CMSRepository
from huma_plugins.components.cms.use_case import (
    AdvancedSearchContentRequestObject,
    AdvancedSearchContentUseCase,
)
from huma_plugins.components.cms.use_case.advanced_query.queries import (
    library,
    CMSContentOrLA,
)
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.dtos.user import UserDTO
from sdk.common.adapter.feature_toggle import FeatureToggleAdapter
from sdk.common.utils import inject
from sdk.organization.repository.organization_repository import OrganizationRepository


class AdvancedSearchContentUseCasePatched(AdvancedSearchContentUseCase):
    @cached_property
    def _user(self) -> UserDTO:
        return _user_sample()

    def _retrieve_content(self):
        return [
            CMSContentDTO(
                id="5f7e6b3b7b7d6d0001a0f1a4",
                status=Status.PUBLISHED,
                schema="articles",
                data={
                    "title": "Test Article",
                    "content": "Best day of my life",
                    "readTime": 10,
                    "articleType": "Markdown",
                    "thumbnail": "5f7e6b3b7b7d6d0001a0f1a4",
                },
                tags=["tag1"],
                updateDateTime=datetime(2024, 1, 1),
                createDateTime=datetime(2024, 1, 1),
            ),
            CMSContentDTO(
                id="5f7e6b3b7b7d6d0001a0f1a5",
                status=Status.PUBLISHED,
                schema="articles",
                data={
                    "title": "Test Article 2",
                    "content": "Normal day of my life",
                    "readTime": 20,
                    "articleType": "Markdown",
                    "thumbnail": "5f7e6b3b7b7d6d0001a0f1a5",
                },
                tags=["tag1", "tag2"],
                updateDateTime=datetime(2024, 1, 2),
                createDateTime=datetime(2024, 1, 2),
            ),
            CMSContentDTO(
                id="5f7e6b3b7b7d6d0001a0f1a6",
                status=Status.PUBLISHED,
                schema="articles",
                data={
                    "title": "Test Article 3",
                    "content": "Worst day of my life",
                    "readTime": 30,
                    "articleType": "Markdown",
                    "thumbnail": "5f7e6b3b7b7d6d0001a0f1a6",
                },
                tags=[],
                updateDateTime=datetime(2024, 1, 3),
                createDateTime=datetime(2024, 1, 3),
            ),
        ]


class TestAdvancedSearchContentUseCase(unittest.TestCase):
    def setUp(self):
        def bind(binder):
            binder.bind_to_constructor(CMSLibrary, lambda: CMSLibrary(load_default=True))
            binder.bind(OrganizationRepository, MagicMock(spec=OrganizationRepository))
            binder.bind(FeatureToggleAdapter, MagicMock(spec=FeatureToggleAdapter))
            binder.bind(CMSRepository, MagicMock(spec=CMSRepository))

        inject.clear_and_configure(bind)
        self.mock_request = request_sample()
        self.use_case = AdvancedSearchContentUseCasePatched()

    def tearDown(self):
        inject.clear()
        return super().tearDown()

    def test_process_request__query_all(self):
        self.mock_request.query = "limit(all(articles), 5)"
        result = self.use_case.execute(self.mock_request).items
        self.assertEqual(3, len(result))
        self.assertEqual("5f7e6b3b7b7d6d0001a0f1a4", result[0].id)
        self.assertEqual("5f7e6b3b7b7d6d0001a0f1a5", result[1].id)
        self.assertEqual("5f7e6b3b7b7d6d0001a0f1a6", result[2].id)

    def test_process_request__query_unread(self):
        self.mock_request.query = "limit(unread(articles), 2)"
        result = self.use_case.execute(self.mock_request).items
        self.assertEqual(2, len(result))

    def test_process_request__query_random(self):
        self.mock_request.query = "random(articles, 1)"
        result = self.use_case.execute(self.mock_request).items
        self.assertEqual(1, len(result))

    def test_process_request__custom_func(self):
        def custom_filter(articles: list[CMSContentOrLA]) -> list[CMSContentOrLA]:
            return [a for a in articles if len(a.data["title"]) > 12]

        library.register(custom_filter)

        self.mock_request.query = "custom_filter(articles)"
        result = self.use_case.execute(self.mock_request).items
        self.assertEqual(2, len(result))

    def test_process_request__variables(self):
        query = "limit(all(articles), 1 if data.HeartRate.value >= 80 else 2)"
        self.mock_request.query = query
        result = self.use_case.execute(self.mock_request).items
        self.assertEqual(2, len(result))

    def test_process_request__tag(self):
        query = "filter(articles, tags='tag1')"
        self.mock_request.query = query
        result = self.use_case.execute(self.mock_request).items
        self.assertEqual(2, len(result))

    def test_process_request__multiple_tags(self):
        query = "filter(articles, tags='tag1,tag2')"
        self.mock_request.query = query
        result = self.use_case.execute(self.mock_request).items
        self.assertEqual(2, len(result))

    def test_process_request__no_tags_match(self):
        query = "filter(articles, tags='tag3')"
        self.mock_request.query = query
        result = self.use_case.execute(self.mock_request).items
        self.assertEqual(0, len(result))


def request_sample():
    submitter_mock = Mock(spec=AuthorizedUser)
    submitter_mock.deployment.get_localization.return_value = {}
    submitter_mock.deployment.learn.articles = []
    return AdvancedSearchContentRequestObject(
        collection="articles",
        query="all(articles)",
        submitter=submitter_mock,
    )


def _user_sample():
    return UserDTO(
        dateOfBirth=datetime(1990, 1, 1),
        gender=UserDTO.Gender.MALE,
        biologicalSex=UserDTO.BiologicalSex.MALE,
        recentModuleResults={"5f7e6b3b7b7d6d0001a0f1a8": [{"HeartRate": {"value": 70}}]},
    )
