import unittest
from unittest.mock import patch, Mock

from huma_plugins.components.cms.adapters.cms_converter import CMSArticleConverter
from huma_plugins.components.cms.dtos import CMSContentDTO
from huma_plugins.components.cms.dtos.cms_learn_article import CMSArticleContentType
from huma_plugins.components.cms.utils.utils import apply_cms_content_localization
from sdk.common.exceptions.exceptions import BucketFileDoesNotExist
from sdk.common.utils.convertible import ConvertibleClassValidationError


class CMSArticleConverterTests(unittest.TestCase):
    def setUp(self):
        self.converter = CMSArticleConverter()
        self.signed_url_use_case = patch(
            "huma_plugins.components.cms.adapters.cms_converter.GetSignedUrlUseCaseV1"
        ).start()

    def tearDown(self):
        patch.stopall()

    def test_convert_with_empty_article_content(self):
        cms_content = CMSContentDTO(data={})
        result = self.converter.convert(cms_content)
        self.assertIsNone(result)

    def test_convert_external_url_article(self):
        cms_content = CMSContentDTO(
            data={
                "articleType": CMSArticleContentType.EXTERNAL_URL.value,
                "externalURL": "https://example.com",
            }
        )
        result = self.converter.convert(cms_content)
        self.assertTrue(result.content.isValid)
        self.assertEqual(result.content.url, "https://example.com")

    def test_convert_invalid_external_url_article(self):
        cms_content = CMSContentDTO(data={"articleType": CMSArticleContentType.EXTERNAL_URL.value})
        result = self.converter.convert(cms_content)
        self.assertIsNone(result)

    def test_convert_markdown_article(self):
        cms_content = CMSContentDTO(
            data={
                "articleType": CMSArticleContentType.MARKDOWN.value,
                "content": "Best day of my life",
            }
        )
        result = self.converter.convert(cms_content)
        self.assertTrue(result.content.isValid)
        self.assertIsNotNone(result.content.body)

    def test_convert_invalid_markdown_article(self):
        cms_content = CMSContentDTO(
            data={
                "articleType": CMSArticleContentType.MARKDOWN.value,
            }
        )
        result = self.converter.convert(cms_content)
        self.assertIsNone(result)

    def test_convert_media_article(self):
        cms_content = CMSContentDTO(
            data={
                "articleType": CMSArticleContentType.MEDIA.value,
                "media": "https://example.com/video.mp4",
            }
        )
        result = self.converter.convert(cms_content)
        self.assertTrue(result.content.isValid)
        self.assertEqual(result.content.videoUrl, "https://example.com/video.mp4")

    def test_convert_invalid_media_article(self):
        cms_content = CMSContentDTO(
            data={
                "articleType": CMSArticleContentType.MEDIA.value,
            }
        )
        result = self.converter.convert(cms_content)
        self.assertIsNone(result)

    def test_unsupported_article_type(self):
        cms_content = CMSContentDTO(data={"articleType": "unsupported"})
        with self.assertRaises(ConvertibleClassValidationError):
            self.converter.convert(cms_content)

    def test_content_image_assets_populated(self):
        self.signed_url_use_case().execute.return_value = Mock(url="https://example.com/image.png")
        cms_content = CMSContentDTO(
            data={
                "articleType": CMSArticleContentType.MARKDOWN.value,
                "content": "Best day of my life",
                "image": "5f7e6b3b7b7d6d0001a0f1a4",
            }
        )
        result = self.converter.convert(cms_content)
        self.assertTrue(result.content.isValid)
        self.assertEqual("https://example.com/image.png", result.content.cmsThumbnail)

    def test_article_image_assets_populated(self):
        self.signed_url_use_case().execute.return_value = Mock(url="https://example.com/image.png")
        cms_content = CMSContentDTO(
            data={
                "articleType": CMSArticleContentType.MARKDOWN.value,
                "content": "Best day of my life",
                "thumbnail": "5f7e6b3b7b7d6d0001a0f1a4",
            }
        )
        result = self.converter.convert(cms_content)
        self.assertTrue(result.content.isValid)
        self.assertEqual("https://example.com/image.png", result.thumbnailImageUrl)

    def test_content_video_assets_populated(self):
        self.signed_url_use_case().execute.return_value = Mock(url="https://example.com/video.mp4")
        cms_content = CMSContentDTO(
            data={
                "articleType": CMSArticleContentType.MEDIA.value,
                "media": "5f7e6b3b7b7d6d0001a0f1a4",
            }
        )
        result = self.converter.convert(cms_content)
        self.assertTrue(result.content.isValid)
        self.assertEqual("https://example.com/video.mp4", result.content.videoUrl)

    def test_asset_not_found(self):
        self.signed_url_use_case().execute.side_effect = BucketFileDoesNotExist("Asset not found")
        cms_content = CMSContentDTO(
            data={
                "articleType": CMSArticleContentType.MEDIA.value,
                "media": "5f7e6b3b7b7d6d0001a0f1a4",
            }
        )
        result = self.converter.convert(cms_content)
        self.assertIsNone(result)

    def test_content_translated(self):
        valid_languages = ("en", "de")
        data = self._sample_cms_data()
        localized = apply_cms_content_localization(data, valid_languages)

        self.assertEqual(localized, data["localizable"][0])

    def test_empty_translations(self):
        valid_languages = ("en", "de")
        data = {"localizable": []}
        localized = apply_cms_content_localization(data, valid_languages)

        self.assertEqual(localized, {})

    def test_content_translated_other_languages(self):
        valid_languages = ("ua", "es")
        data = self._sample_cms_data()
        localized = apply_cms_content_localization(data, valid_languages)

        self.assertEqual(localized, data["localizable"][0])

    def test_content_translated_no_valid_languages(self):
        valid_languages = ()
        data = self._sample_cms_data()
        localized = apply_cms_content_localization(data, valid_languages)

        self.assertEqual(localized, data["localizable"][0])

    def test_preferred_language_order(self):
        valid_languages = ("de", "en")
        data = self._sample_cms_data()
        localized = apply_cms_content_localization(data, valid_languages)

        self.assertEqual(localized, data["localizable"][1])

    def test_unpublished_article_is_preferred(self):
        valid_languages = ("de", "en")
        data = self._sample_cms_data()
        preferred = {
            "articleType": CMSArticleContentType.MARKDOWN.value,
            "title": "Draft",
            "content": "Draft",
            "language": "en",
        }
        cms_content = CMSContentDTO(data=data, draftData={"localizable": [preferred]})
        result = self.converter.convert(cms_content, force_draft=True, languages=valid_languages)

        self.assertIsNotNone(result)
        self.assertEqual("Draft", result.title)

    @staticmethod
    def _sample_cms_data():
        return {
            "localizable": [
                {"title": "Title", "language": "en"},
                {"title": "Titel", "language": "de"},
                {"title": "Titre", "language": "fr"},
            ]
        }
