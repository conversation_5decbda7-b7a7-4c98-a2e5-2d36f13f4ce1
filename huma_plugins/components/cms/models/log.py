from datetime import datetime
from django.db import models


class CMSChangeLog(models.Model):
    class Meta:
        db_table = "cms_change_log"
        app_label = "cms"

    id = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True)
    contentId = models.CharField(max_length=24, null=False, blank=False)
    authorId = models.CharField(max_length=24, null=True, blank=True)
    snapshot = models.JSONField(null=True, blank=True)
    version = models.IntegerField(null=False, blank=False)
    change = models.JSONField(null=True, blank=True)
    createDateTime = models.DateTimeField(default=datetime.utcnow)
