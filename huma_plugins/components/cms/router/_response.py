from huma_plugins.components.cms.dtos import CMSContentDTO
from huma_plugins.components.cms.library import CMSCollection
from sdk.cms.interfaces import CMSSchemaData
from sdk.common.usecase.response_object import Response
from sdk.common.utils.convertible import convertibleclass, default_field, required_field
from sdk.organization.router.organization_responses import PaginatedResponseObject


@convertibleclass
class RetrieveSchemaResponse(Response):
    schema: dict = required_field()


@convertibleclass
class ListCollectionsResponse(Response):
    collections: list[CMSCollection | CMSSchemaData] = required_field()


@convertibleclass
class SearchContentResponseObject(PaginatedResponseObject):
    items: list[CMSContentDTO] = default_field()
