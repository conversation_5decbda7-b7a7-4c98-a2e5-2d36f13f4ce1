import uuid

from huma_plugins.components.cms.config import FEATURE_FLAG_CMS_STAGE_0
from huma_plugins.components.cms.dtos import CMS<PERSON><PERSON>ntD<PERSON>, DEPLOYMENT_STATUS_TO_CMS_STATUS_MAP
from huma_plugins.components.cms.exceptions import CMSContentIdIsMissingException
from huma_plugins.components.cms.library import (
    CMS_QUESTIONNAIRE_ID,
    CMS_QUESTIONNAIRE_PAGES,
    QUESTIONNAIRE_COLLECTION_NAME,
    QUESTIONNAIRE_CONFIGURATION_KIND_CMS,
    QUESTIONNAIRE_CONFIGURATION_KIND_INTERNAL,
    QUESTIONNAIRE_MODULE_ID,
    QUESTIONNAIRE_TYPE,
    ARTICLES_COLLECTION_NAME,
    TAG_CMS_QUESTIONNAIRE_AUTO_STORE,
)
from huma_plugins.components.cms.service.cms_service import CMSService
from huma_plugins.components.cms.use_case import (
    Create<PERSON>ontentRequestObject,
    CreateContentUseCase,
    RetrieveConvertedContentUseCase,
    RetrieveConvertedContentRequestObject,
)
from sdk.common.adapter.feature_toggle import FeatureToggleAdapter
from sdk.common.monitoring import report_exception
from sdk.common.utils import inject
from sdk.deployment.dtos.status import EnableStatus, Status as DeploymentStatus
from sdk.deployment.events import (
    RetrieveCMSQuestionnaireEvent,
    StoreCMSQuestionnaireEvent,
    RetrieveCMSArticlesEvent,
)
from sdk.deployment.models import Deployment
from sdk.deployment.service.deployment_service import DeploymentService
from sdk.module_result.dtos.module_config import RagThreshold


def on_store_module_config_config_body_to_cms_questionnaire(
    event: StoreCMSQuestionnaireEvent,
):
    features: FeatureToggleAdapter = inject.instance(FeatureToggleAdapter)
    if features.is_enabled(FEATURE_FLAG_CMS_STAGE_0):
        event.config_body.pop(CMS_QUESTIONNAIRE_ID, None)
        event.config_body.pop(QUESTIONNAIRE_TYPE, None)
        event.config_body.pop(CMSContentDTO.VERSION, None)
        return event.config_body

    if not event.config_body:
        raise NotImplementedError()

    if event.config_body.get("pages", None) is None:
        return event.config_body

    event.config_body.pop(CMS_QUESTIONNAIRE_ID, None)
    event.config_body.pop(QUESTIONNAIRE_TYPE, None)

    deployment_info: Deployment = DeploymentService().retrieve_deployment_with_fields(
        event.deployment_id,
        ["status", "version"],
    )
    data_field = CMSContentDTO.DRAFT_DATA if deployment_info.status == DeploymentStatus.DRAFT else CMSContentDTO.DATA
    cms_status = DEPLOYMENT_STATUS_TO_CMS_STATUS_MAP.get(deployment_info.status, None)
    if cms_status is None:
        err_msg = (
            f"No CMS status mapping found for deployment status: '{deployment_info.status}' "
            f"(deployment ID: {event.deployment_id})"
        )
        exception_error = NotImplementedError(err_msg)
        report_exception(
            exception_error,
            context_content={"deployment_id": event.deployment_id, "status": str(deployment_info.status)},
        )
        raise exception_error

    data = {
        data_field: event.config_body,
        CMSContentDTO.STATUS: cms_status.value,
        CMSContentDTO.RESOURCES: ["deployment/" + event.deployment_id],
        CMSContentDTO.TAGS: [TAG_CMS_QUESTIONNAIRE_AUTO_STORE],
        CMSContentDTO.SCHEMA: QUESTIONNAIRE_COLLECTION_NAME,
        CMSContentDTO.VERSION: deployment_info.version,
    }

    request = CreateContentRequestObject(**data)

    create_usecase = CreateContentUseCase()
    res = create_usecase.execute(request)

    config_id = event.config_body.get("id", str(uuid.uuid4()))

    return {
        CMSContentDTO.ID: config_id,
        CMS_QUESTIONNAIRE_ID: res.id,
        QUESTIONNAIRE_TYPE: QUESTIONNAIRE_CONFIGURATION_KIND_CMS,
        CMSContentDTO.VERSION: deployment_info.version,
    }


def on_load_deployment_extend_questionnaire_definition(event: RetrieveCMSQuestionnaireEvent):
    """
    Extends questionnaire definitions in deployment by loading content from CMS.
    """
    cms_content_map, configs_to_process = _identify_cms_questionnaires(event.modules_configs or [])
    _load_cms_content(cms_content_map)
    _handle_missing_content(configs_to_process, cms_content_map)
    _update_questionnaires_with_cms_content(configs_to_process, cms_content_map)
    features: FeatureToggleAdapter = inject.instance(FeatureToggleAdapter)
    if features.is_enabled(FEATURE_FLAG_CMS_STAGE_0):
        for config in event.modules_configs:
            if config.configBody:
                config.configBody.pop(CMS_QUESTIONNAIRE_ID, None)
                config.configBody.pop(QUESTIONNAIRE_TYPE, None)
                config.configBody.pop(CMSContentDTO.VERSION, None)


def _identify_cms_questionnaires(modules_configs):
    cms_content_map = {}
    configs_to_process = []

    for config in modules_configs:
        if not _should_process_questionnaire(config):
            continue

        cms_id = config.configBody.get(CMS_QUESTIONNAIRE_ID)
        if not cms_id:
            _report_missing_content_id(
                context_content={"reason": "CMS Content ID is not set"},
                module_config=config,
            )
            config.status = EnableStatus.DISABLED
            continue

        configs_to_process.append(config)
        if cms_id not in cms_content_map:
            cms_content_map[cms_id] = {"content": None, "thresholds": None}

    return cms_content_map, configs_to_process


def _should_process_questionnaire(config):
    if not config.configBody or config.moduleId != QUESTIONNAIRE_MODULE_ID:
        return False

    config_body = config.configBody
    questionnaire_kind = config_body.get(QUESTIONNAIRE_TYPE, QUESTIONNAIRE_CONFIGURATION_KIND_INTERNAL)

    if (
        questionnaire_kind != QUESTIONNAIRE_CONFIGURATION_KIND_CMS
        or config_body.get(CMS_QUESTIONNAIRE_PAGES) is not None
    ):
        return False

    return True


def _load_cms_content(cms_content_map):
    ids_to_load = [cms_id for cms_id, data in cms_content_map.items() if data["content"] is None]

    if not ids_to_load:
        return

    for content in CMSService().retrieve_by_ids(QUESTIONNAIRE_COLLECTION_NAME, ids_to_load):
        content_dict = content.to_dict(include_none=False)
        cms_content_map[content.id]["content"] = content_dict
        threshold_data = content_dict.get("data", {}).pop("ragThresholds", None)
        if threshold_data is not None:
            cms_content_map[content.id]["thresholds"] = threshold_data


def _handle_missing_content(configs_to_process, cms_content_map):
    missing_ids = [cms_id for cms_id, data in cms_content_map.items() if data["content"] is None]

    if not missing_ids:
        return

    _report_missing_content_id(
        context_content={"reason": "linked CMS Content isn't found in CMS"},
        cms_id=missing_ids,
    )

    # Disable configurations with missing content
    for config in configs_to_process:
        if config.configBody.get(CMS_QUESTIONNAIRE_ID) in missing_ids:
            config.status = EnableStatus.DISABLED


def _update_questionnaires_with_cms_content(configs_to_process, cms_content_map):
    for config in configs_to_process:
        cms_id = config.configBody.get(CMS_QUESTIONNAIRE_ID)
        content_data = cms_content_map.get(cms_id)

        if not content_data or content_data["content"] is None:
            continue

        thresholds = content_data["thresholds"]
        if thresholds and config.ragThresholds is None:
            config.ragThresholds = [RagThreshold.from_dict(threshold) for threshold in thresholds]

        config.configBody.update(content_data["content"].get("data", {}))


def on_load_cms_articles_to_module_configs(event: RetrieveCMSArticlesEvent):
    enabled_configs = [mc for mc in event.module_configs if mc.status == EnableStatus.ENABLED and mc.learnArticleIds]

    article_ids = set()
    for config in enabled_configs:
        article_ids.update(config.learnArticleIds)

    if not article_ids:
        return

    req_obj = RetrieveConvertedContentRequestObject(
        contentIds=list(article_ids),
        collection=ARTICLES_COLLECTION_NAME,
        language=event.language,
        unpublished=event.unpublished,
    )
    converted_articles = RetrieveConvertedContentUseCase().execute(req_obj)
    if not converted_articles:
        return

    articles = {article.id: article for article in converted_articles}
    for module_config in enabled_configs:
        module_articles = []
        for index, article_id in enumerate(module_config.learnArticleIds):
            if article_id not in articles:
                continue

            articles[article_id].order = index
            module_articles.append(articles[article_id].to_dict(include_none=False))

        module_config.learnArticles = module_articles


def _report_missing_content_id(context_content, cms_id=None, module_config=None):
    content = context_content or {}
    if cms_id:
        content["contentId"] = cms_id
    if module_config:
        content["ModuleId"] = module_config.moduleId
        content["moduleConfig"] = module_config

    content.update(context_content)

    report_exception(
        error=CMSContentIdIsMissingException(message="Error loading CMS Questionnaire for the ModuleConfig"),
        context_name="cms.callbacks.module_config.on_load_deployment_extend_questionnaire_definition",
        context_content=content,
        tags={"level": "error", "category": "cms"},
    )
