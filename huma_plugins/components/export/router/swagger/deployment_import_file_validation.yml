Import Deployment Config File Validation
---
tags:
  - import

security:
  - Bearer: []

parameters:
  - name: body
    in: body
    required: true
    schema:
      $ref: "#/definitions/ImportDeploymentValidateFileRequest"

responses:
  200:
    description: Deployment Config Service Info
    schema:
      $ref: '#/definitions/ImportDeploymentValidateFileResponse'

definitions:
  ImportDeploymentValidateFileRequest:
    required:
      - fileIds
    type: object
    properties:
      fileIds:
        type: array
        items:
          type: string
        example: ["5e84b0dab8dfa268b1180536"]

  ImportDeploymentValidateFileResponse:
    type: object
    properties:
      items:
        type: array
        items:
          type: object
          properties:
            exporterEmail:
              type: string
            exportedDateTime:
              type: string
              format: date-time
            platform:
              type: string
            deploymentName:
              type: string
            fileIntegrityVerified:
              type: boolean
            fileId:
              type: string
              example: "5e84b0dab8dfa268b1180536"
            fileSize:
              type: integer
            fileName:
              type: string
