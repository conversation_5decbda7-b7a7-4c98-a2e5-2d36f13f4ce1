from pathlib import Path

from flask import url_for

from huma_plugins.components.export.use_case.export_request_objects import (
    ExportRequestObject,
)
from sdk.module_result.modules import BreathlessnessModule
from sdk.module_result.modules.questionnaire import QuestionnaireDTO
from .export_config_tests import SUPER_ADMIN_ID
from .export_router_tests import (
    ExportTestCase,
    FORMAT,
    JSON_FORMAT,
    MODULE_NAMES,
    MODULE_VIEW,
    VIEW,
    mock_exclude_fields,
)
from .single_deployment_export_tests import ADMIN_USER_ID

MANAGER_CONFIG_ID = "5f0d64a47fe6637a82dcbea8"
USER_CONFIG_ID = "5f0d64a47fe6637a82dcbea7"
DEPLOYMENT_ID = "5d386cc6ff885918d96edb2e"
EXPORTED_ID = "5ed803dd5f2f99da73655134"


class ManagerQuestionnaireTests(ExportTestCase):
    fixtures = ExportTestCase.fixtures + [
        Path(__file__).parent.joinpath("fixtures/manager_questionnaire_data.json"),
    ]

    def setUp(self):
        super(ManagerQuestionnaireTests, self).setUp()
        self.data = self.get_sample_request_data(deployment_id=DEPLOYMENT_ID)
        self.data[FORMAT] = JSON_FORMAT
        self.data[ExportRequestObject.SINGLE_FILE_RESPONSE] = True
        self.data[VIEW] = MODULE_VIEW

    @mock_exclude_fields
    def test_export_manager_questionnaire_option(self):
        self.data[MODULE_NAMES] = ["ManagerQuestionnaire"]
        resp = self.request_export(self.data, ADMIN_USER_ID)
        manager_questionnaires, user_questionnaires = self._get_questionnaire_data(resp)
        self.assertEqual(1, len(manager_questionnaires))
        self.assertEqual(0, len(user_questionnaires))
        config_id = manager_questionnaires[0][QuestionnaireDTO.MODULE_CONFIG_ID]
        self.assertEqual(MANAGER_CONFIG_ID, config_id)

    @mock_exclude_fields
    def test_export_user_questionnaire_option_excludes_manager_data(self):
        self.data[MODULE_NAMES] = ["Questionnaire"]
        resp = self.request_export(self.data, ADMIN_USER_ID)
        manager_questionnaires, user_questionnaires = self._get_questionnaire_data(resp)
        self.assertEqual(0, len(manager_questionnaires))
        self.assertEqual(1, len(user_questionnaires))
        config_id = user_questionnaires[0][QuestionnaireDTO.MODULE_CONFIG_ID]
        self.assertEqual(USER_CONFIG_ID, config_id)

    @mock_exclude_fields
    def test_export_include_both_questionnaire_options(self):
        self.data[MODULE_NAMES] = ["Questionnaire", "ManagerQuestionnaire"]
        resp = self.request_export(self.data, ADMIN_USER_ID)
        manager_questionnaires, user_questionnaires = self._get_questionnaire_data(resp)
        self.assertEqual(1, len(manager_questionnaires))
        self.assertEqual(1, len(user_questionnaires))
        manager_config_id = manager_questionnaires[0][QuestionnaireDTO.MODULE_CONFIG_ID]
        user_config_id = user_questionnaires[0][QuestionnaireDTO.MODULE_CONFIG_ID]
        self.assertEqual(USER_CONFIG_ID, user_config_id)
        self.assertEqual(MANAGER_CONFIG_ID, manager_config_id)

    @mock_exclude_fields
    def test_export_all_questionnaire_options(self):
        resp = self.request_export(self.data, ADMIN_USER_ID)
        manager_questionnaires, user_questionnaires = self._get_questionnaire_data(resp)
        self.assertEqual(1, len(manager_questionnaires))
        self.assertEqual(1, len(user_questionnaires))
        manager_config_id = manager_questionnaires[0][QuestionnaireDTO.MODULE_CONFIG_ID]
        user_config_id = user_questionnaires[0][QuestionnaireDTO.MODULE_CONFIG_ID]
        self.assertEqual(USER_CONFIG_ID, user_config_id)
        self.assertEqual(MANAGER_CONFIG_ID, manager_config_id)

    @mock_exclude_fields
    def test_export_simple_questionnaire_does_not_affected_by_manager_questionnaire(
        self,
    ):
        self.data[ExportRequestObject.MODULE_NAMES] = [BreathlessnessModule.moduleId]
        resp = self.request_export(self.data, ADMIN_USER_ID)
        self.assertEqual(200, resp.status_code)
        self.assertEqual(1, len(resp.json))
        self.assertIn(BreathlessnessModule.moduleId, resp.json)

    @mock_exclude_fields
    def test_export_exclude_manager_questionnaire_options(self):
        self.data[ExportRequestObject.EXCLUDED_MODULE_NAMES] = ["ManagerQuestionnaire"]
        resp = self.request_export(self.data, ADMIN_USER_ID)
        manager_questionnaires, user_questionnaires = self._get_questionnaire_data(resp)
        self.assertEqual(0, len(manager_questionnaires))
        self.assertEqual(1, len(user_questionnaires))
        user_config_id = user_questionnaires[0][QuestionnaireDTO.MODULE_CONFIG_ID]
        self.assertEqual(USER_CONFIG_ID, user_config_id)

    @mock_exclude_fields
    def test_export_surgical_forms_with_enabled_consent_and_third_party_role(self):
        self.data[ExportRequestObject.DEIDENTIFIED] = True
        cfg_url = url_for(
            "deployment_route.update_onboarding_module_config",
            deployment_id=DEPLOYMENT_ID,
        )
        onboarding_cfg_body = {
            "onboardingId": "Consent",
            "status": "ENABLED",
            "order": 1,
            "userTypes": ["User"],
            "id": "61926cc89cb844829c967fe0",
        }
        cfg_rsp = self.flask_client.put(
            cfg_url,
            headers=self.get_headers_for_token(SUPER_ADMIN_ID),
            json=onboarding_cfg_body,
        )
        self.assertEqual(200, cfg_rsp.status_code)

        resp = self.request_export(self.data, ADMIN_USER_ID)
        manager_questionnaires, user_questionnaires = self._get_questionnaire_data(resp)
        self.assertEqual(1, len(manager_questionnaires))
        self.assertEqual(0, len(user_questionnaires))
        manager_config_id = manager_questionnaires[0][QuestionnaireDTO.MODULE_CONFIG_ID]
        self.assertEqual(MANAGER_CONFIG_ID, manager_config_id)

        third_party_resp = self.request_export(self.data, EXPORTED_ID)
        manager_questionnaires, user_questionnaires = self._get_questionnaire_data(third_party_resp)
        self.assertEqual(0, len(manager_questionnaires))
        self.assertEqual(0, len(user_questionnaires))

    def _get_questionnaire_data(self, rsp):
        self.assertEqual(200, rsp.status_code)
        questionnaires = rsp.json.get("Questionnaire", {})
        manager_questionnaires = questionnaires.get("ManagerQuestionnaire", [])
        user_questionnaires = questionnaires.get("Questionnaire", [])
        return manager_questionnaires, user_questionnaires
