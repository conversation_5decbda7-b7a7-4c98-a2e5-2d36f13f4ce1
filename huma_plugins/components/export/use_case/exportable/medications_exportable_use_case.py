from huma_plugins.components.export.helpers.convertion_helpers import (
    get_primitive_dict,
    ExportData,
)
from huma_plugins.components.export.use_case.exportable.exportable_request_objects import (
    ExportableRequestObject,
)
from huma_plugins.components.export.use_case.exportable.exportable_use_case import (
    ExportableUseCase,
)
from huma_plugins.components.medication.dtos.medication import MedicationDTO
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.modules.module import Module


class MedicationsModuleExportableUseCase(ExportableUseCase):
    def get_raw_result(self) -> ExportData:
        module = self.get_module(self.request_object, "Medications")
        if not module:
            return {}

        raw_results = self.get_module_result(self.request_object, module)
        return {module.moduleId: [get_primitive_dict(p) for p in raw_results]}

    def get_module_result(self, request_object: ExportableRequestObject, module: Module) -> list[PrimitiveDTO]:
        module_results = []
        if module.exportable:
            module_primitives = module.primitives.copy()
            module_primitives.append(MedicationDTO)
            for primitive_cls in module_primitives:
                primitives = self._export_repo.retrieve_primitives(
                    primitive_class=primitive_cls,
                    module_id=module.moduleId,
                    deployment_id=request_object.deploymentId,
                    start=request_object.fromDate,
                    end=request_object.toDate,
                    user_ids=request_object.userIds,
                    use_creation_time=request_object.useCreationTime,
                    start_date_field=MedicationDTO.UPDATE_DATE_TIME,
                )
                module_results.extend(primitives)
        return module_results
