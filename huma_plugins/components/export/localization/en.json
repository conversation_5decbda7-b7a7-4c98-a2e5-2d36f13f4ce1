{"DataPushNotification.failBody": "There was some problem while backing up your files, please try again.", "DataPushNotification.successBody": "Go to Downloads to view your files.", "DataPushNotification.title": "Your files are available for download", "Notification.export.body.error": "Please open Huma and request download again.", "Notification.export.body.success": "The data export you requested is ready. Click below to download your data.<br>This link will expire in 7 days.", "Notification.export.buttonText.error": "Open Huma", "Notification.export.buttonText.success": "Download", "Notification.export.subject": "Your Huma data export is ready", "Notification.export.subtitle.error": "Your file with all your data from Huma is failed to download.", "Notification.export.subtitle.success": "Your file with all your data from Huma is now ready to download.", "Notification.export.title": "%{username}", "PDFReport.CoverLetterPage.DOB": "DOB", "PDFReport.CoverLetterPage.dataPeriod": "Data Period", "PDFReport.CoverLetterPage.deployment": "Deployment", "PDFReport.CoverLetterPage.email": "Email", "PDFReport.CoverLetterPage.gender": "Gender", "PDFReport.CoverLetterPage.height": "Height", "PDFReport.CoverLetterPage.patient": "Patient", "PDFReport.CoverLetterPage.summaryHealthReport": "Summary Health Report", "SMSNotification.export.error": "Hi %{givenName} %{familyName},\n\nYour Huma data export has failed. Please open Huma and request the download again.\n\nOpen Huma here: %{link}", "SMSNotification.export.success": "Hi %{givenName} %{familyName},\n\nYour Huma data export is ready for you. Please note that this link will be valid for the next 7 days.\n\nDownload your data here: %{link}"}