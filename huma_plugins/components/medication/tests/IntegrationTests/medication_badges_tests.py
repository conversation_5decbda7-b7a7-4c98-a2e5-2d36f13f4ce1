from pathlib import Path
from typing import Any

from flask import url_for

from huma_plugins.components.medication.component import MedicationComponent
from huma_plugins.components.medication.dtos.medication_v2 import MedicationV2DTO
from huma_plugins.components.medication.module import MedicationsV2Module
from huma_plugins.components.medication.tests.IntegrationTests.medication_router_v2_tests import (
    sample_meal_based_medication,
)
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.calendar.component import CalendarComponent
from sdk.deployment.component import DeploymentComponent
from sdk.module_result.component import ModuleResultComponent
from sdk.module_result.tests.IntegrationTests.user_notes_tests import UserNotesTestMixin
from sdk.notification.component import NotificationComponent
from sdk.organization.component import OrganizationComponent
from sdk.tests.extension_test_case import ExtensionTestCase

VALID_USER_ID = "5e84b0dab8dfa268b1180536"
VALID_USER_2_ID = "5e8f0c74b50aa9656c34789c"
VALID_MANAGER_ID = "5e8f0c74b50aa9656c34789d"
VALID_DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
VALID_PROXY_USER_ID = "5e8f0c74b50aa9656c342220"
MEDICATION_CONFIG_ID = "5f1824ba504787d8d89ebeca"


class MedicationHistoryBadgesTestCase(ExtensionTestCase, UserNotesTestMixin):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        CalendarComponent(),
        DeploymentComponent(),
        ModuleResultComponent(additional_modules=[MedicationsV2Module()]),
        MedicationComponent(),
        OrganizationComponent(),
        NotificationComponent(),
    ]
    fixtures = [
        Path(__file__).parent.joinpath("fixtures/deployments_dump.json"),
        Path(__file__).parent.joinpath("fixtures/medication_dump_v2.json"),
    ]

    def setUp(self):
        super().setUp()

        self.manager_headers = self.get_headers_for_token(VALID_MANAGER_ID)
        self.user_headers = self.get_headers_for_token(VALID_USER_ID)
        self.proxy_headers = self.get_headers_for_token(VALID_PROXY_USER_ID)
        self.user_route = f"/api/extensions/v1/user/{VALID_USER_ID}"

    def test_badges_with_medications(self):
        rsp = self.flask_client.get(self.user_route, headers=self.user_headers)
        self.assertEqual(200, rsp.status_code)
        user = rsp.json
        self.assertIn("badges", user)
        badges = user["badges"]
        self.assertIn("medications", badges)
        medications = badges["medications"]
        self.assertEqual(medications["total"], 1)
        self.assertEqual(len(medications["created"]), 1)

    def test_no_badges_for_meal_based_medications(self):
        rsp = self.flask_client.get(self.user_route, headers=self.user_headers)
        self.assertEqual(200, rsp.status_code)
        medication_badges = rsp.json["badges"]["medications"]
        self.assertEqual(1, medication_badges["total"])

        body = sample_meal_based_medication()
        body[MedicationV2DTO.MODULE_CONFIG_ID] = MEDICATION_CONFIG_ID
        self._create_medication_by_manager(body)

        rsp = self.flask_client.get(self.user_route, headers=self.user_headers)
        self.assertEqual(200, rsp.status_code)
        medication_badges = rsp.json["badges"]["medications"]
        # Badges count unchanged
        self.assertEqual(1, medication_badges["total"])

    def test_badges_with_medications_with_proxy(self):
        rsp = self.flask_client.get(self.user_route, headers=self.proxy_headers)
        self.assertEqual(200, rsp.status_code)
        user = rsp.json
        self.assertIn("badges", user)
        badges = user["badges"]
        self.assertIn("medications", badges)
        medications = badges["medications"]
        self.assertEqual(medications["total"], 1)
        self.assertEqual(len(medications["created"]), 1)

    def test_set_medical_histories_as_seen(self):
        expected_medication_history_id_list = self._mark_history_seen(VALID_USER_ID)
        self.assertEqual(1, len(expected_medication_history_id_list))

    def test_set_medical_histories_as_seen_with_proxy(self):
        expected_medication_history_id_list = self._mark_history_seen(VALID_PROXY_USER_ID)
        self.assertEqual(1, len(expected_medication_history_id_list))

    def test_medication_history_flush_on_observation_note_submission(self):
        response_dict = self._get_unseen_medication_history()
        self.assertIn("total", response_dict)
        self.assertEqual(2, response_dict["total"])

        rsp = self._add_user_notes(VALID_USER_ID, VALID_DEPLOYMENT_ID, "note")
        self.assertEqual(201, rsp.status_code)

        response_dict = self._get_unseen_medication_history()
        self.assertEqual(0, response_dict["total"])

    def _get_unseen_medication_history(self) -> dict[str, Any]:
        url = url_for(
            "medication_v2.retrieve_medications_history",
            user_id=VALID_USER_ID,
            seen=False,
            searchWithUserType="USER",
        )
        response = self.flask_client.get(url, headers=self.user_headers)
        self.assertEqual(200, response.status_code)
        return response.json

    def _mark_history_seen(self, as_: str) -> dict:
        rsp = self.flask_client.post(
            url_for("medication_v2.update_medication_history_as_seen", user_id=VALID_USER_ID),
            headers=self.get_headers_for_token(as_),
        )
        self.assertEqual(200, rsp.status_code)
        return rsp.json

    def _create_medication_by_manager(self, body: dict) -> dict:
        url = url_for(
            "medication_v2.create_medication",
            user_id=VALID_USER_ID,
        )
        headers = self.get_headers_for_token(VALID_MANAGER_ID)
        response = self.flask_client.post(url, json=body, headers=headers)
        self.assertEqual(201, response.status_code)
        return response.json
