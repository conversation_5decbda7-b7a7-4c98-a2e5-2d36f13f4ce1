from dataclasses import field, fields
from datetime import datetime
from typing import Optional

import pytz
from apiflask import Schema
from dateutil import rrule
from dateutil.relativedelta import relativedelta
from flask import g
from marshmallow import post_load

from huma_plugins.components.medication.dtos.medication_log import (
    MedicationLogDTO,
    MedicationStatus,
)
from huma_plugins.components.medication.dtos.medication_v2 import (
    MedicationReasonUpdated,
    MedicationV2DTO,
    MedicationV2Schedule,
    SubmitterUserType,
)
from huma_plugins.components.medication.module import MedicationsV2Module
from sdk.appointment.exceptions import InvalidDateException
from sdk.authorization.dtos.user import UserDTO
from sdk.calendar.router.calendar_request import ExportCalendarRequestObject
from sdk.common.usecase.request_object import RequestObject
from sdk.common.utils import inject
from sdk.common.utils.common_functions_utils import deep_get
from sdk.common.utils.convertible import (
    ConvertibleClassValidationError,
    convertibleclass,
    default_field,
    meta,
    natural_number_field,
    positive_integer_field,
    required_field,
)
from sdk.common.utils.date_utils import localize_from_utc, localize_to_utc
from sdk.common.utils.fields import id_field
from sdk.common.utils.marshmallow.schemas import BaseSchema
from sdk.common.utils.validators import (
    default_datetime_meta,
    must_be_at_least_one_of,
    must_be_only_one_of,
    must_be_present,
    must_not_be_present,
    not_empty,
    remove_none_values,
    str_to_bool,
    to_clean_string,
    utc_str_to_date,
    validate_entity_name,
    validate_object_id,
)
from sdk.deployment.dtos.deployment import DeploymentDTO
from sdk.module_result.dtos.module_config import ModuleConfig
from sdk.module_result.exceptions import InvalidModuleConfigurationException
from sdk.module_result.modules.modules_manager import ModulesManager


@convertibleclass
class BaseMedicationRequestObjectV2(MedicationV2DTO, RequestObject):
    DEPLOYMENT = "deployment"
    USER = "user"
    SUBMITTER = "submitter"

    id: str = default_field(metadata=meta(validate_object_id, value_to_field=str, view_arg="medication_id"))
    userId: str = default_field(
        metadata=meta(validate_object_id, value_to_field=str, view_arg="user_id"),
    )

    enabled: bool = default_field()
    user: UserDTO = default_field(metadata=meta(dump_only=True))
    deployment: DeploymentDTO = default_field(metadata=meta(dump_only=True))
    submitter: UserDTO = default_field(metadata=meta(dump_only=True))
    moduleConfig: ModuleConfig = default_field(metadata=meta(dump_only=True))

    @post_load
    def add_parameters(self: BaseSchema, data: dict, **kwargs) -> dict:
        if not isinstance(data, dict):
            return data

        req_obj = BaseMedicationRequestObjectV2
        data[req_obj.USER] = g.authz_path_user.user
        data[req_obj.MODULE_ID] = MedicationsV2Module.moduleId
        data[req_obj.SUBMITTER] = g.authz_user.user
        data[req_obj.SUBMITTER_ID] = g.authz_user.id
        data[req_obj.SUBMITTER_USER_TYPE] = SubmitterUserType(g.authz_user.user_type().upper())
        return data

    @classmethod
    def validate(cls, req_obj: "BaseMedicationRequestObjectV2"):
        MedicationV2DTO.validate(req_obj)
        must_not_be_present(moduleConfig=req_obj.moduleConfig)
        cls._validate_start_date(req_obj)

        if req_obj.indication or req_obj.endDate:
            must_be_present(startDate=req_obj.startDate)
            if req_obj.endDate and req_obj.startDate >= utc_str_to_date(req_obj.endDate):
                raise ConvertibleClassValidationError("End date must be greater than start date.")

        if req_obj.additionalFields:
            cls._validate_additional_info(req_obj)

    @classmethod
    def _validate_additional_info(
        cls,
        req_obj: "BaseMedicationRequestObjectV2",
    ):
        if req_obj.is_inhaler:
            medication_mc = req_obj.deployment.find_module_config(
                module_id="MedicationsV2", module_config_id=req_obj.moduleConfigId
            )
            if not medication_mc:
                return

            config_body = medication_mc.configBody
            units: list[str] = deep_get(config_body, "types.inhaler.units", [])
            if units and req_obj.unit and req_obj.unit not in units:
                raise ConvertibleClassValidationError(f"unit [{req_obj.unit}] must be one of {units}")
            frequency: str = req_obj.additionalFields.inhaler.frequency.value
            bounds: Optional[dict] = deep_get(config_body, f"flows.inhaler.frequencies.{frequency}", {})

            if req_obj.submitterUserType is not SubmitterUserType.MANAGER:
                if not req_obj.dosage:
                    return

                min_bound = bounds.get("lowerBound", float("-inf"))
                max_bound = bounds.get("upperBound", float("inf"))
                if not (min_bound <= req_obj.dosage <= max_bound):
                    raise ConvertibleClassValidationError(
                        f"dosage value should be in range of [{min_bound}-{max_bound}]"
                    )

            cls._validate_and_set_inhaler_fields(req_obj)

    @classmethod
    def _validate_and_set_inhaler_fields(cls, req_obj: "BaseMedicationRequestObjectV2"):
        req_obj.schedule.asNeeded = req_obj.is_as_needed

        if req_obj.has_ranged_schedule:
            req_obj.schedule.isoDuration = "P1D"
            scheduled_required_fields = {
                f"{cls.SCHEDULE}.{MedicationV2Schedule.TIME_RANGES}": req_obj.schedule.timeRanges,
            }
            must_be_present(**scheduled_required_fields)

    @classmethod
    def _validate_start_date(cls, req_obj: "BaseMedicationRequestObjectV2"):
        if not req_obj.startDate:
            return

        user_creation_time = req_obj.user.createDateTime
        if not user_creation_time or req_obj.startDate > user_creation_time.date():
            return

        a_year_from_user_creation = user_creation_time.date() - relativedelta(years=1)
        if req_obj.startDate < a_year_from_user_creation:
            raise ConvertibleClassValidationError("Start date can't be more than 1 year before user creation date.")

    def post_init(self):
        manager = inject.instance(ModulesManager)
        module = manager.find_module(self.moduleId)
        if not module:
            raise InvalidModuleConfigurationException("Module MedicationV2 is not configured.")
        self.moduleConfig = module.extract_module_config(
            self.deployment.moduleConfigs,
            module_config_id=self.moduleConfigId,
        )
        if not self.moduleConfig.notificationData:
            raise InvalidModuleConfigurationException("Notification Data is not configured for MedicationV2 module.")
        if self.submitterUserType and self.submitterUserType == SubmitterUserType.PROXY:
            self.submitterUserType = SubmitterUserType.USER

        for meal in self.mealSchedule or []:
            meal.unit = self.unit
            meal.customUnit = self.customUnit

    def to_medication(self) -> MedicationV2DTO:
        data = {f.name: getattr(self, f.name, None) for f in fields(MedicationV2DTO)}  # noqa
        if self.reason and self.submitterUserType == SubmitterUserType.MANAGER:
            data[self.REASON] = {
                MedicationReasonUpdated.CLINICIAN_ID: self.submitterId,
                MedicationReasonUpdated.REASON: self.reason,
                MedicationReasonUpdated.CLINICIAN_NAME: self.submitter.get_full_name(),
            }
        return MedicationV2DTO(**remove_none_values(data))


@convertibleclass
class CreateMedicationRequestObjectV2(BaseMedicationRequestObjectV2):
    enabled: bool = field(default=True)
    name: str = required_field(metadata=meta(validate_entity_name, value_to_field=to_clean_string))
    moduleConfigId: str = required_field(metadata=meta(validate_object_id, value_to_field=str))

    class Meta(BaseSchema.Meta):
        exclude = (
            MedicationV2DTO.ID,
            MedicationV2DTO.REASON,
            MedicationV2DTO.ADHERENCE,
            MedicationV2DTO.ADHERENCES,
        )

    @post_load
    def add_create_obj_parameters(self: Schema, data: dict, **kwargs) -> dict:
        if not isinstance(data, dict):
            return data

        req_obj = CreateMedicationRequestObjectV2
        data[req_obj.DEPLOYMENT] = g.authz_path_user.deployment
        data[req_obj.DEPLOYMENT_ID] = g.authz_path_user.deployment_id()
        if data.get(req_obj.DOSAGE) is None:
            data[req_obj.DOSAGE] = 0.0
        return data

    @classmethod
    def validate(cls, medication: "CreateMedicationRequestObjectV2"):
        must_be_only_one_of(
            schedule=medication.schedule,
            mealSchedule=medication.mealSchedule,
        )

        if not medication.additionalFields:
            if medication.schedule and not medication.schedule.asNeeded:
                scheduled_required_fields = {
                    f"{cls.SCHEDULE}.{MedicationV2Schedule.ISO_DURATION}": medication.schedule.isoDuration,
                    f"{cls.SCHEDULE}.{MedicationV2Schedule.TIMES_OF_READINGS}": medication.schedule.timesOfReadings,
                }
                must_be_present(**scheduled_required_fields)

        super().validate(medication)


@convertibleclass
class UpdateMedicationRequestObjectV2(BaseMedicationRequestObjectV2):
    enabled: bool = required_field()
    reason: str = default_field(metadata=meta(not_empty))

    class Meta(BaseSchema.Meta):
        exclude = (
            MedicationV2DTO.NAME,
            MedicationV2DTO.ADHERENCE,
            MedicationV2DTO.ADHERENCES,
        )

    @post_load
    def add_update_obj_parameters(self: Schema, data: dict, **kwargs) -> dict:
        if not isinstance(data, dict):
            return data

        req_obj = UpdateMedicationRequestObjectV2
        data[req_obj.DEPLOYMENT] = g.authz_path_user.deployment
        data[req_obj.DEPLOYMENT_ID] = g.authz_path_user.deployment_id()
        return data

    @classmethod
    def validate(cls, medication: "UpdateMedicationRequestObjectV2"):
        must_be_only_one_of(
            schedule=medication.schedule,
            mealSchedule=medication.mealSchedule,
            could_be_none=True,
        )
        if medication.schedule:
            must_not_be_present(connectedModules=medication.connectedModules)
            if not medication.additionalFields and not medication.schedule.asNeeded:
                scheduled_required_fields = {
                    f"{cls.SCHEDULE}.{MedicationV2Schedule.ISO_DURATION}": medication.schedule.isoDuration,
                    f"{cls.SCHEDULE}.{MedicationV2Schedule.TIMES_OF_READINGS}": medication.schedule.timesOfReadings,
                }
                must_be_present(**scheduled_required_fields)
        else:
            must_not_be_present(maxDosage=medication.maxDosage)

        if medication.submitterUserType == SubmitterUserType.MANAGER:
            must_be_present(reason=medication.reason)
        else:
            must_not_be_present(reason=medication.reason)

        if medication.mealSchedule:
            must_be_present(unit=medication.unit)

        super().validate(medication)


@convertibleclass
class RetrieveMedicationsRequestObjectV2(RequestObject):
    USER = "user"
    SKIP = "skip"
    LIMIT = "limit"
    DEPLOYMENT_ID = "deploymentId"
    ENABLED = "enabled"
    HIDE_MEAL_BASED = "hideMealBased"
    GROUPED = "grouped"

    user: UserDTO = default_field(metadata=meta(dump_only=True))
    deploymentId: str = default_field(metadata=meta(dump_only=True))
    skip: int = positive_integer_field(default=0)
    limit: int = natural_number_field(default=100000)
    enabled: bool = default_field(metadata=meta(value_to_field=str_to_bool))
    hideMealBased: bool = field(default=False)
    grouped: bool = field(default=False)

    @post_load
    def add_parameters(self: Schema, data: dict, **kwargs) -> dict:
        if not isinstance(data, dict):
            return data

        req_obj = RetrieveMedicationsRequestObjectV2
        data[req_obj.USER] = g.authz_path_user.user
        data[req_obj.DEPLOYMENT_ID] = g.authz_path_user.deployment_id()
        return data


@convertibleclass
class RetrieveMedicationBadgesRequestObject(RequestObject):
    USER_ID = "userId"
    DEPLOYMENT_ID = "deploymentId"

    userId: str = id_field(required=True)
    deploymentId: str = id_field(required=True)


@convertibleclass
class UpdateAsSeenMedicationHistoryRequestObject(RequestObject):
    USER_ID = "userId"
    USER_TYPE = "userType"
    DEPLOYMENT_ID = "deploymentId"

    userId: str = required_field(metadata=meta(validate_object_id, value_to_field=str))
    userType: str = default_field(metadata=meta(dump_only=True))
    deploymentId: str = default_field(metadata=meta(validate_object_id, value_to_field=str))


@convertibleclass
class RetrieveMedicationsHistoryV2RequestObject(RequestObject):
    SKIP = "skip"
    LIMIT = "limit"
    SEEN = "seen"
    SEARCH_WITH_USER_TYPE = "searchWithUserType"
    USER_ID = "userId"
    USER_TYPE = "userType"
    DEPLOYMENT_ID = "deploymentId"

    skip: int = positive_integer_field(default=0)
    limit: int = natural_number_field(default=100000)
    seen: bool = default_field(metadata=meta(value_to_field=str_to_bool))
    searchWithUserType: str = default_field()

    userId: str = default_field(metadata=meta(view_arg="user_id"))
    userType: str = default_field(metadata=meta(dump_only=True))
    deploymentId: str = default_field(metadata=meta(dump_only=True))

    @post_load
    def add_parameters(self: Schema, data: dict, **kwargs) -> dict:
        if not isinstance(data, dict):
            return data

        req_obj = RetrieveMedicationsHistoryV2RequestObject
        data[req_obj.USER_TYPE] = g.authz_user.user_type().upper()
        data[req_obj.DEPLOYMENT_ID] = g.authz_user.deployment_id()
        return data


@convertibleclass
class RetrieveMedicationsNotesRequestObject(RequestObject):
    USER_ID = "userId"
    DEPLOYMENT_ID = "deploymentId"
    MEDICATION_ID = "medicationId"

    deploymentId: str = id_field(required=True)
    userId: str = id_field(required=True)
    medicationId: str = id_field(required=True)


@convertibleclass
class RetrieveMedicationsCalendarRequestObject(RequestObject):
    USER = "user"
    DEPLOYMENT_ID = "deploymentId"
    START_DATE_TIME = "startDateTime"
    END_DATE_TIME = "endDateTime"
    CONNECTED_MODULE = "connectedModule"
    HIDE_MEAL_BASED = "hideMealBased"

    user: UserDTO = default_field(metadata=meta(dump_only=True))
    deploymentId: str = default_field(metadata=meta(dump_only=True))
    startDateTime: datetime = required_field(metadata=default_datetime_meta())  # coming as local time
    endDateTime: datetime = required_field(metadata=default_datetime_meta())  # coming as local time
    logOverride: bool = field(default=True)
    connectedModule: str = default_field(metadata=meta(validate_entity_name))
    hideMealBased: bool = field(default=False)

    @post_load
    def add_parameters(self: Schema, data: dict, **kwargs) -> dict:
        if not isinstance(data, dict):
            return data

        req_obj = RetrieveMedicationsCalendarRequestObject
        data[req_obj.USER] = g.authz_path_user.user
        data[req_obj.DEPLOYMENT_ID] = g.authz_path_user.deployment_id()
        return data

    @classmethod
    def validate(cls, request: "RetrieveMedicationsCalendarRequestObject"):
        if request.startDateTime > request.endDateTime:
            raise InvalidDateException("Start date can't be later than end date.")

    def affected_days(self):
        rule = rrule.rrule(
            freq=rrule.DAILY,
            dtstart=self.startDateTime,
            until=self.endDateTime,
            byhour=0,
            byminute=0,
            bysecond=0,
        )
        return iter(rule)

    def post_init(self):
        timezone = pytz.timezone(self.user.timezone)
        self.startDateTime = localize_to_utc(self.startDateTime, timezone)
        self.endDateTime = localize_to_utc(self.endDateTime, timezone)


@convertibleclass
class RetrieveMedicationCalendarRequestObject(RetrieveMedicationsCalendarRequestObject):
    MEDICATION_ID = "medicationId"

    medicationId: str = default_field(metadata=meta(view_arg="medication_id"))


@convertibleclass
class CreateMedicationLogRequestObject(MedicationLogDTO, RequestObject):
    DEPLOYMENT = "deployment"
    USER = "user"

    status: MedicationStatus = default_field()
    eventId: str = id_field()
    deployment: DeploymentDTO = default_field(metadata=meta(dump_only=True))
    user: UserDTO = default_field(metadata=meta(dump_only=True))
    userId: str = default_field(
        metadata=meta(validate_object_id, value_to_field=str, view_arg="user_id"),
    )
    deploymentId: str = default_field(
        metadata=meta(validate_object_id, value_to_field=str, dump_only=True),
    )
    medicationId: str = default_field(metadata=meta(validate_object_id, value_to_field=str, view_arg="medication_id"))

    @post_load
    def add_parameters(self: BaseSchema, data: dict, **kwargs) -> dict:
        if not isinstance(data, dict):
            return data

        req_obj = CreateMedicationLogRequestObject
        data[req_obj.USER] = g.path_user
        data[req_obj.DEPLOYMENT] = g.authz_path_user.deployment
        data[req_obj.DEPLOYMENT_ID] = g.authz_user.deployment_id()
        return data

    @classmethod
    def validate(cls, medication_log: "CreateMedicationLogRequestObject"):
        must_be_at_least_one_of(
            eventId=medication_log.eventId,
            medicatedDateTimeSlots=medication_log.medicatedDateTimeSlots,
        )
        if medication_log.eventId:
            medication_log._validate_start_date_time()

    def post_init(self):
        if self.startDateTime:
            self.startDateTime = localize_from_utc(self.startDateTime, self.user.timezone)

    def _validate_start_date_time(self):
        must_be_present(startDateTime=self.startDateTime)

        tz = pytz.timezone(self.user.timezone)
        now = datetime.now(tz)
        start_dt = self.startDateTime.replace(tzinfo=pytz.UTC).astimezone(tz)
        if now.date() < start_dt.date():
            raise ConvertibleClassValidationError("Log submission can not be made for a future date")


@convertibleclass
class RetrieveUnseenMedicationResultsRequestObject(RequestObject):
    USER_ID = "userId"
    DEPLOYMENT = "deployment"

    userId: str = default_field(metadata=meta(dump_only=True))
    deployment: DeploymentDTO = default_field(metadata=meta(dump_only=True))
    moduleConfigId: str = default_field()
    skip: int = positive_integer_field(default=0)
    limit: int = natural_number_field(default=None)

    @post_load
    def add_parameters(self: Schema, data: dict, **kwargs) -> dict:
        if not isinstance(data, dict):
            return data

        req_obj = RetrieveUnseenMedicationResultsRequestObject
        data[req_obj.USER_ID] = g.authz_path_user.id
        data[req_obj.DEPLOYMENT] = g.authz_path_user.deployment
        return data


class ExportMedicationsCalendarRequestObject(ExportCalendarRequestObject):
    @classmethod
    def validate(cls, request_object):
        must_be_present(start=request_object.start, end=request_object.end)


@convertibleclass
class DeleteUserMedicationV2RequestObject:
    USER_ID = "userId"

    userId: str = required_field(metadata=meta(validate_object_id, value_to_field=str))


@convertibleclass
class DisableMedicationScheduleEventRequestObject:
    DEPLOYMENT_ID = "deploymentId"
    MODULE_CONFIG_ID = "moduleConfigId"

    deploymentId: str = default_field(metadata=meta(validate_object_id))
    moduleConfigId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class DisableUserMedicationsRequestObject:
    SUBMITTER_ID = "submitterId"
    USER_ID = "userId"

    submitterId: str = default_field(metadata=meta(validate_object_id))
    userId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class DeleteUserMedicationLogRequestObject(RequestObject):
    USER_ID = "userId"

    userId: str = required_field(metadata=meta(validate_object_id, value_to_field=str))


@convertibleclass
class DeleteUserMedicationHistoryV2RequestObject(RequestObject):
    USER_ID = "userId"

    userId: str = required_field(metadata=meta(validate_object_id, value_to_field=str))
