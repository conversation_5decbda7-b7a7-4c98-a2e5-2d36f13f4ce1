Retrieve Medication Version 2
---
tags:
  - medicationv2

security:
  - Bearer: []

parameters:
  - in: path
    name: user_id
    description: user id to create medication for
    required: true
    type: string
  - name: skip
    in: query
    required: true
    type: integer
  - name: limit
    in: query
    required: true
    type: integer
  - name: enabled
    in: query
    type: boolean
responses:
  201:
    description: Medication with array of records to each medication based on requested data
    schema:
      $ref: "#/definitions/RetrieveMedicationResponseV2"
definitions:
  RetrieveMedicationResponseV2:
    type: object
    required:
      - items
      - total
    properties:
      items:
        type: array
        items:
          $ref: "#/definitions/MedicationV2"
      total:
        type: integer
        example: 1
