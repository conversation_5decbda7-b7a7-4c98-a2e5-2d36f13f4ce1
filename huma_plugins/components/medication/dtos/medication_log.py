from datetime import datetime
from enum import Enum, StrEnum

from sdk.authorization.dtos.user import Flags
from sdk.authorization.services.authorization import AuthorizationService
from sdk.common.common_models.device_models import DeviceDetails
from sdk.common.utils.convertible import (
    convertibleclass,
    default_field,
    field,
    meta,
    required_field,
)
from sdk.common.utils.date_utils import localize_to_utc
from sdk.common.utils.fields import id_field
from sdk.common.utils.string_utils import snake_case_to_camel_case
from sdk.common.utils.validators import (
    default_datetime_meta,
    default_datetimes_meta,
    not_empty,
    utc_str_field_to_val,
    validate_entity_name,
    validate_not_too_long,
    validate_object_id,
    validate_range,
)
from sdk.module_result.dtos.diary_item import (
    Chip,
    DetailsPresentationType,
    DiaryBackgroundColor,
    DiaryDetail,
    DiaryIcon,
    DiaryItem,
    DiaryVisibility,
)
from sdk.module_result.dtos.primitives import MealType


class MedicationStatus(StrEnum):
    TAKEN = "TAKEN"
    SKIPPED = "SKIPPED"

    @classmethod
    def diary_color_mapping(cls) -> dict:
        return {
            MedicationStatus.TAKEN: DiaryBackgroundColor.color_mapping()[Flags.GREEN],
            MedicationStatus.SKIPPED: DiaryBackgroundColor.color_mapping()[Flags.GRAY],
        }


class LogSource(Enum):
    MANUAL = "Manual"


@convertibleclass
class ShortPrimitiveData:
    primitiveId: str = id_field(required=True)
    primitiveName: str = required_field(metadata=meta(validate_entity_name))
    moduleId: str = required_field(metadata=meta(validate_entity_name))
    moduleConfigId: str = id_field(required=True)


class MedicationLogDiaryDetailLiterals(Enum):
    """Order matters!"""

    MEDICATION = "Diary.detailName.medication"
    DOSAGE = "Diary.detailName.dosage"
    DATE_TIME = "Diary.detailName.dateTime"
    STATUS = "Diary.detailName.status"
    MEAL_BASED = "Diary.detailName.mealBased"
    DESCRIPTION = "Diary.detailName.description"


@convertibleclass
class MedicationLogDTO:
    MODEL_NAME = "MedicationLog"
    ID = "id"
    ID_ = "_id"
    USER_ID = "userId"
    DEPLOYMENT_ID = "deploymentId"
    EVENT_ID = "eventId"
    STATUS = "status"
    START_DATE_TIME = "startDateTime"
    UPDATE_DATE_TIME = "updateDateTime"
    CREATE_DATE_TIME = "createDateTime"
    LOGS_COUNT = "logsCount"
    MEDICATION_ID = "medicationId"
    MEDICATED_DATETIME_SLOTS = "medicatedDateTimeSlots"
    DEVICE = "device"
    SOURCE = "source"
    MEAL = "meal"
    RELATED_PRIMITIVE_DATA = "relatedPrimitiveData"

    id: str = default_field(metadata=meta(validate_object_id, value_to_field=str, dump_only=True))
    userId: str = required_field(metadata=meta(validate_object_id, value_to_field=str))
    deploymentId: str = required_field(metadata=meta(validate_object_id, value_to_field=str))
    eventId: str = required_field(metadata=meta(validate_object_id, value_to_field=str))
    medicationId: str = required_field(metadata=meta(validate_object_id, value_to_field=str))
    logsCount: int = default_field(metadata=meta(validate_range(min_=0), val_to_field=int))
    status: MedicationStatus = required_field(metadata=meta(not_empty))
    medicatedDateTimeSlots: list[datetime] = default_field(metadata=default_datetimes_meta())
    startDateTime: datetime = default_field(metadata=default_datetime_meta())
    updateDateTime: datetime = default_field(metadata=default_datetime_meta(True))
    createDateTime: datetime = default_field(metadata=default_datetime_meta(True))
    device: DeviceDetails = default_field()
    source: str = field(default=LogSource.MANUAL.value)
    medicationName: str = default_field(metadata=meta(dump_only=True))
    dosage: float = default_field(metadata=meta(dump_only=True))
    unit: str = default_field(metadata=meta(dump_only=True))
    customUnit: str = default_field(metadata=meta(dump_only=True))
    meal: MealType = default_field()
    userNote: str = default_field(metadata=meta(validate_not_too_long))
    relatedPrimitiveData: ShortPrimitiveData = default_field()

    def localize_to_utc(self, timezone):
        """Converts local startDateTime in model to UTC time"""
        self.startDateTime = localize_to_utc(self.startDateTime, timezone)

    @property
    def is_taken(self):
        return self.status and (self.status is MedicationStatus.TAKEN)

    @property
    def is_manual(self):
        return self.source == "Manual"

    def to_diary(self) -> DiaryItem:
        from huma_plugins.components.medication.module import MedicationsV2Module

        KEYS = MedicationLogDiaryDetailLiterals
        status_color = MedicationStatus.diary_color_mapping()[self.status]
        meal_key = DiaryItem.get_meal_key(self.meal)
        status_key = f"Diary.detailName.status.{snake_case_to_camel_case(self.status)}"
        formatted_dosage = str(int(self.dosage)) if self.dosage.is_integer() else str(self.dosage)
        if self.status == MedicationStatus.SKIPPED:
            user = AuthorizationService().retrieve_simple_user_profile(self.userId)
            self.localize_to_utc(user.timezone)
            start_dt = self.startDateTime
        else:
            start_dt = self.medicatedDateTimeSlots[0]

        unit = self.customUnit or self.unit
        diary_data = {
            DiaryItem.DIARY_ID: self.id,
            DiaryItem.DIARY_SOURCE_ID: self.id,
            DiaryItem.USER_ID: self.userId,
            DiaryItem.VALUE: [formatted_dosage],
            DiaryItem.UNIT: [unit],
            DiaryItem.DIARY_TYPE: MedicationsV2Module.moduleId,
            DiaryItem.TITLE: f"<b>{self.medicationName}</b>",
            DiaryItem.MEAL_EVENT_TYPE: self.meal,
            DiaryItem.START_DATE_TIME: start_dt,
            DiaryItem.DIARY_BG: DiaryBackgroundColor.light_color_mapping()[Flags.GRAY],
            DiaryItem.DETAILS_PRESENTATION: DetailsPresentationType.BOLD_BOTTOM,
            DiaryItem.VISIBILITY: DiaryVisibility.all(as_strings=True),
            DiaryItem.DIARY_SIDEBAR: status_color,
            DiaryItem.DETAILS: [
                {
                    DiaryDetail.KEY: KEYS.MEDICATION.value,
                    DiaryDetail.VALUE: self.medicationName,
                },
                {
                    DiaryDetail.KEY: KEYS.DOSAGE.value,
                    DiaryDetail.VALUE: f"{formatted_dosage} {unit}",
                },
                {
                    DiaryDetail.KEY: KEYS.DATE_TIME.value,
                    DiaryDetail.VALUE: utc_str_field_to_val(start_dt),
                },
                {DiaryDetail.KEY: KEYS.STATUS.value, DiaryDetail.VALUE: status_key},
                {DiaryDetail.KEY: KEYS.MEAL_BASED.value, DiaryDetail.VALUE: meal_key},
            ],
            DiaryItem.DIARY_CHIPS: [
                Chip(text=meal_key),
                Chip(
                    text=status_key,
                    borderColor=status_color,
                    icon=DiaryIcon.DOT,
                    iconColor=status_color,
                ),
            ],
            DiaryItem.EXTRA_FIELDS: {"relatedModuleId": self.relatedPrimitiveData.moduleId},
        }

        details: list[dict] = diary_data[DiaryItem.DETAILS]
        if self.userNote:
            details.append(
                {
                    DiaryDetail.KEY: KEYS.DESCRIPTION.value,
                    DiaryDetail.VALUE: self.userNote,
                }
            )

        return DiaryItem.from_dict(diary_data)
