from django.core.validators import MinValueValidator
from django.db import models

from huma_plugins.components.medication.dtos.medication_v2 import SubmitterUserType
from sdk.common.utils.mongo_utils import generate_obj_id


class MedicationV2(models.Model):
    class Meta:
        app_label = "medication"
        db_table = "medication_v2"
        indexes = [
            models.Index(fields=["mongoId"]),
        ]

    id = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True, default=generate_obj_id)
    enabled = models.BooleanField(default=False)
    userId = models.CharField(max_length=24)
    deploymentId = models.CharField(max_length=24)
    moduleId = models.CharField()
    moduleConfigId = models.CharField(max_length=24)
    name = models.CharField()
    coding = models.JSONField(null=True, blank=True, default=list)
    dosage = models.FloatField(null=True, blank=True, default=None, validators=[MinValueValidator(0.0)])
    unit = models.CharField(null=True, blank=True, default=None, max_length=40)
    customUnit = models.CharField(null=True, blank=True, default=None, max_length=40)
    indication = models.JSONField(null=True, blank=True, default=None)
    isNotificationEnabled = models.BooleanField(default=False)
    maxDosage = models.FloatField(null=True, blank=True, default=None, validators=[MinValueValidator(0.0)])
    schedule = models.JSONField(null=True, blank=True, default=None)
    adherence = models.JSONField(null=True, blank=True, default=None)
    adherences = models.JSONField(null=True, blank=True, default=list)
    reason = models.JSONField(null=True, blank=True, default=None)
    submitterId = models.CharField(max_length=24)
    submitterUserType = models.CharField(
        null=True,
        blank=True,
        default=None,
        choices=[(e.value, e.value) for e in SubmitterUserType],
    )
    startDate = models.DateField(null=True, blank=True, default=None)
    endDate = models.CharField(null=True, blank=True, default=None)
    updateDateTime = models.DateTimeField(null=True, blank=True, default=None)
    createDateTime = models.DateTimeField(null=True, blank=True, default=None)
    additionalFields = models.JSONField(null=True, blank=True, default=None)
    mealSchedule = models.JSONField(null=True, blank=True, default=None)
    connectedModules = models.JSONField(null=True, blank=True, default=None)
    groupId = models.CharField(null=True, blank=True)
