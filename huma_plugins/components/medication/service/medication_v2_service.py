from datetime import datetime

from huma_plugins.components.medication.dtos.medication import MedicationChangeType
from huma_plugins.components.medication.dtos.medication_history import (
    MedicationHistoryV2DTO,
    RegimenChange,
)
from huma_plugins.components.medication.dtos.medication_log import MedicationLogDTO
from huma_plugins.components.medication.dtos.medication_v2 import (
    MedicationReasonUpdated,
    MedicationV2DTO,
    SubmitterUserType,
)
from huma_plugins.components.medication.exceptions import MaxMedicationCreated
from huma_plugins.components.medication.repository.medication_repository_v2 import (
    MedicationV2Repository,
)
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.common.common_models.common_models import CaptureChange
from sdk.common.utils.common_functions_utils import find
from sdk.common.utils.inject import autoparams


class MedicationV2Service:
    MAX_ACTIVE_MEDICATIONS = 20

    @autoparams()
    def __init__(self, repo: MedicationV2Repository):
        self.repo = repo

    def create_medication(self, medication: MedicationV2DTO):
        self._check_max_number_of_medication_exceeded(medication.userId, medication.deploymentId)
        medication_id = self.repo.create_medication(medication)
        medication_history = self._build_new_medication_history(medication, medication_id)
        self.create_medication_history(medication_history)
        return medication_id

    def update_medication(self, medication: MedicationV2DTO, remove_max_dosage: bool):
        existing_medication: MedicationV2DTO = self.retrieve_medication_by_id(medication.id)
        new_medication = self.repo.update_medication(medication, remove_max_dosage)
        medication_history = self._build_updated_medication_history(medication, existing_medication)
        self.create_medication_history(medication_history)
        return new_medication

    def delete_user_medication(self, user_id: str):
        return self.repo.delete_user_medication(user_id=user_id)

    def delete_user_medication_logs(self, user_id: str):
        return self.repo.delete_user_medication_logs(user_id=user_id)

    def delete_user_medication_history(self, user_id: str):
        return self.repo.delete_user_medication_history(user_id=user_id)

    def disable_medications(self, user_id: str, submitter: AuthorizedUser) -> list[str]:
        reason = MedicationReasonUpdated.from_dict(
            {
                MedicationReasonUpdated.REASON: "This medication was deleted as part of patient offboarding",
                MedicationReasonUpdated.CLINICIAN_ID: submitter.id,
                MedicationReasonUpdated.CLINICIAN_NAME: submitter.user.get_full_name(),
            }
        )
        submitter_type = submitter.user_type().upper()
        return self.repo.disable_medications(user_id, reason, submitter_type)

    def retrieve_medication_log_by_event_id_and_start_datetime_and_source(
        self, event_id: str, start_datetime: datetime, source: str
    ) -> MedicationLogDTO:
        return self.repo.retrieve_medication_log_by_event_id_and_start_datetime_and_source(
            event_id=event_id, start_datetime=start_datetime, source=source
        )

    def retrieve_medications(
        self,
        user_id: str,
        deployment_id: str,
        enabled: bool,
        hide_meal_based: bool = False,
        skip: int = 0,
        limit: int | None = None,
    ) -> tuple[list[MedicationV2DTO], int]:
        return self.repo.retrieve_medications(
            user_id=user_id,
            deployment_id=deployment_id,
            enabled=enabled,
            hide_meal_based=hide_meal_based,
            skip=skip,
            limit=limit,
        )

    def retrieve_medication_by_id(self, medication_id: str) -> MedicationV2DTO:
        return self.repo.retrieve_medication_by_id(medication_id=medication_id)

    def retrieve_user_medication_logs(self, user_id, **filters):
        return self.repo.retrieve_medication_logs(user_id, **filters)

    def count_medications(self, user_id: str, **filters):
        return self.repo.count_medications(user_id, **filters)

    def create_medication_log(self, log: MedicationLogDTO):
        return self.repo.create_medication_logs([log])[0]

    def create_medication_logs(self, logs: list[MedicationLogDTO]):
        return self.repo.create_medication_logs(logs)

    def update_medication_log(self, log: MedicationLogDTO):
        return self.repo.update_medication_log(log)

    def delete_medication_log(self, log: MedicationLogDTO):
        return self.repo.delete_medication_log(log)

    def create_medication_history(self, medication_history: MedicationHistoryV2DTO) -> str:
        return self.repo.create_medication_history(medication_history=medication_history)

    def retrieve_medications_history(self, user_id: str, **filters):
        return self.repo.retrieve_medications_history(user_id=user_id, **filters)

    def retrieve_medication_history(
        self,
        medication_id: str,
        user_id: str,
        deployment_id: str,
        user_type: str | None = None,
        change_types: list[str] | None = None,
        start_datetime: datetime | None = None,
        end_datetime: datetime | None = None,
        custom_filters: object | None = None,
    ) -> tuple[list[MedicationHistoryV2DTO], int]:
        return self.repo.retrieve_medication_history(
            medication_id=medication_id,
            user_id=user_id,
            deployment_id=deployment_id,
            user_type=user_type,
            change_types=change_types,
            start_datetime=start_datetime,
            end_datetime=end_datetime,
            custom_filters=custom_filters,
        )

    def set_medication_histories_as_seen(
        self,
        user_id: str,
        deployment_id: str | None = None,
        user_type: str | None = None,
    ):
        user_type = user_type or SubmitterUserType.MANAGER.value
        return self.repo.set_medication_histories_as_seen(user_id, deployment_id, user_type)

    def _check_max_number_of_medication_exceeded(self, user_id: str, deployment_id: str):
        total_count = self.repo.count_medications(
            user_id=user_id,
            deployment_id=deployment_id,
            enabled=True,
        )
        if total_count >= self.MAX_ACTIVE_MEDICATIONS:
            raise MaxMedicationCreated

    @staticmethod
    def _build_new_medication_history(medication: MedicationV2DTO, _id: str) -> MedicationHistoryV2DTO:
        medication_history_dict = {
            MedicationHistoryV2DTO.USER_ID: medication.userId,
            MedicationHistoryV2DTO.DEPLOYMENT_ID: medication.deploymentId,
            MedicationHistoryV2DTO.MEDICATION_ID: _id,
            MedicationHistoryV2DTO.MEDICATION_NAME: medication.name,
            MedicationHistoryV2DTO.CHANGE_TYPE: MedicationChangeType.MEDICATION_CREATE.value,
            MedicationHistoryV2DTO.USER_TYPE: medication.submitterUserType,
        }
        if frequency := medication.regimen_frequency:
            regimen = {
                RegimenChange.FREQUENCY: {
                    CaptureChange.OLD: "",
                    CaptureChange.NEW: frequency.name,
                }
            }
            medication_history_dict[MedicationHistoryV2DTO.REGIMEN] = regimen
        return MedicationHistoryV2DTO.from_dict(medication_history_dict, ignore_none=True)

    def _build_updated_medication_history(
        self, medication: MedicationV2DTO, existing_medication: MedicationV2DTO
    ) -> MedicationHistoryV2DTO:
        medication_history_dict = {
            MedicationHistoryV2DTO.USER_ID: medication.userId,
            MedicationHistoryV2DTO.DEPLOYMENT_ID: medication.deploymentId,
            MedicationHistoryV2DTO.MEDICATION_ID: medication.id,
            MedicationHistoryV2DTO.MEDICATION_NAME: medication.name or existing_medication.name,
            MedicationHistoryV2DTO.USER_TYPE: medication.submitterUserType or existing_medication.submitterUserType,
            MedicationHistoryV2DTO.REASON: medication.reason,
        }
        if medication.enabled is not False:
            medication_history_dict[MedicationHistoryV2DTO.CHANGE_TYPE] = MedicationChangeType.MEDICATION_UPDATE.value
        else:
            medication_history_dict[MedicationHistoryV2DTO.CHANGE_TYPE] = MedicationChangeType.MEDICATION_DELETE.value

        if frequency := medication.regimen_frequency:
            medication_log = self._last_medication_log_for_frequency(medication_history_dict)
            prev_frequency = medication_log.frequency.new if medication_log is not None else ""
            regimen = {
                RegimenChange.FREQUENCY: {
                    CaptureChange.OLD: prev_frequency,
                    CaptureChange.NEW: frequency.name,
                }
            }
            medication_history_dict[MedicationHistoryV2DTO.REGIMEN] = regimen
        return MedicationHistoryV2DTO.from_dict(medication_history_dict, ignore_none=True)

    def _last_medication_log_for_frequency(self, medication_history_dict) -> MedicationHistoryV2DTO | None:
        medication_histories, _ = self.retrieve_medication_history(
            medication_id=str(medication_history_dict[MedicationHistoryV2DTO.MEDICATION_ID]),
            user_id=str(medication_history_dict[MedicationHistoryV2DTO.USER_ID]),
            deployment_id=str(medication_history_dict[MedicationHistoryV2DTO.DEPLOYMENT_ID]),
            custom_filters={"must_not_null": [MedicationHistoryV2DTO.REGIMEN]},
        )
        r = find(
            lambda med: getattr(med, MedicationHistoryV2DTO.REGIMEN),
            medication_histories[::-1],
        )
        return r
