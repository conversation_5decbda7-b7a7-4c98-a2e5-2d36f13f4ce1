from datetime import datetime
from enum import Enum

from dateutil.relativedelta import relativedelta
from i18n import t

from huma_plugins.components.medication.dtos.medication_schedule_event import (
    MedicationScheduleEvent,
    get_open_module_details,
)
from huma_plugins.components.medication.dtos.medication_v2 import (
    MedicationNotificationAction,
    MedicationV2DTO,
    SubmitterUserType,
)
from huma_plugins.components.medication.router.medication_request_v2 import (
    BaseMedicationRequestObjectV2,
    RetrieveMedicationCalendarRequestObject,
    RetrieveMedicationsCalendarRequestObject,
)
from huma_plugins.components.medication.service.medication_service import MedicationService
from huma_plugins.components.medication.service.medication_v2_service import MedicationV2Service
from huma_plugins.components.medication.utils import medication_to_scheduled_events
from sdk.authorization.dtos.role.role import RoleDTO
from sdk.authorization.dtos.user import UnseenFlags
from sdk.authorization.services.authorization import AuthorizationService
from sdk.calendar.service.calendar_service import CalendarService
from sdk.common.adapter.email_adapter import PercentageTemplate
from sdk.common.push_notifications.push_notifications_utils import prepare_and_send_push_notification
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.date_utils import localize_from_utc
from sdk.common.utils.inject import autoparams
from sdk.common.utils.validators import remove_none_values
from sdk.module_result.dtos.unseen_result import UnseenResultDTO
from sdk.module_result.repository.unseen_result_repository import UnseenResultsRepository
from .retrieve_medication_calendar_use_case_v1 import RetrieveMedicationCalendarUseCase
from .retrieve_medications_calendar_use_case_v1 import RetrieveMedicationsCalendarUseCase

medication_notification_body_keys = {
    MedicationNotificationAction.MEDICATION_ADDED: "Medication.Notification.medicationAdded.body",
    MedicationNotificationAction.MEDICATION_UPDATED: "Medication.Notification.medicationUpdated.body",
    MedicationNotificationAction.MEDICATION_REMOVED: "Medication.Notification.medicationRemoved.body",
}


class UnseenChangeType(Enum):
    MEDICATION_CREATED = "MEDICATION_CREATED"
    MEDICATION_DELETED = "MEDICATION_DELETED"
    DOSAGE_EXCEEDED = "DOSAGE_EXCEEDED"


class BaseMedicationUseCase(UseCase):
    def __init__(self):
        self._service: MedicationService = MedicationService()

    def process_request(self, request_object):
        raise NotImplementedError


class BaseMedicationUseCaseV2(UseCase):
    request_object: BaseMedicationRequestObjectV2

    @autoparams()
    def __init__(self, unseen_repo: UnseenResultsRepository):
        self._service: MedicationV2Service = MedicationV2Service()
        self._auth_service: AuthorizationService = AuthorizationService()
        self._calendar_service: CalendarService = CalendarService()
        self._unseen_repo = unseen_repo

    def process_request(self, request_object):
        raise NotImplementedError

    def _create_events(self, medication: MedicationV2DTO):
        timezone = self.request_object.user.timezone or "UTC"
        events = medication_to_scheduled_events(medication, timezone)
        notification_data = self.request_object.moduleConfig.notificationData

        for event in events:
            event.title = notification_data.title
            event.description = notification_data.body
            self._calendar_service.create_event(event, timezone)

    def _notify_user(self, medication: MedicationV2DTO, action: MedicationNotificationAction):
        if medication.submitterUserType is not SubmitterUserType.MANAGER:
            return

        language = self._get_language()
        title = t("Medication.Notification.title", locale=language)

        body = PercentageTemplate(t(medication_notification_body_keys[action], locale=language))
        body = body.safe_substitute(name=medication.name)

        if medication.mealSchedule:
            action = MedicationNotificationAction.OPEN_MODULE_DETAILS
            module_id, module_config_id = get_open_module_details(medication)
        else:
            module_id, module_config_id = medication.moduleId, medication.moduleConfigId

        notification_data = {
            "action": action.value,
            MedicationV2DTO.MODULE_ID: module_id,
            MedicationV2DTO.MODULE_CONFIG_ID: module_config_id,
        }

        prepare_and_send_push_notification(
            user_id=medication.userId,
            action=action.value,
            notification_template={"title": title, "body": body},
            notification_data=remove_none_values(notification_data),
            run_async=True,
        )

    def _mark_unseen_user_changes(self, medication: MedicationV2DTO):
        if medication.submitterUserType is SubmitterUserType.MANAGER:
            return

        self._create_or_update_unseen_result(
            medication,
            UnseenChangeType.MEDICATION_CREATED,
            self.request_object.user.timezone,
        )

    def _get_language(self) -> str:
        if language := self.request_object.user.language:
            return language
        return self.request_object.deployment.language

    def _create_or_update_unseen_result(
        self, medication: MedicationV2DTO, change_type: UnseenChangeType, time_zone: str
    ):
        unseen_result = {
            UnseenResultDTO.USER_ID: medication.userId,
            UnseenResultDTO.MODULE_ID: medication.moduleId,
            UnseenResultDTO.MODULE_CONFIG_ID: medication.moduleConfigId,
            UnseenResultDTO.DEPLOYMENT_ID: medication.deploymentId,
            UnseenResultDTO.PRIMITIVE_NAME: MedicationV2DTO.get_primitive_name(),
            UnseenResultDTO.EXTRA: {
                "changeType": change_type.value,
                MedicationV2DTO.NAME: medication.name,
                MedicationV2DTO.UNIT: medication.unit,
                MedicationV2DTO.CUSTOM_UNIT: medication.customUnit,
            },
            UnseenResultDTO.FLAGS: {
                UnseenFlags.RED: 0,
                UnseenFlags.AMBER: 0,
                UnseenFlags.GRAY: 1,
            },
        }
        result = UnseenResultDTO.from_dict(unseen_result)
        self._unseen_repo.create_or_update(result)
        self._update_unseen_flags(medication.userId, time_zone)

    def _update_unseen_flags(self, user_id: str, time_zone: str):
        deployment_module_config_ids = [
            mc.id
            for mc in self.request_object.deployment.moduleConfigs
            if not mc.is_filled_by(RoleDTO.UserType.MANAGER)
        ]
        unseen_flags = self._unseen_repo.calculate_unseen_flags(
            user_id=user_id,
            time_zone=time_zone,
            module_config_ids=deployment_module_config_ids,
        )
        self._auth_service.update_unseen_flags(user_id, unseen_flags)

    def _retrieve_users_medication_calendar_for_adherence(
        self, medication_id: str = None
    ) -> list[MedicationScheduleEvent]:
        end = localize_from_utc(datetime.utcnow(), self.request_object.user.timezone)
        start = end - relativedelta(days=7)
        if medication_id:
            request_object = RetrieveMedicationCalendarRequestObject(
                user=self.request_object.user,
                deploymentId=self.request_object.deploymentId,
                medicationId=medication_id,
                startDateTime=start,
                endDateTime=end,
            )
            response = RetrieveMedicationCalendarUseCase().execute(request_object)
        else:
            request_object = RetrieveMedicationsCalendarRequestObject(
                user=self.request_object.user,
                deploymentId=self.request_object.deploymentId,
                startDateTime=start,
                endDateTime=end,
            )
            response = RetrieveMedicationsCalendarUseCase().execute(request_object)
        return response.items
