from datetime import datetime

from huma_plugins.components.medication.dtos.medication_v2 import (
    MedicationNotificationAction as Action,
    MedicationV2DTO,
)
from huma_plugins.components.medication.router.medication_request_v2 import (
    CreateMedicationRequestObjectV2,
)
from huma_plugins.components.medication.use_case.base_medication_use_case import (
    BaseMedicationUseCaseV2,
)
from sdk.common.usecase.response_object import ResultIdResponseObject
from sdk.key_action.use_case.complete_key_action_use_case import (
    CompleteKeyActionUseCase,
)
from sdk.key_action.use_case.key_action_requests import (
    CompleteKeyActionRequestObject,
)


class CreateMedicationUseCaseV2(BaseMedicationUseCaseV2):
    MAX_ACTIVE_MEDICATIONS = 20

    request_object: CreateMedicationRequestObjectV2

    def process_request(self, request_object: CreateMedicationRequestObjectV2):
        medication = request_object.to_medication()
        medication_id = self._service.create_medication(medication)
        medication.id = medication_id
        self.complete_active_key_actions(medication)
        if medication.has_ranged_schedule or not (medication.schedule and medication.schedule.asNeeded):
            self._create_events(medication)

        self._notify_user(medication, action=Action.MEDICATION_ADDED)
        self._mark_unseen_user_changes(medication)

        return ResultIdResponseObject(medication_id)

    def complete_active_key_actions(self, medication: MedicationV2DTO):
        data = {
            CompleteKeyActionRequestObject.MODULE_ID: medication.moduleId,
            CompleteKeyActionRequestObject.MODULE_CONFIG_ID: medication.moduleConfigId,
            CompleteKeyActionRequestObject.USER: self.request_object.user,
            CompleteKeyActionRequestObject.CREATE_DATE_TIME: datetime.utcnow(),
        }
        request_object = CompleteKeyActionRequestObject.from_dict(data)
        CompleteKeyActionUseCase().execute(request_object)
