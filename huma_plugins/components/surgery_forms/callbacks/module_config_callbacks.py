from typing import Union

from sdk.deployment.events import (
    PreCreateModuleConfigEvent,
    PreUpdateModuleConfigEvent,
)
from sdk.module_result.dtos.module_config import ModuleConfig, FilledBy
from sdk.common.utils.convertible import ConvertibleClassValidationError


def validate_filled_by_property(
    event: Union[PreCreateModuleConfigEvent, PreUpdateModuleConfigEvent],
):
    filled_by = event.module_config.get_config_body().get(ModuleConfig.FILLED_BY)
    if filled_by and filled_by not in (FilledBy.USER, FilledBy.MANAGER):
        raise ConvertibleClassValidationError(
            f"Field [configBody.filledBy] with value [{filled_by}] has invalid value]"
        )
