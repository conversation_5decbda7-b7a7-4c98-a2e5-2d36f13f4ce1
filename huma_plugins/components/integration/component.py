import logging
from typing import Optional, Union, List

from flask import Blueprint

from huma_plugins.components.integration.callbacks.integration_callback import (
    integration_callback,
    integration_callback_create_user,
    integration_callback_delete_user,
    integration_callback_update_user,
    integration_callback_post_move_user_event,
    integration_callback_post_offboard_event,
    integration_callback_post_reactivate_user_event,
    integration_callback_post_sign_up_event,
)
from huma_plugins.components.integration.config.config import IntegrationConfig
from huma_plugins.components.integration.repository.integration_repository import (
    IntegrationRepository,
)
from huma_plugins.components.integration.repository.postgres_integration_repository import (
    PostgresIntegrationRepository,
)
from huma_plugins.components.integration.repository.postgres_patient_profile_repository import (
    PostgresPatientProfileRepository,
)
from huma_plugins.components.integration.repository.patient_profile_repository import (
    PatientProfileRepository,
)
from huma_plugins.components.integration.router.integration_router import (
    integration_route,
    integration_ehr_route,
)
from sdk.auth.events.delete_user_event import DeleteUserEvent
from sdk.authorization.di.components import PostCreateUserEvent
from sdk.authorization.events import (
    PostUserProfileUpdateEvent,
    PostUserOffBoardEvent,
    PostUserReactivationEvent,
)
from sdk.authorization.events.post_move_user_event import PostMoveUserEvent
from sdk.authorization.events.post_user_onboard_event import UserOnboardedEvent
from sdk.common.adapter.event_bus_adapter import EventBusAdapter
from sdk.common.utils.inject import Binder, autoparams
from sdk.module_result.event_bus.post_create_module_results_batch_event import (
    PostCreateModuleResultBatchEvent,
)
from sdk.phoenix.component_manager import PhoenixBaseComponent
from sdk.phoenix.config.server_config import PhoenixServerConfig

logger = logging.getLogger(__name__)


class IntegrationComponent(PhoenixBaseComponent):
    config_class = IntegrationConfig
    tag_name = "integration"

    def bind(self, binder: Binder, config: PhoenixServerConfig):
        binder.bind_to_provider(IntegrationRepository, lambda: PostgresIntegrationRepository())
        logger.debug("IntegrationRepository bind to PostgresIntegrationRepository")

        binder.bind_to_provider(PatientProfileRepository, lambda: PostgresPatientProfileRepository())
        logger.debug("PatientProfileRepository bind to PostgresPatientProfileRepository")

    @property
    def blueprint(self) -> Optional[Union[Blueprint, List[Blueprint]]]:
        return [integration_route, integration_ehr_route]

    @autoparams()
    def post_setup(self, event_bus: EventBusAdapter):
        event_bus.subscribe(PostCreateModuleResultBatchEvent, integration_callback)
        event_bus.subscribe(PostCreateUserEvent, integration_callback_create_user)
        event_bus.subscribe(PostUserProfileUpdateEvent, integration_callback_update_user)
        event_bus.subscribe(DeleteUserEvent, integration_callback_delete_user)
        event_bus.subscribe(PostUserOffBoardEvent, integration_callback_post_offboard_event)
        event_bus.subscribe(PostUserReactivationEvent, integration_callback_post_reactivate_user_event)
        event_bus.subscribe(PostMoveUserEvent, integration_callback_post_move_user_event)
        event_bus.subscribe(UserOnboardedEvent, integration_callback_post_sign_up_event)

        super().post_setup()
