import datetime
from pathlib import Path

from huma_plugins.components.export.component import ExportComponent
from huma_plugins.components.integration.adapters.generic.ehr_api import EHRApi
from huma_plugins.components.integration.component import IntegrationComponent
from huma_plugins.components.integration.dtos.generic_ehr import GenericEHRDTO
from huma_plugins.components.integration.dtos.patient_profile import PatientProfileDTO
from huma_plugins.components.integration.repository.patient_profile_repository import (
    PatientProfileRepository,
)
from huma_plugins.components.integration.tasks import (
    search_patient_profile_in_ehr_task,
    update_patient_profile_status,
    update_patient_profile_task,
)
from huma_plugins.components.integration.tests.IntegrationTests.sample_data import (
    sample_flowsheet_data,
    sample_flowsheet_data_missing_value,
    sample_generic_ehr,
    sample_integration,
    sample_search_query_data,
    sample_search_query_no_match,
    sample_search_query_wrong_data,
)
from huma_plugins.tests.plugin_test_case import PluginsTestCase
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.common.utils import inject
from sdk.deployment.component import DeploymentComponent
from sdk.module_result.component import ModuleResultComponent
from sdk.module_result.modules import BloodPressureModule
from sdk.organization.component import OrganizationComponent
from sdk.versioning.component import VersionComponent

DEPLOYMENT_ID = "5f652a9661c37dd829c8d23a"
DEPLOYMENT_ID2 = "5ed8ae76cf99540b259a7315"
DEPLOYMENT_ID3 = "61926cbe9cb844829c967f8a"
USER_EMAIL = "<EMAIL>"
USER_EMAIL2 = "<EMAIL>"
USER_ID2 = "615db4dd92a28f0cee2e14c1"
USER_ID3 = "615db4dd92a28f0cee2e14c3"

GIVEN_NAME = "Timothy"
FAMILY_NAME = "Bixby"

USER_ID = "5e8f0c74b50aa9656c34789b"
BIRTH_DATE = datetime.datetime(2008, 1, 6)


class IntegrationEHRApiTestCase(PluginsTestCase):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        IntegrationComponent(),
        ExportComponent(),
        OrganizationComponent(),
        ModuleResultComponent(additional_modules=[BloodPressureModule()]),
        VersionComponent(server_version="1.0.0", api_version="1.0.0"),
    ]

    fixtures = [Path(__file__).parent.joinpath("fixtures/data.json")]

    migration_path: str = str(Path(__file__).parent.parent.parent) + "/migrations"

    def setUp(self):
        super().setUp()

        self.data = sample_flowsheet_data
        self.wrong_data = sample_flowsheet_data_missing_value

        self.search_query = sample_search_query_data
        self.search_wrong_data = sample_search_query_wrong_data
        self.search_no_match = sample_search_query_no_match

        generic_ehr = GenericEHRDTO.from_dict(sample_generic_ehr)
        sample_integration.generic_ehr = generic_ehr

        # send data to redox sandbox and check if we get successful response
        self.ehr_api = EHRApi(sample_integration)

    def test_new_flowsheet_with_oauth2_authType_required_field_missing(self):
        self.ehr_api._integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.LEGACY

        response = self.ehr_api.new_flowsheet(self.wrong_data)

        self.assertTrue("Errors" in response["Meta"])

    def test_new_flowsheet_with_legacy_authType(self):
        self.ehr_api._integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.LEGACY

        response = self.ehr_api.new_flowsheet(self.data)

        self.assertFalse("Errors" in response["Meta"])
        self.assertIsNotNone(response["Meta"]["Source"]["ID"])

    def test_new_flowsheet_with_wrong_auth(self):
        self.ehr_api._integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.LEGACY
        self.ehr_api._api_key = "fdsafdsa"

        with self.assertRaises(Exception):
            self.ehr_api.new_flowsheet(self.data)

    def test_patient_search_with_legacy_authType(self):
        self.ehr_api._integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.LEGACY

        response = self.ehr_api.query_patient_search(self.search_query)

        self.assertTrue("Identifiers" in response)

    def test_patient_search_with_legacy_authType_no_destination(self):
        self.ehr_api._integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.LEGACY
        del self.ehr_api._integration.generic_ehr.destination[1]

        response = self.ehr_api.query_patient_search(self.search_query)

        self.assertIsNone(response)

    def test_patient_search_with_legacy_authType_required_field_missing(self):
        self.ehr_api._integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.LEGACY

        response = self.ehr_api.query_patient_search(self.search_wrong_data)

        self.assertIsNone(response)

    def test_patient_search_with_legacy_authType_no_match(self):
        self.ehr_api._integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.LEGACY

        response = self.ehr_api.query_patient_search(self.search_no_match)

        self.assertIsNone(response)

    def test_search_patient_profile_by_id_in_ehr_task_match_deployment(self):
        search_patient_profile_in_ehr_task(
            USER_EMAIL,
            DEPLOYMENT_ID,
            GIVEN_NAME,
            FAMILY_NAME,
            BIRTH_DATE.strftime("%Y-%m-%d"),
            USER_ID,
        )
        patient_profile_repo = inject.instance(PatientProfileRepository)
        patient_profile = patient_profile_repo.retrieve_patient_profile_by_email(USER_EMAIL)

        self.assertEqual(patient_profile.status.value, "ON_BOARDED")

    def test_search_patient_profile_by_id_in_ehr_task_match_organization(self):
        search_patient_profile_in_ehr_task(
            USER_EMAIL,
            DEPLOYMENT_ID3,
            GIVEN_NAME,
            FAMILY_NAME,
            BIRTH_DATE.strftime("%Y-%m-%d"),
            USER_ID,
        )
        patient_profile_repo = inject.instance(PatientProfileRepository)
        patient_profile = patient_profile_repo.retrieve_patient_profile_by_email(USER_EMAIL)

        self.assertEqual(patient_profile.status.value, "ON_BOARDED")

    def test_update_patient_profile_task(self):
        update_patient_profile_task(USER_EMAIL2, USER_ID2)

        patient_profile_repo = inject.instance(PatientProfileRepository)
        patient_profile = patient_profile_repo.retrieve_patient_profile_by_email(USER_EMAIL2)

        self.assertEqual(patient_profile.status.value, "ON_BOARDED")

    def test_update_patient_profile_status_to_offboarded(self):
        update_patient_profile_status(USER_ID2, PatientProfileDTO.Status.OFF_BOARDED.value)

        patient_profile_repo = inject.instance(PatientProfileRepository)
        patient_profile = patient_profile_repo.retrieve_patient_profile_by_user_id(USER_ID2)

        self.assertEqual(patient_profile.status.value, "OFF_BOARDED")

    def test_update_patient_profile_status_to_onboarded(self):
        update_patient_profile_status(USER_ID3, PatientProfileDTO.Status.ON_BOARDED.value)

        patient_profile_repo = inject.instance(PatientProfileRepository)
        patient_profile = patient_profile_repo.retrieve_patient_profile_by_user_id(USER_ID3)

        self.assertEqual(patient_profile.status.value, "ON_BOARDED")

    def test_new_flowsheet_with_oauth2_authType(self):
        self.ehr_api._integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.OAUTH2

        response = self.ehr_api.new_flowsheet(self.data)

        self.assertFalse("Errors" in response["Meta"])
        self.assertIsNotNone(response["Meta"]["Source"]["ID"])

    def test_new_flowsheet_with_oauth2_basic_authType(self):
        from unittest.mock import patch

        self.ehr_api._integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.OAUTH2_BASIC
        self.ehr_api._integration.generic_ehr.client_id = "test_client_id"
        self.ehr_api._integration.generic_ehr.client_secret = "test_client_secret"

        with patch(
            "huma_plugins.components.integration.adapters.generic.ehr_api.EHRApi.login_basic", return_value=True
        ):
            with patch("requests.Session.post") as mock_post:
                from unittest.mock import MagicMock

                mock_response = MagicMock()
                mock_response.ok = True
                mock_response.json.return_value = {"Meta": {"Source": {"ID": "test_source_id"}}}
                mock_post.return_value = mock_response

                response = self.ehr_api.new_flowsheet(self.data)

                self.assertFalse("Errors" in response["Meta"])
                self.assertIsNotNone(response["Meta"]["Source"]["ID"])
