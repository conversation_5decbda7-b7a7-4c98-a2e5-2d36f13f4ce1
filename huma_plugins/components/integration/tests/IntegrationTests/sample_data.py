from huma_plugins.components.integration.dtos.integration import IntegrationDTO

sample_webhook_url = "https://webhook.site/64a6d6f5-34f3-450c-9497-63ffd468a9e9"
sample_expected_webhook_event_json = '{"id": "e82eff3858144843a0dcd69ca143919b", "type": "MODULE_RESULT", "payload": {"primitives": [{"userId": "615db4dd92a28f0cee2e14c1", "moduleId": "BloodPressure", "moduleConfigId": "617a6ae12ad9606b933e3db7", "deploymentId": "617a6ade2ad9606b933e3d8e", "version": 0, "deviceName": "iOS", "isAggregated": false, "startDateTime": "2021-11-24T10:09:17.694000Z", "createDateTime": "2021-11-24T10:09:17.845000Z", "submitterId": "615db4dd92a28f0cee2e14c1", "server": {"hostUrl": "vb-ppserver.ngrok.io", "server": "1.17.0", "api": "V1"}, "diastolicValue": 80, "systolicValue": 92, "diastolicValueUnit": "mmHg", "systolicValueUnit": "mmHg", "_cls": "BloodPressure", "user": {"id": "615db4dd92a28f0cee2e14c1", "updateDateTime": "2021-11-11T19:03:16.148000Z", "createDateTime": "2021-10-06T14:38:21.150000Z", "givenName": "Mahdi", "familyName": "Biria", "gender": "MALE", "biologicalSex": "MALE", "dateOfBirth": "1988-04-04", "email": "<EMAIL>", "contactEmail": "<EMAIL>", "timezone": "UTC", "enrollmentId": 354, "language": "en", "finishedOnboarding": true, "stats": {"taskCompliance": {"current": 0, "total": 14, "due": 0, "updateDateTime": "2021-11-11T19:03:16.121000Z", "percentage": 0}}, "age": 35}}], "module_id": "BloodPressure", "deployment_id": "61926cbe9cb844829c967f8a", "device_name": "iOS", "module_config_id": "617a6ae12ad9606b933e3db7", "patient_id": "**********"}}'
sample_expected_webhook_header = {"Content-Type": "application/json; charset=UTF-8"}
sample_event_dict = {
    "userId": "615db4dd92a28f0cee2e14c1",
    "moduleId": "BloodPressure",
    "moduleConfigId": "617a6ae12ad9606b933e3db7",
    "deploymentId": "61926cbe9cb844829c967f8a",
    "deviceName": "iOS",
    "startDateTime": "2021-11-09T14:28:37.630000Z",
    "primitives": [
        {
            "id": "618a8595a57e07e2de456e33",
            "userId": "615db4dd92a28f0cee2e14c1",
            "moduleId": "BloodPressure",
            "moduleResultId": "31f79109948c469bba0b3e202960d961",
            "moduleConfigId": "617a6ae12ad9606b933e3db7",
            "deploymentId": "61926cbe9cb844829c967f8a",
            "version": 0,
            "deviceName": "iOS",
            "isAggregated": False,
            "isAverage": None,
            "startDateTime": "2021-11-09T14:28:37.630000Z",
            "endDateTime": None,
            "createDateTime": "2021-11-09T14:28:37.767489Z",
            "submitterId": "615db4dd92a28f0cee2e14c1",
            "correlationStartDateTime": None,
            "client": None,
            "server": {
                "hostUrl": "vb-ppserver.ngrok.io",
                "server": "1.16.0",
                "api": "V1",
            },
            "ragThreshold": None,
            "flags": None,
            "diastolicValue": 80,
            "systolicValue": 92,
            "diastolicValueUnit": "mmHg",
            "systolicValueUnit": "mmHg",
            "_cls": "BloodPressure",
        }
    ],
}

sample_kafka_config = {
    "url": "pkc-l6wr6.europe-west2.gcp.confluent.cloud:9092",
    "authType": "PLAIN",
    "saslUsername": "OYMYIEH7QWGXVLIK",
    "saslPassword": "",
    "inbound": {"topic": "order_in", "eventTypes": ["ORDER", "DEMOGRAPHIC_UPDATE"]},
    "outbound": {
        "topic": "module-result-out",
        "eventTypes": ["SIGN_UP", "MODULE_RESULT"],
        "extraArgs": {"retries": "3", "retry_backoff_ms": "10000"},
    },
}

sample_integration = IntegrationDTO(
    id="63f785dc24588002f8e93e19",
    name="Generic EHR Integration",
    integrationType=IntegrationDTO.Type.GENERICEHR,
    organizationIds=["5f652a9661c37dd829c8d23a"],
    deploymentIds=["5f652a9661c37dd829c8d23a", "617a6ade2ad9606b933e3d8e"],
    moduleNames=None,
    excludedModuleNames=None,
)

sample_generic_ehr = {
    "url": "https://api.redoxengine.com",
    "authType": "OAUTH2",
    "api_key": "724c91b2-7b4a-44b6-b551-56abd8dae222",
    "api_secret": "O0Yndmg89NUCGH98TPwArQqL7wqggFjKr600WXIqRYEnTwmtLH0kg7lB2P2QU2Fd9pTN7Poj",
    "key_id": "P0ImOh0_xYc3aRosRrrcwucsGTj9R_GLGFe6DYoof8c",
    "client_id": "d9eae604-e1e4-45a9-b307-4bfbd9ffefa4",
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    "source_id": "8e489e03-7dcd-4735-a879-da16b017ec85",
    "source_name": "Redox API Endpoint",
    "destination": [
        {
            "destination_id": "af394f14-b34a-464f-8d24-895f370af4c9",
            "destination_name": "Flowsheet",
            "destination_func": "new_flowsheet",
            "is_test": False,
        },
        {
            "destination_id": "0f4bd1d1-451d-4351-8cfd-b767d1b488d6",
            "destination_name": "PatientSearch",
            "destination_func": "query_patient_search",
            "is_test": False,
        },
    ],
    "logs_id": "d9f5d293-7110-461e-a875-3beb089e79f3",
    "logs_attempt_id": "925d1617-2fe0-468c-a14c-f8c04b572c54",
    "retry": 1,
}

sample_flowsheet_data = {
    "Patient": {
        "Identifiers": [
            {"ID": "**********", "IDType": "MR"},
        ],
        "Demographics": {
            "FirstName": "Timothy",
            "LastName": "Bixby",
            # "DOB": "2008-01-06",
        },
    },
    "Observations": [
        {
            "DateTime": "2015-08-13T21:08:57.581Z",
            "Value": "110.00",
            "ValueType": "Numeric",
            "Units": "mmHg",
            "Code": "Systolic",
            "Codeset": "RedoxEMR",
            "Description": None,
            "Status": None,
            "AbnormalFlag": None,
        },
        {
            "DateTime": "2015-08-13T21:08:57.581Z",
            "Value": "90.00",
            "ValueType": "Numeric",
            "Units": "mmHg",
            "Code": "Diastolic",
            "Codeset": "RedoxEMR",
            "Description": None,
            "Status": None,
            "AbnormalFlag": None,
        },
    ],
}

sample_search_query_data = {
    "Demographics": {
        "FirstName": "Timothy",
        "LastName": "Bixby",
        "DOB": "2008-01-06",
    }
}

sample_search_query_wrong_data = {"Demographics": {"FirstName": "Timothy", "LastName": "Bixby"}}

sample_search_query_no_match = {
    "Demographics": {
        "FirstName": "Timothy",
        "LastName": "Bixby",
        "DOB": "2009-01-06",
    }
}

sample_flowsheet_data_missing_value = {
    "Patient": {
        "Identifiers": [
            {"ID": "**********", "IDType": ""},
        ],
        "Demographics": {
            "FirstName": "Timothy",
            "LastName": "Bixby",
            # "DOB": "2008-01-06",
        },
    },
    "Observations": [
        {
            "DateTime": "2015-08-13T21:08:57.581Z",
            "Value": "110.00",
            "ValueType": "Numeric",
            "Units": "mmHg",
            "Code": "Systolic",
            "Codeset": "RedoxEMR",
            "Description": None,
            "Status": None,
            "AbnormalFlag": None,
        },
        {
            "DateTime": "2015-08-13T21:08:57.581Z",
            "Value": "90.00",
            "ValueType": "Numeric",
            "Units": "mmHg",
            "Code": "Diastolic",
            "Codeset": "RedoxEMR",
            "Description": None,
            "Status": None,
            "AbnormalFlag": None,
        },
    ],
}

sample_order = {
    "Meta": {
        "DataModel": "Order",
        "EventType": "New",
        "EventDateTime": "2023-01-26T14:59:09.217Z",
        "Source": {
            "ID": "7160cba1-9668-492c-b51c-60bbf3eaacab",
            "Name": "Receive Order",
        },
        "Destination": {"ID": "aef472f24-c35a-264f-3d21-695f270af1c9", "Name": "Huma"},
        "Message": {"ID": 9970},
    },
    "Order": {
        "ID": "1",
        "TransactionDateTime": None,
        "Procedure": {"Code": "003038", "Codeset": "L", "Description": "Urinalysis"},
    },
    "Patient": {
        "Identifiers": [{"ID": "12001", "IDType": "NHS"}],
        "Demographics": {
            "FirstName": "John",
            "MiddleName": "",
            "LastName": "Jones",
            "Mobile": "",
            "EmailAddresses": ["<EMAIL>"],
            "DOB": "1967-08-24",
        },
    },
}
