from copy import deepcopy
from unittest import TestCase, mock
from unittest.mock import <PERSON>Mock

from sdk.authorization.dtos.user import UserDTO
from sdk.common.adapter.event_bus_adapter import EventBusAdapter
from sdk.common.usecase.response_object import Response
from sdk.common.utils import inject
from sdk.deployment.repository.consent_repository import ConsentRepository
from sdk.deployment.repository.deployment_repository import DeploymentRepository
from sdk.deployment.repository.econsent_repository import EConsentRepository
from sdk.module_result.modules.modules_manager import ModulesManager
from sdk.phoenix.config.server_config import PhoenixServerConfig

from huma_plugins.components.integration.adapters import integration_adapter
from huma_plugins.components.integration.adapters.utils import PATIENT_ID, transform_event
from huma_plugins.components.integration.adapters.webhook_adapter import WebhookAdapter
from huma_plugins.components.integration.dtos.patient_profile import PatientProfileDTO
from huma_plugins.components.integration.tests.UnitTests.sample_data import sample_event
from huma_plugins.components.integration.tests.UnitTests.UnitTests.sample_data import sample_patient_profile

SAMPLE_ID = "65142c42d69b0b2ed651b250"
BASE_PATH_UTILS = "huma_plugins.components.integration.adapters.utils"


class WebhookAdapterTestCase(TestCase):
    def setUp(self):
        def bind(binder):
            binder.bind(DeploymentRepository, MagicMock())
            binder.bind(PhoenixServerConfig, MagicMock())
            binder.bind(EventBusAdapter, MagicMock())
            binder.bind(ModulesManager, MagicMock())
            binder.bind(ConsentRepository, MagicMock())
            binder.bind(EConsentRepository, MagicMock())

        inject.clear_and_configure(bind)

    def test_transform_event(self):
        auth_service = MagicMock()
        use_case = MagicMock()

        profile = PatientProfileDTO.from_dict(sample_patient_profile)
        use_case().execute.return_value = Response(value=profile)
        with (
            mock.patch(f"{BASE_PATH_UTILS}.AuthorizationService", auth_service),
            mock.patch(
                f"{BASE_PATH_UTILS}.get_consents_meta_data_with_deployment",
                lambda *args, **kwargs: (MagicMock(), MagicMock()),
            ),
            mock.patch(
                f"{BASE_PATH_UTILS}.exclude_no_user_data_and_attach_users_if_requested",
                MagicMock(),
            ),
            mock.patch(
                f"{BASE_PATH_UTILS}.FindPatientProfileByUserIdUseCase",
                use_case,
            ),
        ):
            event = deepcopy(sample_event)
            transform_event(
                event=event,
                deployment_repo=MagicMock(),
                server_config=MagicMock(),
            )
            retrieve_profile_method = auth_service().retrieve_simple_user_profile
            retrieve_profile_method.assert_called_with(SAMPLE_ID)
            user = UserDTO(id=SAMPLE_ID)
            auth_service().retrieve_simple_user_profile.return_value = user
            self.assertIn(PATIENT_ID, event)
            self.assertEqual(event[PATIENT_ID], "1234")

            adapter = WebhookAdapter(MagicMock())
            adapter.prepare_integration_data(event)
            self.assertIn(integration_adapter.PATIENT_ID, adapter._message)
