from datetime import datetime

from huma_plugins.components.integration.dtos.gcp_fhir import GCPFhirDTO
from huma_plugins.components.integration.dtos.integration import IntegrationDTO
from huma_plugins.components.integration.dtos.webhook import WebhookDTO
from sdk.module_result.dtos.primitives import Server
from sdk.module_result.modules.blood_pressure import BloodPressureDTO

sample_user_id = "615db4dd92a28f0cee2e14c1"
sample_deployment_id = "606c50a113dbea3656ff1bb0"
sample_module_config_id = "617a6ae12ad9606b933e3db7"
sample_module_result_id = "31f79109948c469bba0b3e202960d961"

sample_blood_pressure = BloodPressureDTO(
    id="618a8595a57e07e2de456e33",
    userId="615db4dd92a28f0cee2e14c1",
    moduleId="BloodPressure",
    moduleResultId="31f79109948c469bba0b3e202960d961",
    moduleConfigId="617a6ae12ad9606b933e3db7",
    deploymentId="606c50a113dbea3656ff1bb0",
    version=0,
    deviceName="iOS",
    isAggregated=False,
    startDateTime=datetime(2021, 11, 9, 14, 28, 37, 630000),
    endDateTime=None,
    createDateTime=datetime(2021, 11, 9, 14, 28, 37, 767489),
    submitterId="615db4dd92a28f0cee2e14c1",
    correlationStartDateTime=None,
    client=None,
    server=Server(hostUrl="vb-ppserver.ngrok.io", server="1.16.0", api="V1"),
    ragThreshold=None,
    flags=None,
    diastolicValue=80,
    systolicValue=92,
    diastolicValueUnit="mmHg",
    systolicValueUnit="mmHg",
)

sample_blood_glucose_dict = {
    "id": None,
    "userId": "615db4dd92a28f0cee2e14c1",
    "moduleId": "BloodGlucose",
    "moduleConfigId": "5f652ae161c37dd829c8d245",
    "moduleResultId": "b0fef05036594866996517c72c4b3ff2",
    "deploymentId": "5f652a9661c37dd829c8d23a",
    "version": 0,
    "deviceName": "iOS",
    "isAggregated": False,
    "startDateTime": "2022-04-13T14:35:46.516000Z",
    "endDateTime": None,
    "createDateTime": "2022-04-13T14:35:46.728000Z",
    "submitterId": "615db4dd92a28f0cee2e14c1",
    "correlationStartDateTime": None,
    "client": None,
    "server": {"hostUrl": "vb-ppserver.ngrok.io", "server": "1.21.0", "api": "V1"},
    "ragThreshold": {
        "BloodGlucose": {
            "value": {
                "color": "#FBCCD7",
                "severity": 3,
                "direction": "DECREASED",
                "isCustom": False,
            }
        }
    },
    "flags": {"red": 1, "amber": 0, "gray": 0},
    "value": 12.0,
    "valueUnit": "mmol/L",
    "originalValue": None,
    "originalUnit": None,
    "_cls": "BloodGlucose",
    "user": {
        "id": "615db4dd92a28f0cee2e14c1",
        "updateDateTime": "2022-04-13T14:35:46.887000Z",
        "createDateTime": "2021-10-06T14:38:21.150000Z",
        "lastSubmitDateTime": "2022-04-13T14:35:46.728000Z",
        "givenName": "Mahdi",
        "familyName": "Biria",
        "gender": "MALE",
        "biologicalSex": "MALE",
        "ethnicity": "OTHER_ETHNIC_GROUPS",
        "dateOfBirth": "1988-04-04",
        "email": "<EMAIL>",
        "height": 178.0,
        "extraCustomFields": {},
        "timezone": "Europe/London",
        "enrollmentId": 354,
        "boardingStatus": {
            "status": 0,
            "updateDateTime": "2022-01-06T10:51:27.051000Z",
        },
        "language": "ar",
        "finishedOnboarding": True,
        "stats": {
            "taskCompliance": {
                "current": 0,
                "total": 0,
                "due": 0,
                "updateDateTime": "2022-04-13T14:31:11.313000Z",
            }
        },
        "lastLoginDateTime": "2022-03-18T17:00:59.897953Z",
        "consent": {
            "id": "615dd2dd92a28f0cee2e14cb",
            "userId": "615db4dd92a28f0cee2e14c1",
            "consentId": "5f652ac661c37dd829c8d23b",
            "revision": 3,
            "givenName": "Test client",
            "middleName": "test",
            "familyName": "Test client",
            "signature": {"bucket": "bucket", "key": "key", "region": "eu"},
            "sharingOption": 1,
            "createDateTime": "2021-10-06T16:46:21.447000Z",
            "deploymentId": "5f652a9661c37dd829c8d23a",
        },
    },
}
sample_weight_dict = {
    "id": None,
    "userId": "615db4dd92a28f0cee2e14c1",
    "moduleId": "Weight",
    "moduleConfigId": "5f652ae161c37dd829c8d24a",
    "moduleResultId": "879f5ee23e5943b0ba659a9d9b8839f8",
    "deploymentId": "5f652a9661c37dd829c8d23a",
    "version": 0,
    "deviceName": "iOS",
    "isAggregated": False,
    "startDateTime": "2022-04-13T15:10:23.659000Z",
    "endDateTime": None,
    "createDateTime": "2022-04-13T15:10:23.889000Z",
    "submitterId": "615db4dd92a28f0cee2e14c1",
    "correlationStartDateTime": None,
    "client": None,
    "server": {"hostUrl": "vb-ppserver.ngrok.io", "server": "1.21.0", "api": "V1"},
    "ragThreshold": {
        "Weight": {
            "value": {
                "color": "#CBEBF0",
                "severity": 1,
                "direction": "NOCHANGE",
                "isCustom": False,
            }
        }
    },
    "flags": {"gray": 1, "amber": 0, "red": 0},
    "value": 80.0,
    "valueUnit": "kg",
    "originalValue": None,
    "originalUnit": None,
    "_cls": "Weight",
    "user": {
        "id": "615db4dd92a28f0cee2e14c1",
        "updateDateTime": "2022-04-13T15:10:24.083000Z",
        "createDateTime": "2021-10-06T14:38:21.150000Z",
        "lastSubmitDateTime": "2022-04-13T15:10:23.889000Z",
        "givenName": "Mahdi",
        "familyName": "Biria",
        "gender": "MALE",
        "biologicalSex": "MALE",
        "ethnicity": "OTHER_ETHNIC_GROUPS",
        "dateOfBirth": "1988-04-04",
        "email": "<EMAIL>",
        "height": 178.0,
        "extraCustomFields": {},
        "timezone": "Europe/London",
        "enrollmentId": 354,
        "boardingStatus": {
            "status": 0,
            "updateDateTime": "2022-01-06T10:51:27.051000Z",
        },
        "language": "ar",
        "finishedOnboarding": True,
        "stats": {
            "taskCompliance": {
                "current": 0,
                "total": 0,
                "due": 0,
                "updateDateTime": "2022-04-13T14:48:06.770000Z",
            }
        },
        "lastLoginDateTime": "2022-03-18T17:00:59.897953Z",
        "consent": {
            "id": "615dd2dd92a28f0cee2e14cb",
            "userId": "615db4dd92a28f0cee2e14c1",
            "consentId": "5f652ac661c37dd829c8d23b",
            "revision": 3,
            "givenName": "Test client",
            "middleName": "test",
            "familyName": "Test client",
            "signature": {"bucket": "bucket", "key": "key", "region": "eu"},
            "sharingOption": 1,
            "createDateTime": "2021-10-06T16:46:21.447000Z",
            "deploymentId": "5f652a9661c37dd829c8d23a",
        },
    },
}
sample_heart_rate_dict = {
    "id": None,
    "userId": "615db4dd92a28f0cee2e14c1",
    "moduleId": "HeartRate",
    "moduleConfigId": "6138cdecb26118015764c1da",
    "moduleResultId": "1e61011138ce4f7b98088345b6685de4",
    "deploymentId": "5f652a9661c37dd829c8d23a",
    "version": 0,
    "deviceName": "iOS",
    "isAggregated": False,
    "startDateTime": "2022-04-13T15:14:01.961000Z",
    "endDateTime": None,
    "createDateTime": "2022-04-13T15:14:02.160000Z",
    "submitterId": "615db4dd92a28f0cee2e14c1",
    "correlationStartDateTime": None,
    "client": None,
    "server": {"hostUrl": "vb-ppserver.ngrok.io", "server": "1.21.0", "api": "V1"},
    "ragThreshold": {
        "HeartRate": {
            "value": {
                "color": "#FBCCD7",
                "severity": 3,
                "direction": "NOCHANGE",
                "isCustom": False,
            },
            "variabilitySDNN": {},
        }
    },
    "flags": {"red": 1, "amber": 0, "gray": 0},
    "value": 23,
    "heartRateType": None,
    "classification": None,
    "source": None,
    "variabilityAVNN": None,
    "variabilitySDNN": None,
    "variabilityRMSSD": None,
    "variabilityPNN50": None,
    "variabilityprcLF": None,
    "confidence": None,
    "goodIBI": None,
    "rawDataObject": None,
    "valueUnit": "bpm",
    "metadata": None,
    "_cls": "HeartRate",
    "user": {
        "id": "615db4dd92a28f0cee2e14c1",
        "updateDateTime": "2022-04-13T15:14:02.351000Z",
        "createDateTime": "2021-10-06T14:38:21.150000Z",
        "lastSubmitDateTime": "2022-04-13T15:14:02.160000Z",
        "givenName": "Mahdi",
        "familyName": "Biria",
        "gender": "MALE",
        "biologicalSex": "MALE",
        "ethnicity": "OTHER_ETHNIC_GROUPS",
        "dateOfBirth": "1988-04-04",
        "email": "<EMAIL>",
        "height": 178.0,
        "extraCustomFields": {},
        "timezone": "Europe/London",
        "enrollmentId": 354,
        "boardingStatus": {
            "status": 0,
            "updateDateTime": "2022-01-06T10:51:27.051000Z",
        },
        "language": "ar",
        "finishedOnboarding": True,
        "stats": {
            "taskCompliance": {
                "current": 0,
                "total": 0,
                "due": 0,
                "updateDateTime": "2022-04-13T14:48:06.770000Z",
            }
        },
        "lastLoginDateTime": "2022-03-18T17:00:59.897953Z",
        "consent": {
            "id": "615dd2dd92a28f0cee2e14cb",
            "userId": "615db4dd92a28f0cee2e14c1",
            "consentId": "5f652ac661c37dd829c8d23b",
            "revision": 3,
            "givenName": "Test client",
            "middleName": "test",
            "familyName": "Test client",
            "signature": {"bucket": "bucket", "key": "key", "region": "eu"},
            "sharingOption": 1,
            "createDateTime": "2021-10-06T16:46:21.447000Z",
            "deploymentId": "5f652a9661c37dd829c8d23a",
        },
    },
}
sample_body_temperature_dict = {
    "id": None,
    "userId": "615db4dd92a28f0cee2e14c1",
    "moduleId": "Temperature",
    "moduleConfigId": "5f652ae13ded61931ba05938",
    "moduleResultId": "b72c43ce0a30439297ab0057291a7b92",
    "deploymentId": "5f652a9661c37dd829c8d23a",
    "version": 0,
    "deviceName": "iOS",
    "isAggregated": False,
    "startDateTime": "2022-04-13T15:17:44.326000Z",
    "endDateTime": None,
    "createDateTime": "2022-04-13T15:17:44.566000Z",
    "submitterId": "615db4dd92a28f0cee2e14c1",
    "correlationStartDateTime": None,
    "client": None,
    "server": {"hostUrl": "vb-ppserver.ngrok.io", "server": "1.21.0", "api": "V1"},
    "ragThreshold": {
        "Temperature": {
            "value": {
                "color": "#CBEBF0",
                "severity": 1,
                "direction": "NOCHANGE",
                "isCustom": False,
            }
        }
    },
    "flags": {"gray": 1, "amber": 0, "red": 0},
    "value": 34.0,
    "valueUnit": "oC",
    "originalValue": None,
    "originalUnit": None,
    "_cls": "Temperature",
    "user": {
        "id": "615db4dd92a28f0cee2e14c1",
        "updateDateTime": "2022-04-13T15:17:44.801000Z",
        "createDateTime": "2021-10-06T14:38:21.150000Z",
        "lastSubmitDateTime": "2022-04-13T15:17:44.566000Z",
        "givenName": "Mahdi",
        "familyName": "Biria",
        "gender": "MALE",
        "biologicalSex": "MALE",
        "ethnicity": "OTHER_ETHNIC_GROUPS",
        "dateOfBirth": "1988-04-04",
        "email": "<EMAIL>",
        "height": 178.0,
        "extraCustomFields": {},
        "timezone": "Europe/London",
        "enrollmentId": 354,
        "boardingStatus": {
            "status": 0,
            "updateDateTime": "2022-01-06T10:51:27.051000Z",
        },
        "language": "ar",
        "finishedOnboarding": True,
        "stats": {
            "taskCompliance": {
                "current": 0,
                "total": 0,
                "due": 0,
                "updateDateTime": "2022-04-13T14:48:06.770000Z",
            }
        },
        "lastLoginDateTime": "2022-03-18T17:00:59.897953Z",
        "consent": {
            "id": "615dd2dd92a28f0cee2e14cb",
            "userId": "615db4dd92a28f0cee2e14c1",
            "consentId": "5f652ac661c37dd829c8d23b",
            "revision": 3,
            "givenName": "Test client",
            "middleName": "test",
            "familyName": "Test client",
            "signature": {"bucket": "bucket", "key": "key", "region": "eu"},
            "sharingOption": 1,
            "createDateTime": "2021-10-06T16:46:21.447000Z",
            "deploymentId": "5f652a9661c37dd829c8d23a",
        },
    },
}

sample_event_primitives = {"BloodPressure": sample_blood_pressure}
sample_event_dicts = [
    {
        "userId": "615db4dd92a28f0cee2e14c1",
        "moduleId": "Symptom",
        "moduleResultId": "31f79109948c469bba0b3e202960d961",
        "moduleConfigId": "617a6ae22ad9606b933e3dfc",
        "deploymentId": "617a6ade2ad9606b933e3d8e",
        "deviceName": "iOS",
        "startDateTime": "2021-11-09T14:28:37.630000Z",
        "primitives": [
            {
                "id": "6195448957543e2137fbc565",
                "userId": "615db4dd92a28f0cee2e14c1",
                "moduleId": "Symptom",
                "moduleResultId": "31f79109948c469bba0b3e202960d961",
                "moduleConfigId": "617a6ae22ad9606b933e3dfc",
                "deploymentId": "617a6ade2ad9606b933e3d8e",
                "version": 0,
                "deviceName": "iOS",
                "isAggregated": False,
                "startDateTime": "2020-04-28T21:13:07.000000Z",
                "endDateTime": None,
                "createDateTime": "2021-11-17T18:06:01.828254Z",
                "submitterId": "615db4dd92a28f0cee2e14c1",
                "correlationStartDateTime": None,
                "client": None,
                "server": {
                    "hostUrl": "vb-ppserver.ngrok.io",
                    "server": "1.17.0",
                    "api": "V1",
                },
                "ragThreshold": None,
                "flags": None,
                "value": None,
                "complexValues": [
                    {"name": "Pain", "severity": 2},
                    {"name": "Breathlessness", "severity": 4},
                    {"name": "Swelling", "severity": 3},
                ],
                "_cls": "Symptom",
            }
        ],
    },
    {
        "userId": "615db4dd92a28f0cee2e14c1",
        "moduleId": "CVDRiskScore",
        "moduleResultId": "31f79109948c469bba0b3e202960d962",
        "moduleConfigId": "617a6ae12ad9606b933e3dc6",
        "deploymentId": "617a6ade2ad9606b933e3d8f",
        "deviceName": "iOS",
        "startDateTime": "2021-11-09T14:28:37.630000Z",
        "primitives": [
            {
                "id": "61953811211379346452d5f3",
                "userId": "615db4dd92a28f0cee2e14c1",
                "moduleId": "CVDRiskScore",
                "moduleResultId": "31f79109948c469bba0b3e202960d962",
                "moduleConfigId": "617a6ae12ad9606b933e3dc6",
                "deploymentId": "617a6ade2ad9606b933e3d8f",
                "version": 1,
                "deviceName": "iOS",
                "isAggregated": False,
                "startDateTime": "2021-10-26T14:54:11.573000Z",
                "endDateTime": None,
                "createDateTime": "2021-11-17T17:12:49.960770Z",
                "submitterId": "615db4dd92a28f0cee2e14c1",
                "correlationStartDateTime": None,
                "client": None,
                "server": None,
                "ragThreshold": None,
                "flags": None,
                "answers": [
                    {
                        "answerText": "12",
                        "value": None,
                        "compositeAnswer": None,
                        "answerScore": None,
                        "questionId": "cvd_sleep_hours",
                        "question": "About how many hours of sleep do you usually get in 24 hours?",
                        "format": "NUMERIC",
                        "choices": None,
                        "selectedChoices": None,
                        "selectionCriteria": None,
                        "lowerBound": None,
                        "upperBound": None,
                        "lists": None,
                    },
                    {
                        "answerText": "56",
                        "value": None,
                        "compositeAnswer": None,
                        "answerScore": None,
                        "questionId": "cvd_resting_heart_rate",
                        "question": "What is your Resting Heart Rate?",
                        "format": "NUMERIC_UNIT",
                        "choices": None,
                        "selectedChoices": None,
                        "selectionCriteria": None,
                        "lowerBound": None,
                        "upperBound": None,
                        "lists": None,
                    },
                    {
                        "answerText": "65.0, 60.0",
                        "value": None,
                        "compositeAnswer": {
                            "waistCircumference": "65.0",
                            "hipCircumference": "60.0",
                        },
                        "answerScore": None,
                        "questionId": "cvd_hip_waist_circumference",
                        "question": "What is your waist and hip circumference?",
                        "format": "COMPOSITE",
                        "choices": None,
                        "selectedChoices": None,
                        "selectionCriteria": None,
                        "lowerBound": None,
                        "upperBound": None,
                        "lists": None,
                    },
                    {
                        "answerText": "DAILY",
                        "value": None,
                        "compositeAnswer": None,
                        "answerScore": None,
                        "questionId": "cvd_drink_alcohol",
                        "question": "How often do you drink alcohol?",
                        "format": "TEXTCHOICE",
                        "choices": [
                            "DAILY",
                            "THREE_OR_FOUR_TIMES_A_WEEK",
                            "ONCE_OR_TWICE_A_WEEK",
                            "ONE_TO_THREE_TIMES_A_MONTH",
                            "SPECIAL_OCCASIONS",
                            "NEVER",
                        ],
                        "selectedChoices": ["DAILY"],
                        "selectionCriteria": "SINGLE",
                        "lowerBound": None,
                        "upperBound": None,
                        "lists": None,
                    },
                    {
                        "answerText": "false",
                        "value": False,
                        "compositeAnswer": None,
                        "answerScore": None,
                        "questionId": "cvd_smoke",
                        "question": "Do you currently smoke?",
                        "format": "BOOLEAN",
                        "choices": None,
                        "selectedChoices": None,
                        "selectionCriteria": None,
                        "lowerBound": None,
                        "upperBound": None,
                        "lists": None,
                    },
                    {
                        "answerText": "BRISK_PACE",
                        "value": None,
                        "compositeAnswer": None,
                        "answerScore": None,
                        "questionId": "cvd_walking_pace",
                        "question": "How would you describe your usual walking pace?",
                        "format": "TEXTCHOICE",
                        "choices": ["SLOW_PACE", "STEADY_AVERAGE_PACE", "BRISK_PACE"],
                        "selectedChoices": ["BRISK_PACE"],
                        "selectionCriteria": "SINGLE",
                        "lowerBound": None,
                        "upperBound": None,
                        "lists": None,
                    },
                    {
                        "answerText": "GOOD",
                        "value": None,
                        "compositeAnswer": None,
                        "answerScore": None,
                        "questionId": "cvd_overall_health",
                        "question": "In general, how would you rate your overall health?",
                        "format": "TEXTCHOICE",
                        "choices": ["EXCELLENT", "GOOD", "FAIR", "POOR"],
                        "selectedChoices": ["GOOD"],
                        "selectionCriteria": "SINGLE",
                        "lowerBound": None,
                        "upperBound": None,
                        "lists": None,
                    },
                    {
                        "answerText": "NONE_OF_THE_ABOVE",
                        "value": None,
                        "compositeAnswer": None,
                        "answerScore": None,
                        "questionId": "cvd_conditions",
                        "question": "Have you been diagnosed with any of the following conditions?",
                        "format": "TEXTCHOICE",
                        "choices": [
                            "HIGH_BLOOD_PRESSURE",
                            "DIABETES",
                            "ATRIAL_FIBRILLATION_OR_FLUTTER",
                            "OTHER_HEART_ARRHYTHMIAS",
                            "DEPRESSION",
                            "LEUKAEMIA_LYMPHOMA_MYELOMA",
                            "NONE_OF_THE_ABOVE",
                        ],
                        "selectedChoices": ["NONE_OF_THE_ABOVE"],
                        "selectionCriteria": "MULTIPLE",
                        "lowerBound": None,
                        "upperBound": None,
                        "lists": None,
                    },
                    {
                        "answerText": "NONE_OF_THE_ABOVE",
                        "value": None,
                        "compositeAnswer": None,
                        "answerScore": None,
                        "questionId": "cvd_symptoms",
                        "question": "Do you experience any of the following symptoms?",
                        "format": "TEXTCHOICE",
                        "choices": [
                            "BREATHLESSNESS",
                            "DIZZINESS_OR_GIDDINESS",
                            "LOSS_OF_CONSCIOUSNESS_OR_COLLAPSE",
                            "ABDOMINAL_AND_PELVIC_PAIN",
                            "NONE_OF_THE_ABOVE",
                        ],
                        "selectedChoices": ["NONE_OF_THE_ABOVE"],
                        "selectionCriteria": "MULTIPLE",
                        "lowerBound": None,
                        "upperBound": None,
                        "lists": None,
                    },
                    {
                        "answerText": "NONE_OF_THE_ABOVE",
                        "value": None,
                        "compositeAnswer": None,
                        "answerScore": None,
                        "questionId": "cvd_medications",
                        "question": "Do you take any of the following medications?",
                        "format": "TEXTCHOICE",
                        "choices": [
                            "CHOLESTEROL_LOWERING_MEDICATION",
                            "BLOOD_PRESSURE_MEDICATION",
                            "INSULIN",
                            "NONE_OF_THE_ABOVE",
                        ],
                        "selectedChoices": ["NONE_OF_THE_ABOVE"],
                        "selectionCriteria": "MULTIPLE",
                        "lowerBound": None,
                        "upperBound": None,
                        "lists": None,
                    },
                    {
                        "answerText": "NONE_OF_THE_ABOVE",
                        "value": None,
                        "compositeAnswer": None,
                        "answerScore": None,
                        "questionId": "cvd_close_relatives",
                        "question": "Do any of your close relatives have a heart disease?",
                        "format": "TEXTCHOICE",
                        "choices": ["FATHER", "MOTHER", "SIBLING", "NONE_OF_THE_ABOVE"],
                        "selectedChoices": ["NONE_OF_THE_ABOVE"],
                        "selectionCriteria": "MULTIPLE",
                        "lowerBound": None,
                        "upperBound": None,
                        "lists": None,
                    },
                ],
                "appResult": None,
                "isForManager": None,
                "questionnaireId": "",
                "questionnaireName": "",
                "value": None,
                "skipped": None,
                "_cls": "Questionnaire",
            },
            {
                "id": "61953811211379346452d5f4",
                "userId": "615db4dd92a28f0cee2e14c1",
                "moduleId": "CVDRiskScore",
                "moduleResultId": "31f79109948c469bba0b3e202960d962",
                "moduleConfigId": "617a6ae12ad9606b933e3dc6",
                "deploymentId": "617a6ade2ad9606b933e3d8f",
                "version": 1,
                "deviceName": "iOS",
                "isAggregated": False,
                "startDateTime": "2021-10-26T14:54:11.573000Z",
                "endDateTime": None,
                "createDateTime": "2021-11-17T17:12:49.986410Z",
                "submitterId": "615db4dd92a28f0cee2e14c1",
                "correlationStartDateTime": None,
                "client": None,
                "server": None,
                "ragThreshold": None,
                "flags": None,
                "value": 56,
                "heartRateType": None,
                "classification": None,
                "source": "Manual",
                "variabilityAVNN": None,
                "variabilitySDNN": None,
                "variabilityRMSSD": None,
                "variabilityPNN50": None,
                "variabilityprcLF": None,
                "confidence": None,
                "goodIBI": None,
                "rawDataObject": None,
                "valueUnit": "bpm",
                "metadata": None,
                "_cls": "HeartRate",
            },
            {
                "id": "61953812211379346452d5f5",
                "userId": "615db4dd92a28f0cee2e14c1",
                "moduleId": "CVDRiskScore",
                "moduleResultId": "31f79109948c469bba0b3e202960d962",
                "moduleConfigId": "617a6ae12ad9606b933e3dc6",
                "deploymentId": "617a6ade2ad9606b933e3d8f",
                "version": 1,
                "deviceName": "iOS",
                "isAggregated": False,
                "startDateTime": "2021-10-26T14:54:11.573000Z",
                "endDateTime": None,
                "createDateTime": "2021-11-17T17:12:50.007197Z",
                "submitterId": "615db4dd92a28f0cee2e14c1",
                "correlationStartDateTime": None,
                "client": None,
                "server": None,
                "ragThreshold": None,
                "flags": None,
                "visceralFat": None,
                "totalBodyFat": None,
                "waistCircumference": 65.0,
                "waistCircumferenceUnit": "cm",
                "hipCircumference": 60.0,
                "hipCircumferenceUnit": "cm",
                "waistToHipRatio": None,
                "_cls": "BodyMeasurement",
            },
            {
                "id": "61953812211379346452d5f6",
                "userId": "615db4dd92a28f0cee2e14c1",
                "moduleId": "CVDRiskScore",
                "moduleResultId": "31f79109948c469bba0b3e202960d962",
                "moduleConfigId": "617a6ae12ad9606b933e3dc6",
                "deploymentId": "617a6ade2ad9606b933e3d8f",
                "version": 1,
                "deviceName": "iOS",
                "isAggregated": False,
                "startDateTime": "2021-10-26T14:54:11.573000Z",
                "endDateTime": None,
                "createDateTime": "2021-11-17T17:12:50.120642Z",
                "submitterId": "615db4dd92a28f0cee2e14c1",
                "correlationStartDateTime": None,
                "client": None,
                "server": None,
                "ragThreshold": None,
                "flags": None,
                "age": 33.0,
                "sex": "MALE",
                "alcoholIntake": "DAILY",
                "sleepDuration": 12.0,
                "smokingStatus": False,
                "walkingPace": "BRISK_PACE",
                "overallHealth": "GOOD",
                "existingConditions": ["NONE_OF_THE_ABOVE"],
                "existingSymptoms": ["NONE_OF_THE_ABOVE"],
                "currentMedications": ["NONE_OF_THE_ABOVE"],
                "familyHeartDisease": ["NONE_OF_THE_ABOVE"],
                "heartRate": 56.0,
                "waistCircumference": 65.0,
                "hipCircumference": 60.0,
                "waistToHipRatio": 1.0833333333333333,
                "riskFactors": [
                    {"name": "Age", "colorHex": None, "contribution": 0.0},
                    {"name": "Sex", "colorHex": None, "contribution": 0.0},
                    {"name": "Alcohol", "colorHex": None, "contribution": 0.0},
                    {"name": "Smoking", "colorHex": None, "contribution": 0.0},
                    {
                        "name": "Body Measurements",
                        "colorHex": "#FDE5EB",
                        "contribution": 0.**********,
                    },
                    {"name": "Heart Rate", "colorHex": None, "contribution": 0.0},
                    {"name": "Medical History", "colorHex": None, "contribution": 0.0},
                    {"name": "Health Rating", "colorHex": None, "contribution": 0.0},
                    {
                        "name": "Physical Activity",
                        "colorHex": "#FFF7EB",
                        "contribution": -0.141855204,
                    },
                    {
                        "name": "Sleep",
                        "colorHex": "#FFF7EB",
                        "contribution": -0.**********,
                    },
                ],
                "riskTrajectory": [
                    {"riskPercentage": 0.**********, "daysCount": 182.5},
                    {"riskPercentage": 0.**********, "daysCount": 365.0},
                    {"riskPercentage": 0.**********, "daysCount": 730.0},
                    {"riskPercentage": 0.**********, "daysCount": 1095.0},
                    {"riskPercentage": 0.**********, "daysCount": 1460.0},
                    {"riskPercentage": 0.**********, "daysCount": 1825.0},
                    {"riskPercentage": 0.**********, "daysCount": 2190.0},
                    {"riskPercentage": 0.0068440073, "daysCount": 2555.0},
                    {"riskPercentage": 0.008102194, "daysCount": 2920.0},
                    {"riskPercentage": 0.0094302219, "daysCount": 3285.0},
                    {"riskPercentage": 0.0107578889, "daysCount": 3650.0},
                ],
                "originalValue": 0.0107578889,
                "roundedValue": 0.01,
                "_cls": "CVDRiskScore",
            },
        ],
    },
    {
        "userId": "615db4dd92a28f0cee2e14c1",
        "moduleId": "BloodPressure",
        "moduleResultId": "31f79109948c469bba0b3e202960d963",
        "moduleConfigId": "617a6ae12ad9606b933e3db7",
        "deploymentId": "606c50a113dbea3656ff1bb0",
        "deviceName": "iOS",
        "startDateTime": "2021-11-09T14:28:37.630000Z",
        "primitives": [
            {
                "id": "618a8595a57e07e2de456e33",
                "userId": "615db4dd92a28f0cee2e14c1",
                "moduleId": "BloodPressure",
                "moduleResultId": "31f79109948c469bba0b3e202960d963",
                "moduleConfigId": "617a6ae12ad9606b933e3db7",
                "deploymentId": "606c50a113dbea3656ff1bb0",
                "version": 0,
                "deviceName": "iOS",
                "isAggregated": False,
                "startDateTime": "2021-11-09T14:28:37.630000Z",
                "endDateTime": None,
                "createDateTime": "2021-11-09T14:28:37.767489Z",
                "submitterId": "615db4dd92a28f0cee2e14c1",
                "correlationStartDateTime": None,
                "client": None,
                "server": {
                    "hostUrl": "vb-ppserver.ngrok.io",
                    "server": "1.16.0",
                    "api": "V1",
                },
                "ragThreshold": None,
                "flags": None,
                "diastolicValue": 80,
                "systolicValue": 92,
                "diastolicValueUnit": "mmHg",
                "systolicValueUnit": "mmHg",
                "_cls": "BloodPressure",
            }
        ],
    },
]
sample_integrations = [
    IntegrationDTO(
        id="618a5ba0945fc88ee209214a",
        name="HL7 Integration",
        integrationType=IntegrationDTO.Type.WEBHOOK,
        webhook=WebhookDTO(
            endpoint="https://webhook.site/64a6d6f5-34f3-450c-9497-63ffd468a9e9",
            authType="NONE",
            username="12345678sS",
            token=None,
        ),
        organizationIds=["5f652a9661c37dd829c8d23a"],
        deploymentIds=["5f652a9661c37dd829c8d23a", "617a6ade2ad9606b933e3d8e"],
        moduleNames=None,
        excludedModuleNames=None,
    ),
    IntegrationDTO(
        id="618a5ba0945fc88ee209214a",
        name="HL7 Integration",
        integrationType=IntegrationDTO.Type.WEBHOOK,
        webhook=WebhookDTO(
            endpoint="https://webhook.site/64a6d6f5-34f3-450c-9497-63ffd468a9e9",
            authType="NONE",
            username="12345678sS",
            token=None,
        ),
        organizationIds=["5f652a9661c37dd829c8d23a"],
        moduleNames=None,
        excludedModuleNames=None,
    ),
    IntegrationDTO(
        id="618a5ba0945fc88ee209214a",
        name="HL7 Integration",
        integrationType=IntegrationDTO.Type.WEBHOOK,
        webhook=WebhookDTO(
            endpoint="https://webhook.site/64a6d6f5-34f3-450c-9497-63ffd468a9e9",
            authType="NONE",
            username="12345678sS",
            token=None,
        ),
        moduleNames=None,
        excludedModuleNames=None,
    ),
    IntegrationDTO(
        id="618a5ba0945fc88ee209214a",
        name="HL7 Integration",
        integrationType=IntegrationDTO.Type.WEBHOOK,
        webhook=WebhookDTO(
            endpoint="https://webhook.site/64a6d6f5-34f3-450c-9497-63ffd468a9e9",
            authType="NONE",
            username="12345678sS",
            token=None,
        ),
        moduleNames=["BloodPressure"],
        excludedModuleNames=None,
    ),
    IntegrationDTO(
        id="618a5ba0945fc88ee209214a",
        name="HL7 Integration",
        integrationType=IntegrationDTO.Type.WEBHOOK,
        webhook=WebhookDTO(
            endpoint="https://webhook.site/64a6d6f5-34f3-450c-9497-63ffd468a9e9",
            authType="NONE",
            username="12345678sS",
            token=None,
        ),
        moduleNames=["Weight"],
        excludedModuleNames=None,
    ),
    IntegrationDTO(
        id="618a5ba0945fc88ee209214a",
        name="HL7 Integration",
        integrationType=IntegrationDTO.Type.WEBHOOK,
        webhook=WebhookDTO(
            endpoint="https://webhook.site/64a6d6f5-34f3-450c-9497-63ffd468a9e9",
            authType="NONE",
            username="12345678sS",
            token=None,
        ),
        moduleNames=None,
        excludedModuleNames=["BloodPressure"],
    ),
]

sample_gcp_fhir_integration = IntegrationDTO(
    id="618a5ba0945fc88ee209214a",
    name="FHIR Integration GCP",
    integrationType=IntegrationDTO.Type.GCPFHIR,
    gcp_fhir=GCPFhirDTO(
        url="https://healthcare.googleapis.com/v1/projects/hu-global-sandbox/locations/us-east4/datasets/test/fhirStores/huma_demo_fhir/fhir",
        serviceAccountData={},
        config={},
    ),
    organizationIds=["5f652a9661c37dd829c8d23a"],
    deploymentIds=["5f652a9661c37dd829c8d23a", "617a6ade2ad9606b933e3d8e"],
    moduleNames=None,
    excludedModuleNames=None,
)

sample_user_dict = {
    "id": "615db4dd92a28f0cee2e14c1",
    "updateDateTime": "2022-04-13T15:43:04.876000Z",
    "createDateTime": "2021-10-06T14:38:21.150000Z",
    "lastSubmitDateTime": "2022-04-13T15:43:04.689000Z",
    "givenName": "Mahdi",
    "familyName": "Biria",
    "gender": "MALE",
    "biologicalSex": "MALE",
    "ethnicity": "OTHER_ETHNIC_GROUPS",
    "dateOfBirth": "1988-04-04",
    "email": "<EMAIL>",
    "height": 178.0,
    "extraCustomFields": {},
    "timezone": "Europe/London",
    "enrollmentId": 354,
    "boardingStatus": {"status": 0, "updateDateTime": "2022-01-06T10:51:27.051000Z"},
    "language": "ar",
    "finishedOnboarding": True,
    "stats": {
        "taskCompliance": {
            "current": 0,
            "total": 0,
            "due": 0,
            "updateDateTime": "2022-04-13T14:48:06.770000Z",
        }
    },
    "lastLoginDateTime": "2022-03-18T17:00:59.897953Z",
    "consent": {
        "id": "615dd2dd92a28f0cee2e14cb",
        "userId": "615db4dd92a28f0cee2e14c1",
        "consentId": "5f652ac661c37dd829c8d23b",
        "revision": 3,
        "givenName": "Test client",
        "middleName": "test",
        "familyName": "Test client",
        "signature": {"bucket": "bucket", "key": "key", "region": "eu"},
        "sharingOption": 1,
        "createDateTime": "2021-10-06T16:46:21.447000Z",
        "deploymentId": "5f652a9661c37dd829c8d23a",
    },
}

sample_user = {
    "id": "615db4dd92a28f0cee2e14c1",
    "updateDateTime": "2021-11-16T18:54:50.176000Z",
    "createDateTime": "2021-10-06T14:38:21.150000Z",
    "givenName": "Mahdi",
    "familyName": "Biria",
    "gender": "MALE",
    "biologicalSex": "MALE",
    "ethnicity": None,
    "dateOfBirth": "1988-04-04",
    "email": "<EMAIL>",
    "phoneNumber": None,
    "primaryAddress": None,
    "race": None,
    "bloodGroup": None,
    "emergencyPhoneNumber": None,
    "height": None,
    "additionalContactDetails": None,
    "familyMedicalHistory": None,
    "pastHistory": None,
    "presentSymptoms": None,
    "operationHistory": None,
    "chronicIllness": None,
    "allergyHistory": None,
    "pregnancy": None,
    "dateOfLastPhysicalExam": None,
    "extraCustomFields": None,
    "surgeryDetails": None,
    "fenlandCohortId": None,
    "nhsId": None,
    "insuranceNumber": None,
    "wechatId": None,
    "kardiaId": None,
    "pamThirdPartyIdentifier": None,
    "roles": None,
    "timezone": "UTC",
    "latestModuleResults": None,
    "recentModuleResults": None,
    "surgeryDateTime": None,
    "preferredUnits": None,
    "addressComponent": None,
    "personalDocuments": None,
    "enrollmentId": 354,
    "enrollmentNumber": None,
    "deployments": None,
    "boardingStatus": None,
    "language": "en",
    "finishedOnboarding": True,
    "stats": {
        "taskCompliance": {
            "current": 0,
            "total": 14,
            "due": 3,
            "updateDateTime": "2021-11-16T18:54:25.427000Z",
        }
    },
    "ragScore": None,
    "flags": None,
    "consent": {
        "id": "61927308c8a3eff4681d2760",
        "userId": "615db4dd92a28f0cee2e14c1",
        "consentId": "61926cc89cb844829c967fd2",
        "revision": 1,
        "givenName": "Test client",
        "middleName": "test",
        "familyName": "Test client",
        "signature": {"bucket": "bucket", "key": "key", "region": "eu"},
        "sharingOption": 1,
        "createDateTime": "2021-10-28T17:26:38.662000Z",
        "agreement": None,
        "deploymentId": "61926cbe9cb844829c967f8a",
        "additionalConsentAnswers": None,
    },
    "econsent": None,
}


patient_template_sample = {
    "resourceType": "Patient",
    "identifier": [{"system": "https://fhir.nhs.uk/Id/nhs-number", "value": "{obj.id}"}],
    "name": [{"use": "official", "family": "{obj.familyName}", "given": ["{obj.givenName}"]}],
    "gender": "male",
    "birthDate": "1988-04-04",
}

sample_simple_event = {"primitives": [{"user": {"id": "65367bc23c0f4b716627a11d", "email": "<EMAIL>"}}]}

sample_patient_profile = {
    "userId": "65367bc23c0f4b716627a11d",
    "email": "<EMAIL>",
    "identifiers": [{"id": "1234", "idtype": "MRN"}],
    "lastName": "Doe",
    "firstName": "John",
    "status": "ON_BOARDED",
}

sample_simple_blood_glucose_event = {
    "moduleId": "BloodGlucose",
    "primitives": [{"startDateTime": "2022-02-20T12:00:00Z", "value": 100, "valueUnit": "mg/dL"}],
}

sample_order = {
    "Meta": {
        "DataModel": "Order",
        "EventType": "New",
        "EventDateTime": "2023-01-26T14:59:09.217Z",
        "Source": {
            "ID": "af394f14-b34a-464f-8d24-895f370af4c9",
            "Name": "Receive Order",
        },
        "Destination": {"ID": "aef472f24-c35a-264f-3d21-695f270af1c9", "Name": "Huma"},
        "Message": {"ID": 9970},
    },
    "Order": {
        "ID": "1",
        "TransactionDateTime": None,
        "Procedure": {"Code": "003038", "Codeset": "L", "Description": "Urinalysis"},
    },
    "Patient": {
        "Identifiers": [{"ID": "12001", "IDType": "NHS"}],
        "Demographics": {
            "FirstName": "John",
            "MiddleName": "",
            "LastName": "Jones",
            "Mobile": "",
            "EmailAddresses": ["<EMAIL>"],
            "DOB": "1967-08-24",
        },
    },
}
