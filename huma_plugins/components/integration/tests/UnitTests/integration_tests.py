import unittest
from unittest.mock import MagicMock

from bson import ObjectId

from huma_plugins.components.integration.dtos.integration import IntegrationDTO
from huma_plugins.components.integration.dtos.webhook import WebhookDTO
from huma_plugins.components.integration.router.integration_requests import (
    CreateIntegrationRequestObject,
    UpdateIntegrationRequestObject,
    RetrieveIntegrationsRequestObject,
    RetrieveIntegrationRequestObject,
    DeleteIntegrationRequestObject,
)
from huma_plugins.components.integration.use_case.integration_use_case import (
    CreateIntegrationUseCase,
    UpdateIntegrationUseCase,
    RetrieveIntegrationUseCase,
    DeleteIntegrationUseCase,
    RetrieveIntegrationsUseCase,
)

DEPLOYMENT_IDS = ["5f652a9661c37dd829c8d23a"]
ID = "619425abc3c1cd96eac8ba24"
INTEGRATION_TYPE = "WEBHOOK"
LISTENER_TYPE = "DEPLOYMENT_IDS"
EVENT_TYPE = "MODULE_RESULT"
NAME = "HL7 Integration Integration New"
DE_IDENTIFIED = False
WEBHOOK = WebhookDTO()
WEBHOOK.endpoint = "https://webhook.site/64a6d6f5-34f3-450c-9497-63ffd468a9e9"
WEBHOOK.authType = "NONE"


class IntegrationTestCase(unittest.TestCase):
    @staticmethod
    def test_create_integration_use_case_valid():
        request_data = {
            IntegrationDTO.INTEGRATION_NAME: NAME,
            IntegrationDTO.INTEGRATION_TYPE: INTEGRATION_TYPE,
            IntegrationDTO.WEBHOOK: WEBHOOK,
            IntegrationDTO.DEPLOYMENT_IDS: DEPLOYMENT_IDS,
            IntegrationDTO.ORGANIZATION_IDS: [],
        }

        request_object = CreateIntegrationRequestObject.from_dict(request_data)
        mocked_integration_repo = MagicMock()
        mocked_integration_repo.create_integration.return_value = str(ObjectId())
        use_case = CreateIntegrationUseCase(mocked_integration_repo)
        use_case.execute(request_object)
        mocked_integration_repo.create_integration.assert_called_with(request_object)

    @staticmethod
    def test_update_integration_use_case_valid():
        request_data = {
            IntegrationDTO.ID: ID,
            IntegrationDTO.INTEGRATION_NAME: NAME,
            IntegrationDTO.INTEGRATION_TYPE: INTEGRATION_TYPE,
            IntegrationDTO.WEBHOOK: WEBHOOK,
            IntegrationDTO.DEPLOYMENT_IDS: DEPLOYMENT_IDS,
            IntegrationDTO.ORGANIZATION_IDS: [],
        }

        request_object = UpdateIntegrationRequestObject.from_dict(request_data)
        mocked_integration_repo = MagicMock()
        use_case = UpdateIntegrationUseCase(mocked_integration_repo)
        use_case.execute(request_object)
        mocked_integration_repo.update_integration.assert_called_with(request_object)

    @staticmethod
    def test_delete_integration_use_case_valid():
        integration_id = "61815cb0515a3d3bae2960e7"
        request_object = DeleteIntegrationRequestObject.from_dict({"integrationId": integration_id})

        mocked_integration_repo = MagicMock()
        use_case = DeleteIntegrationUseCase(mocked_integration_repo)
        use_case.execute(request_object)
        mocked_integration_repo.delete_integration.assert_called_with(request_object.integrationId)

    @staticmethod
    def test_retrieve_integration_use_case_valid():
        integration_id = "61815cb0515a3d3bae2960e7"

        request_object = RetrieveIntegrationRequestObject.from_dict({"integrationId": integration_id})
        mocked_integration_repo = MagicMock()
        use_case = RetrieveIntegrationUseCase(mocked_integration_repo)
        use_case.execute(request_object)
        mocked_integration_repo.retrieve_integration.assert_called_with(request_object.integrationId)

    @staticmethod
    def test_retrieve_integrations_use_case_valid():
        payload = {"skip": 0, "limit": 10}

        request_object = RetrieveIntegrationsRequestObject().from_dict(payload)

        mocked_integration_repo = MagicMock()
        mocked_integration_repo.retrieve_integrations.return_value = ([], 0)

        RetrieveIntegrationsUseCase(mocked_integration_repo).execute(request_object)

        mocked_integration_repo.retrieve_integrations.assert_called_with(skip=0, limit=10)


if __name__ == "__main__":
    unittest.main()
