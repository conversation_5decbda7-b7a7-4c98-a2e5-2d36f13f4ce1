import unittest
from unittest.mock import MagicMock, patch

from huma_plugins.components.integration.adapters.generic.ehr_api import EHRApi
from huma_plugins.components.integration.dtos.generic_ehr import GenericEHRDTO

REQUESTS_SESSION_PATH = "huma_plugins.components.integration.adapters.generic.ehr_api.requests.session"
POST_PATH = "huma_plugins.components.integration.adapters.generic.ehr_api.requests.Session.post"
UPDATE_TOKENS_PATH = "huma_plugins.components.integration.adapters.generic.ehr_api.EHRApi.update_generic_ehr_tokens"


class MockResponse:
    def __init__(self, json_data=None, ok=True, text=""):
        self._json_data = json_data
        self.ok = ok
        self.text = text

    def json(self):
        return self._json_data


class EHRApiTestCase(unittest.TestCase):
    def setUp(self):
        self.mock_integration = MagicMock()
        self.mock_integration.generic_ehr = MagicMock()
        self.mock_integration.generic_ehr.client_id = "test_client_id"
        self.mock_integration.generic_ehr.client_secret = "test_client_secret"
        self.mock_integration.generic_ehr.token_url = "https://test.token.url"
        self.mock_integration.generic_ehr.url = "https://test.api.url"
        self.mock_integration.generic_ehr.authType = GenericEHRDTO.GenericEHRAuthType.OAUTH2_BASIC
        self.mock_integration.generic_ehr.retry = 3  # Adding retry configuration

        self.ehr_api = EHRApi(self.mock_integration)

    @patch(REQUESTS_SESSION_PATH)
    def test_login_basic_success(self, mock_session_func):
        # Create a mock session that will be returned by requests.session()
        mock_session = MagicMock()
        mock_response = MockResponse(
            {
                "access_token": "test_access_token",
                "expires_in": 3600,
            },
            ok=True,
        )
        mock_session.post.return_value = mock_response
        mock_session_func.return_value = mock_session

        result = self.ehr_api.login_basic()

        self.assertTrue(result)
        self.assertEqual(self.ehr_api._access_token, "test_access_token")

        # Verify the post was called correctly
        mock_session.post.assert_called_once()
        args, kwargs = mock_session.post.call_args
        self.assertEqual(args[0], "https://test.token.url/oauth2/token")
        self.assertIn("Authorization", kwargs["headers"])
        self.assertTrue(kwargs["headers"]["Authorization"].startswith("Basic "))
        self.assertEqual(kwargs["data"], {"grant_type": "client_credentials"})

    @patch(REQUESTS_SESSION_PATH)
    @patch(POST_PATH)
    def test_login_basic_failure(self, mock_post, mock_session_func):
        # Create a mock session that will be returned by requests.session()
        mock_session = MagicMock()
        mock_response = MockResponse(
            ok=False,
            text="Unauthorized",
        )
        mock_session.post.return_value = mock_response
        mock_session_func.return_value = mock_session

        # Ensure report_exception doesn't interfere with the test
        with patch("sdk.common.monitoring.report_exception"):
            result = self.ehr_api.login_basic()

        self.assertFalse(result)
        self.assertEqual(self.ehr_api._access_token, "")

    @patch(REQUESTS_SESSION_PATH)
    @patch(POST_PATH)
    @patch(UPDATE_TOKENS_PATH)
    def test_authenticate_with_oauth2_basic_token_reuse(self, mock_update_tokens, mock_post, mock_session_func):
        # Set up token and expiration to test token reuse path
        self.mock_integration.generic_ehr.access_token = "existing_token"
        self.mock_integration.generic_ehr.access_token_expiration = "2099-12-31T23:59:59Z"

        # Create a mock session that will be returned by requests.session()
        mock_session = MagicMock()
        mock_session_func.return_value = mock_session

        mock_response = MagicMock()
        mock_response.ok = True
        mock_response.json.return_value = {"access_token": "test_access_token", "expires_in": 3600}
        mock_post.return_value = mock_response

        result = self.ehr_api.authenticate()

        self.assertTrue(result)
        # Token should be reused, not updated
        self.assertEqual(self.ehr_api._access_token, "existing_token")
        # update_generic_ehr_tokens should not be called since we're reusing the token
        mock_update_tokens.assert_not_called()

    @patch(REQUESTS_SESSION_PATH)
    @patch(POST_PATH)
    @patch(UPDATE_TOKENS_PATH)
    def test_authenticate_with_oauth2_basic_token_refresh(self, mock_update_tokens, mock_post, mock_session_func):
        # Set up expired token to test refresh path
        self.mock_integration.generic_ehr.access_token = "expired_token"
        self.mock_integration.generic_ehr.access_token_expiration = "2020-01-01T00:00:00Z"

        # Create a mock session that will be returned by requests.session()
        mock_session = MagicMock()
        mock_response = MockResponse(
            {
                "access_token": "new_access_token",
                "expires_in": 3600,
            },
            ok=True,
        )
        mock_session.post.return_value = mock_response
        mock_session_func.return_value = mock_session

        result = self.ehr_api.authenticate()

        self.assertTrue(result)
        # Token should be refreshed
        self.assertEqual(self.ehr_api._access_token, "new_access_token")
        # update_generic_ehr_tokens should be called with the new token
        mock_update_tokens.assert_called_once()


if __name__ == "__main__":
    unittest.main()
