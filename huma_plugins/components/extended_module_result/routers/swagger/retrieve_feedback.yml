summary: Retrieve Module Result Feedback
tags:
  - modules

security:
  - Bearer: []

parameters:
  - in: path
    name: user_id
    required: true
    type: string
  - in: path
    name: module_id
    type: string
    required: true
  - in: path
    name: module_result_id
    type: string
    required: true
    description: ID of the submitted result.
responses:
  200:
    description: Feedback data generated for submitted result
    schema:
      $ref: "#/definitions/RetrieveFeedbackResponse"

definitions:
  RetrieveFeedbackResponse:
    type: object
    properties:
      comparingValue:
        type: array
        items:
          type: number
          format: integer
      currentValue:
        type: array
        items:
          type: number
          format: integer
      currentValueSeverity:
        type: number
        format: integer
        minimum: 1
        maximum: 3
      feedback:
        $ref: '#/definitions/FeedbackResponse'
      lowerBound:
        type: number
      ragThresholds:
        type: array
        items:
          $ref: '#/definitions/RagThreshold'
      upperBound:
        type: number

  FeedbackResponse:
    type: object
    properties:
      feedbackTextId:
        type: string
        example: "5e84b0dab8dfa268b1180536"
      text:
        type: string
        example: "Monitor your peak flow regularly"
