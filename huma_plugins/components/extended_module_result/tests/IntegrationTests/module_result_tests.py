from huma_plugins.components.extended_module_result.dtos.primitives import (
    BodyCompositionDTO,
)
from huma_plugins.components.extended_module_result.tests.IntegrationTests.base_module_result_tests import (
    BaseModuleResultTest,
)
from huma_plugins.tests.shared.common import (
    BODY_COMPOSITION_SAMPLE,
    sample_weight_for_bmi_dict,
    sample_body_composition_for_bmi_dict,
)
from sdk.common.utils.common_functions_utils import deep_get
from sdk.module_result.modules.bmi import BMIDTO, BMIModule


class ExtendedModuleResultsTestCase(BaseModuleResultTest):
    def test_submit_body_composition_bmi_module_result(self):
        body = [sample_weight_for_bmi_dict(200), sample_body_composition_for_bmi_dict()]
        rsp = self._submit_module_result(BMIModule.moduleId, body, 201)
        self.assertNotIn("errors", rsp.json)
        self.assertEqual(len(rsp.json.get("ids")), 3)

        rsp = self._search_module_result(BMIDTO.get_primitive_name())
        self.assertIn(BodyCompositionDTO.get_primitive_name(), rsp.json)
        body_composition = rsp.json.get(BodyCompositionDTO.get_primitive_name())[0]
        for item in BODY_COMPOSITION_SAMPLE:
            self.assertEqual(body_composition.get(item), BODY_COMPOSITION_SAMPLE.get(item))
        self.assertEqual(
            deep_get(
                body_composition,
                f"{BodyCompositionDTO.DEVICE}.{BodyCompositionDTO.USER_INDEX}",
            ),
            1,
        )

    def test_submit_bmi_without_body_composition(self):
        body = [sample_weight_for_bmi_dict(200)]
        rsp = self._submit_module_result(BMIModule.moduleId, body, 201)
        self.assertNotIn("errors", rsp.json)
        self.assertEqual(len(rsp.json.get("ids")), 2)

        rsp = self._search_module_result(BMIDTO.get_primitive_name())
        self.assertFalse(rsp.json.get(BodyCompositionDTO.get_primitive_name()))
