from datetime import datetime
from unittest import TestCase
from unittest.mock import MagicMock, patch

from parameterized import parameterized

from huma_plugins.components.extended_module_result.dtos.primitives import ECGDTO
from huma_plugins.components.extended_module_result.dtos.primitives.primitive_ecg import (
    ECGClassification,
)
from huma_plugins.components.extended_module_result.modules.extended_ecg import (
    ECGModule,
)
from huma_plugins.components.extended_module_result.modules.visualizer import (
    ECGHTMLVisualizer,
)
from huma_plugins.tests.shared.common import sample_ecg_dict
from sdk.authorization.dtos.user import UserDTO
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.utils import inject
from sdk.common.utils.convertible import ConvertibleClassValidationError
from sdk.module_result.repository.module_result_repository import ModuleResultRepository

TEST_DEPLOYMENT_ID = "5e8f0c74b50aa9656c34789c"
TEST_USER_ID = "5e8f0c74b50aa9656c34789a"
PATH = "huma_plugins.components.extended_module_result.modules.extended_ecg"


def ecg_sample(sample: dict = None, with_pdf=False) -> ECGDTO:
    ecg = sample_ecg_dict(sample, with_pdf)
    return ECGDTO.from_dict(ecg)


class ECGTestCase(TestCase):
    @patch(f"{PATH}.ecg_data_points_to_array", MagicMock())
    @patch.object(ECGModule, "_generate_ecg_pdf", MagicMock())
    def test_success_ecg_module(self):
        module = ECGModule()
        user = UserDTO(id=TEST_USER_ID)
        primitives = [ecg_sample()]
        module.preprocess(primitives, user)
        primitive = primitives[0]
        self.assertIsNotNone(primitive.pdf)
        self.assertTrue(isinstance(primitive.classification, int))

    @patch(f"{PATH}.ecg_data_points_to_array", MagicMock())
    @patch.object(ECGModule, "_generate_ecg_pdf", MagicMock())
    def test_failure_ecg_module(self):
        module = ECGModule()
        user = UserDTO(id=TEST_USER_ID)
        ecg = ecg_sample({ECGDTO.SOURCE: ECGDTO.HUMA_DEVICE_KIT})
        primitives = [ecg]
        with self.assertRaises(InvalidRequestException) as context:
            module.preprocess(primitives, user)
        self.assertEqual(str(context.exception), "PDF is required")

    @parameterized.expand(
        [
            (ECGClassification.SINUS_RHYTHM, 1),
            (ECGClassification.ATRIAL_FIBRILLATION, 2),
            (ECGClassification.INCONCLUSIVE_HIGH_HEART_RATE, 3),
            (ECGClassification.INCONCLUSIVE_LOW_HEART_RATE, 4),
            (ECGClassification.INCONCLUSIVE_OTHER, 6),
            (ECGClassification.NOT_SET, 8),
            (ECGClassification.AFIB, 9),
            (ECGClassification.BRADYCARDIA, 10),
            (ECGClassification.NORMAL, 11),
            (ECGClassification.TACHYCARDIA, 12),
            (ECGClassification.NO_ANALYSIS, 13),
            (ECGClassification.UNCLASSIFIED, 14),
            (ECGClassification.UNREADABLE, 15),
            (ECGClassification.TOO_LONG, 16),
            (ECGClassification.TOO_SHORT, 17),
            (ECGClassification.NONE, 18),
        ]
    )
    @patch.object(ECGModule, "_generate_ecg_pdf", MagicMock())
    def test_success_ecg_with_classifications(self, classificication, expected_classificication):
        module = ECGModule()
        user = UserDTO(id=TEST_USER_ID)
        primitives = [
            ecg_sample(
                {
                    ECGDTO.SOURCE: ECGDTO.HUMA_DEVICE_KIT,
                    ECGDTO.CLASSIFICATION: classificication.value,
                },
                with_pdf=True,
            )
        ]
        module.preprocess(primitives, user)
        primitive = primitives[0]
        self.assertIsNotNone(primitive.pdf)
        self.assertEqual(primitive.classification, expected_classificication)

    def test_failure_create_ecg_primitive(self):
        with self.assertRaises(ConvertibleClassValidationError):
            ecg_sample({ECGDTO.CLASSIFICATION: None})
        with self.assertRaises(ConvertibleClassValidationError):
            ecg_sample({ECGDTO.AVERAGE_HEART_RATE: None})
        with self.assertRaises(ConvertibleClassValidationError):
            ecg_sample({ECGDTO.LEAD_TYPE: None})
        try:
            ecg_sample({ECGDTO.DATA_POINTS: None})
            ecg_sample({ECGDTO.PDF: None})
        except ConvertibleClassValidationError:
            self.fail()


class ECGVisualizerTestCase(TestCase):
    def setUp(self):
        def bind_and_configure(binder):
            binder.bind(ModuleResultRepository, MagicMock())

        inject.clear_and_configure(bind_and_configure)

    def test_success_filter_ecg_primitives(self):
        now = datetime.utcnow()
        user = UserDTO(id="1", timezone="UTC")
        visualizer = ECGHTMLVisualizer(MagicMock(), user, MagicMock(), now, now)
        linktop_ecg = ecg_sample()
        linktop_ecg.device.manufacturer = "Linktop"
        visualizer.primitives = [ecg_sample(), ecg_sample(), linktop_ecg]
        self.assertEqual(len(visualizer.primitives), 3)

        visualizer._filter_primitives()
        self.assertEqual(len(visualizer.primitives), 2)
