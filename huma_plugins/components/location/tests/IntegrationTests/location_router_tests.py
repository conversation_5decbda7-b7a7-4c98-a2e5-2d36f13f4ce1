from pathlib import Path
from unittest.mock import MagicMock, patch

from huma_plugins.components.location.component import LocationComponent
from huma_plugins.components.location.router.request_objects import (
    RetrieveLocationByCoordinatesRequestObject,
    RetrieveLocationsByTextRequestObject,
)
from huma_plugins.tests.plugin_test_case import PluginsTestCase
from sdk.auth.component import AuthComponent
from sdk.common.localization.utils import Language
from sdk.common.requests.request_utils import RequestContext

USER_ID = "5e8f0c74b50aa9656c34789a"
SAMPLE_FORMATTED_ADDRESS = "1600 Amphitheatre Pkwy, Mountain View, CA 94043, USA"
SAMPLE_LATITUDE = 37.4224764
SAMPLE_LONGITUDE = -122.0842499
PATCH_PATH = "huma_plugins.components.location.adapter.gmaps_location_adapter.googlemaps.Client"


def mocked_gmaps_client(key):
    client = MagicMock()
    client.reverse_geocode.return_value = [
        {
            "formatted_address": SAMPLE_FORMATTED_ADDRESS,
            "geometry": {
                "location": {"lat": SAMPLE_LATITUDE, "lng": SAMPLE_LONGITUDE},
                "location_type": "ROOFTOP",
                "viewport": {
                    "northeast": {"lat": 33, "lng": -122},
                    "southwest": {"lat": 34, "lng": -123},
                },
            },
            "place_id": "ChIJ2eUgeAK6j4ARbn5u_wAGqWA",
            "plus_code": {
                "compound_code": "CWC8+R9 Mountain View, California, United States",
                "global_code": "849VCWC8+R9",
            },
            "types": ["street_address"],
        }
    ]
    client.places.return_value = {
        "results": [
            {
                "formatted_address": SAMPLE_FORMATTED_ADDRESS,
                "geometry": {
                    "location": {"lat": SAMPLE_LATITUDE, "lng": SAMPLE_LONGITUDE},
                    "viewport": {
                        "northeast": {"lat": 33, "lng": -122},
                        "southwest": {"lat": 34, "lng": -123},
                    },
                },
                "name": "1600 Amphitheatre Pkwy",
                "place_id": "ChIJ2eUgeAK6j4ARbn5u_wAGqWA",
                "types": ["street_address"],
            }
        ],
    }
    return client


def mocked_gmaps_client_without_formatted_address(key):
    client = MagicMock()
    client.places.return_value = {
        "results": [
            {
                "geometry": {
                    "location": {"lat": SAMPLE_LATITUDE, "lng": SAMPLE_LONGITUDE},
                    "viewport": {
                        "northeast": {"lat": 33, "lng": -122},
                        "southwest": {"lat": 34, "lng": -123},
                    },
                },
                "name": "1600 Amphitheatre Pkwy",
                "place_id": "ChIJ2eUgeAK6j4ARbn5u_wAGqWA",
                "types": ["street_address"],
            }
        ],
    }
    return client


class LocationRouterTestCase(PluginsTestCase):
    components = [AuthComponent(), LocationComponent()]
    fixtures = [Path(__file__).parent.joinpath("fixtures/users_dump.json")]
    base_url = "/api/location/v1"

    @patch(PATCH_PATH, mocked_gmaps_client)
    def test_retrieve_location_by_coordinates_protected(self):
        url = f"{self.base_url}/coordinates-search"
        rsp = self.flask_client.get(
            url,
            query_string={
                RetrieveLocationByCoordinatesRequestObject.LATITUDE: 48.8583701,
                RetrieveLocationByCoordinatesRequestObject.LONGITUDE: 2.2922926,
            },
        )
        self.assertEqual(401, rsp.status_code)

    @patch(PATCH_PATH, mocked_gmaps_client)
    def test_retrieve_location_by_coordinates(self):
        url = f"{self.base_url}/coordinates-search"
        headers = {
            **self.get_headers_for_token(USER_ID),
            RequestContext.X_HU_LOCALE: Language.EN,
        }
        rsp = self.flask_client.get(
            url,
            query_string={
                RetrieveLocationByCoordinatesRequestObject.LATITUDE: 48.8583701,
                RetrieveLocationByCoordinatesRequestObject.LONGITUDE: 2.2922926,
            },
            headers=headers,
        )
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(SAMPLE_FORMATTED_ADDRESS, rsp.json["placeName"])
        self.assertEqual(SAMPLE_LATITUDE, rsp.json["latitude"])
        self.assertEqual(SAMPLE_LONGITUDE, rsp.json["longitude"])

    @patch(PATCH_PATH, mocked_gmaps_client)
    def test_failed_retrieve_location_by_coordinates_with_invalid_latitude(self):
        url = f"{self.base_url}/coordinates-search"
        headers = {
            **self.get_headers_for_token(USER_ID),
            RequestContext.X_HU_LOCALE: Language.EN,
        }
        rsp = self.flask_client.get(
            url,
            query_string={
                RetrieveLocationByCoordinatesRequestObject.LATITUDE: -98.2922926,
                RetrieveLocationByCoordinatesRequestObject.LONGITUDE: 2.2922926,
            },
            headers=headers,
        )
        self.assertEqual(403, rsp.status_code)

    @patch(PATCH_PATH, mocked_gmaps_client)
    def test_retrieve_locations_by_text(self):
        url = f"{self.base_url}/place-search"
        headers = {
            **self.get_headers_for_token(USER_ID),
            RequestContext.X_HU_LOCALE: Language.EN,
        }
        rsp = self.flask_client.get(
            url,
            query_string={
                RetrieveLocationsByTextRequestObject.QUERY: "Bake",
            },
            headers=headers,
        )
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(1, len(rsp.json))
        self.assertEqual(SAMPLE_FORMATTED_ADDRESS, rsp.json[0]["placeName"])
        self.assertEqual(SAMPLE_LATITUDE, rsp.json[0]["latitude"])
        self.assertEqual(SAMPLE_LONGITUDE, rsp.json[0]["longitude"])

    @patch(PATCH_PATH, mocked_gmaps_client)
    def test_failed_retrieve_locations_by_text_with_empty_query(self):
        url = f"{self.base_url}/place-search"
        headers = {
            **self.get_headers_for_token(USER_ID),
            RequestContext.X_HU_LOCALE: Language.EN,
        }
        rsp = self.flask_client.get(
            url,
            query_string={
                RetrieveLocationsByTextRequestObject.QUERY: "",
            },
            headers=headers,
        )
        self.assertEqual(403, rsp.status_code)

    @patch(PATCH_PATH, mocked_gmaps_client_without_formatted_address)
    def test_retrieve_places_without_formatted_address(
        self,
    ):
        url = f"{self.base_url}/place-search"
        headers = {
            **self.get_headers_for_token(USER_ID),
            RequestContext.X_HU_LOCALE: Language.EN,
        }
        rsp = self.flask_client.get(
            url,
            query_string={
                RetrieveLocationsByTextRequestObject.QUERY: "Bake",
            },
            headers=headers,
        )
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(rsp.json, [])
