import logging
from typing import Optional, Union

from flask import Blueprint

from huma_plugins.components.location.adapter.gmaps_location_adapter import (
    GoogleMapsLocationAdapter,
)
from huma_plugins.components.location.adapter.location_adapter import LocationAdapter
from huma_plugins.components.location.config.config import LocationConfig
from huma_plugins.components.location.router.location_router import api
from sdk.common.utils.inject import Binder
from sdk.phoenix.component_manager import PhoenixBaseComponent
from sdk.phoenix.config.server_config import PhoenixServerConfig

logger = logging.getLogger(__name__)


class LocationComponent(PhoenixBaseComponent):
    config_class = LocationConfig
    tag_name = "location"
    router = api

    def bind(self, binder: Binder, config: PhoenixServerConfig):
        config = config.server.adapters.googleMaps
        if not config:
            raise Exception("Google Maps config not found")

        binder.bind_to_provider(LocationAdapter, lambda: GoogleMapsLocationAdapter(config))
        logger.debug("GoogleMapsLocationAdapter bind to LocationAdapter")

    @property
    def blueprint(self) -> Optional[Union[Blueprint, list[Blueprint]]]:
        return self.router
