import logging

from apiflask import APIBlueprint
from flask import send_file

from huma_plugins.components.privacy_assets.router.request_object import (
    InstructionForUseRequestObject,
    RetrieveELabelRequestObject,
)
from huma_plugins.components.privacy_assets.use_cases.retrieve_e_label_use_case import (
    RetrieveELabelUseCase,
)
from huma_plugins.components.privacy_assets.use_cases.retrieve_ifu_use_case import (
    RetrieveIFUUseCase,
)
from sdk.common.usecase.response_object import html_response

api = APIBlueprint(
    "privacy_assets",
    __name__,
    url_prefix="/privacy",
    template_folder="templates",
    static_folder="static",
)

logger = logging.getLogger("PrivacyAssets")


@api.get("/instructions-for-use")
@api.input(InstructionForUseRequestObject.Schema, location="query", arg_name="req_obj")
@api.output({}, content_type="application/pdf")
def instructions_for_use(req_obj: InstructionForUseRequestObject):
    response_object = RetrieveIFUUseCase().execute(req_obj)
    return send_file(
        response_object.value.content,
        download_name=f"InstructionsForUse_{req_obj.type}.pdf",
    )


@api.get("/e-label")
@api.input(RetrieveELabelRequestObject.Schema, location="query", arg_name="request_obj")
@api.doc(**html_response())
def e_label(request_obj: RetrieveELabelRequestObject):
    return RetrieveELabelUseCase().execute(request_obj).html
