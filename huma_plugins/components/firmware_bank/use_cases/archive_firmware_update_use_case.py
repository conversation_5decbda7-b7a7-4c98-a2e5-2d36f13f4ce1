from packaging import version

from huma_plugins.components.firmware_bank.models.firmware_bank_models import (
    FirmwareDetails,
    ArchiveFirmwareRequestObject,
    ArchiveFirmwareResponseObject,
    ArchiveFirmwareResponseItem,
)
from huma_plugins.components.firmware_bank.use_cases.base_firmware_update_usecase import (
    BaseFirmwareUpdateUseCase,
)
from sdk.common.exceptions.exceptions import ObjectDoesNotExist


class ArchiveDeviceFirmwareUseCase(BaseFirmwareUpdateUseCase):
    request_object: ArchiveFirmwareRequestObject

    def process_request(self, request_object: ArchiveFirmwareRequestObject):
        prefix = f"{FirmwareDetails.FIRMWARE_UPDATES}/{request_object.make}/{request_object.model}/"
        archive_prefix = f"{FirmwareDetails.FIRMWARE_UPDATES}/archive/{request_object.make}/{request_object.model}/"
        files = self.share_file_storage.list_objects(
            self.shared_bucket,
            prefix=prefix,
        )
        files = self._remove_prefix(files)
        matched_files = self.find_matched_versions(files)
        if not matched_files:
            raise ObjectDoesNotExist("Firmware does not exist")

        moved_files = []
        failed_files = []
        for file in matched_files:
            old_name, new_name = f"{prefix}{file}", f"{archive_prefix}{file}"
            try:
                self.share_file_storage.copy(old_name, new_name, self.shared_bucket)
            except Exception as e:
                failed_files.append(ArchiveFirmwareResponseItem(old_name, repr(e)))

            else:
                self.share_file_storage.delete_object(self.shared_bucket, old_name)
                moved_files.append(ArchiveFirmwareResponseItem(new_name))

        return {
            ArchiveFirmwareResponseObject.ARCHIVED: moved_files,
            ArchiveFirmwareResponseObject.FAILED: failed_files,
        }

    def find_matched_versions(self, files: list) -> list[tuple[str, str]]:
        matched_versions = []
        for file in files:
            file_version = self._filename_to_version(file)
            if file_version is None:
                continue

            if version.parse(file_version) == version.parse(self.request_object.version):
                matched_versions.append(file)

        return matched_versions
