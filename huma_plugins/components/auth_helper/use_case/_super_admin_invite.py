import datetime as dt

from isodate import parse_duration

from sdk.authorization.adapters.email_invitation_adapter import EmailInvitationAdapter
from sdk.authorization.dtos.invitation import InvitationDTO
from sdk.authorization.dtos.user import RoleAssignmentDTO
from sdk.authorization.exceptions import UserDoesNotExist
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.authorization.repository.invitation_repository import InvitationRepository
from sdk.authorization.use_cases.invitation_use_cases import INVITATION_TOKEN
from sdk.common.adapter.token_adapter import TokenAdapter
from sdk.common.exceptions.exceptions import UserAlreadyExistsException
from sdk.common.usecase.response_object import ResultIdResponseObject
from sdk.common.utils import inject
from sdk.organization.repository.organization_repository import OrganizationRepository
from sdk.phoenix.config.project_config import Client
from sdk.phoenix.config.server_config import PhoenixServerConfig


class SuperAdminInvitationService:
    @inject.autoparams()
    def __init__(
        self,
        invitation_repo: InvitationRepository,
        invitation_adapter: EmailInvitationAdapter,
        token_adapter: TokenAdapter,
        server_config: PhoenixServerConfig,
        auth_repo: AuthorizationRepository,
        org_repo: OrganizationRepository,
    ):
        self._invitation_repo = invitation_repo
        self._invitation_adapter = invitation_adapter
        self._token_adapter = token_adapter
        self._config = server_config
        self._auth_repo = auth_repo
        self._org_repo = org_repo

    def invite(self, email: str, role_id: str) -> ResultIdResponseObject:
        if not self._invitation_should_be_sent(email):
            raise UserAlreadyExistsException

        invitation_id = self._invite(email, role_id)
        return ResultIdResponseObject(id=invitation_id)

    def _invitation_should_be_sent(self, email: str) -> bool:
        try:
            self._auth_repo.retrieve_simple_user_profile(email=email)
        except UserDoesNotExist:
            return True

        return False

    def _invite(self, email: str, role_id: str) -> str:
        invitation = self._create_invitation(email, role_id)
        self._invitation_adapter.send_admin_invitation_email(
            invitation.email,
            self._client,
            "en",
            invitation.shortenedCode,
            "Huma",
        )
        return invitation.id

    def _create_invitation(self, email: str, role_id: str) -> InvitationDTO:
        role = RoleAssignmentDTO.create_role(role_id, "*", "deployment")
        data = {
            InvitationDTO.EMAIL: email,
            InvitationDTO.CODE: self._create_invitation_token(email),
            InvitationDTO.ROLES: [role],
            InvitationDTO.EXPIRES_AT: self._expires_at,
            InvitationDTO.CLIENT_ID: self._client.clientId,
        }
        invitation = InvitationDTO.from_dict(data, ignore_none=True)
        invitation.id = self._invitation_repo.create_invitation(invitation)
        return invitation

    def _create_invitation_token(self, identity: str) -> str:
        return self._token_adapter.create_token(identity, INVITATION_TOKEN, expires_delta=parse_duration("P1D"))

    @property
    def _expires_at(self) -> dt.datetime:
        return dt.datetime.utcnow() + parse_duration("P1D")

    @property
    def _client(self) -> Client:
        return self._config.server.project.get_client_by_client_type(Client.ClientType.ADMIN_WEB)
