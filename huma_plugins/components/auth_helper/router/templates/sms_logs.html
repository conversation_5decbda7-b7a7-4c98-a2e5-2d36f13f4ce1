{% extends 'base.html' %}

{% block styles %}
  <link rel="stylesheet" type="text/css"
        href="{{ url_for("qa_helper.static", filename="css/daterangepicker.css") }}"/>
{% endblock %}

{% block content %}
  <div class="row">
    <div class="col-12 mt-4">
      <div class="card py-3 px-3">
        <form class="row" method="GET"
              action="{{ url_for("qa_helper.sms_logs") }}">
          <div class="form-group col-12 col-md-5 col-lg-4">
            <input type="text"
                   id="search"
                   class="form-control btn-rounded"
                   name="search"
                   placeholder="Email, User ID or Phone Number"
                   aria-label="Search Users"/>
          </div>
          <div class="form-group col-12 col-md-7 col-lg-8 d-flex mt-2 mt-md-0">
            <button id="datepicker" class="btn btn-sm btn-secondary bg-transparent"
                    type="button">
              <em class="fas fa-calendar-alt fa-2x text-muted c-pointer"></em>
            </button>
            <span id="dateLabel" class="text-muted py-2">Pick a date</span>
            <input type="text" id="date" name="date" aria-label="Date"
                   placeholder="Pick a date"
                   hidden/>
            <button class="btn btn-primary btn-rounded btn-dark ms-auto" type="submit">
              Search
            </button>
          </div>
        </form>

        <div class="divider-horizontal mt-3"></div>

        <ul id="userList"
            class="list-group list-group-light px-3 max-h-500 overflow-auto">
          {% if users %}

            {% for user in users %}
              <li class="list-group-item px-3 hover-bg-grey c-pointer"
                  id="{{ user.id }}">
                <div class="d-flex justify-content-between">
                  <div>
                    {% if user.email %}
                      <p>{{ user.email | truncate(35) }}</p>
                    {% else %}
                      <p>Email not set</p>
                    {% endif %}
                    <small class="text-muted">{{ user.id }}</small>
                  </div>
                  <div class="text-end">
                    {% for role in user.roles if not role.resource.startswith("user/") %}
                      {% if role.roleId | length == 24 %}
                        <p>Custom Role</p>
                      {% else %}
                        <p>{{ role.roleId }}</p>
                      {% endif %}
                    {% endfor %}
                  </div>
                </div>
              </li>
            {% endfor %}

          {% endif %}
        </ul>

        {% if logs %}
          <div class="accordion" id="accordionNotifications">
            <ul class="list-group list-group-flush">
              {% for log in logs %}
                <div class="accordion list-group-item">
                  <div class="accordion-header" id="heading{{ log.id }}">
                    <li class="list-group-item-flush my-2 d-flex accordion-button-flush collapsed"
                        data-mdb-toggle="collapse"
                        data-mdb-target="#collapse{{ log.id }}"
                        aria-expanded="false"
                        aria-controls="collapse{{ log.id }}">
                      {% if log.status == 'success' %}
                        <em class="fas fa-check fa-2x px-1"
                            style="color: #8ff6b7"></em>
                      {% elif log.status == 'failed' %}
                        <em class="fas fa-xmark fa-2x px-2"
                            style="color: #f88282"></em>
                      {% endif %}
                      <div class="d-flex flex-column"
                           style="margin-right:auto; margin-left:1rem">
                        <h5 class="mb-0">{{ log.phoneNumber }}</h5>
                        <p class="mb-0">{{ log.text | truncate(50) }}</p>
                      </div>
                      <div class="d-flex flex-column">
                        <div>
                          <em class="fa fa-key"></em>
                          <small
                              class="text-muted ml-auto">{{ log.id }}</small>
                        </div>
                        <div>
                          <em class="fa fa-user"></em>
                          <small
                              class="text-muted ml-auto">{{ log.phoneNumber }}</small>
                          <a target="_blank" rel="noopener"
                             href="{{ url_for("qa_helper.users", search=log.phoneNumber) }}">
                            <em class="fa fa-up-right-from-square"></em>
                          </a>
                        </div>
                      </div>
                      <div class="d-flex flex-column ms-5">
                        <small class="text-muted ml-auto">
                          {{ log.createDateTime.strftime("%d %b %Y %H:%M") }}
                        </small>
                      </div>
                    </li>
                  </div>
                  <div id="collapse{{ log.id }}"
                       class="accordion-collapse collapse"
                       aria-labelledby="heading{{ log.id }}"
                       data-mdb-parent="#accordionNotifications">
                    <div class="accordion-body">
                      <pre>{{ log.to_dict(include_none=False) | tojson_pretty | safe }}</pre>
                    </div>
                  </div>
                </div>
              {% endfor %}
            </ul>
          </div>

          {% if num_pages > 1 %}
            <nav aria-label="Sms Logs Navigation">
              <ul class="pagination justify-content-center my-3">
                {% if page == 1 %}
                  <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                  </li>
                {% else %}
                  <li class="page-item">
                    <a class="page-link"
                       href="{{ url_for('qa_helper.sms_logs', search=search, date=date, page=page - 1) }}"
                       tabindex="-1">Previous</a>
                  </li>
                {% endif %}

                {% for i in range(1, num_pages + 1) %}
                  {% if i == page %}
                    <li class="page-item active" aria-current="page">
                        <span class="page-link">{{ i }} <span
                            class="sr-only">(current)</span></span>
                    </li>
                  {% elif i == 1 or i == num_pages or page - 2 <= i <= page + 3 %}
                    <li class="page-item">
                      <a class="page-link"
                         href="{{ url_for('qa_helper.sms_logs', search=search, date=date, page=i) }}">{{ i }}</a>
                    </li>
                  {% endif %}
                {% endfor %}


                {% if page == num_pages %}
                  <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Next</a>
                  </li>
                {% else %}
                  <li class="page-item">
                    <a class="page-link"
                       href="{{ url_for('qa_helper.sms_logs', search=search, date=date, page=page + 1) }}">Next</a>
                  </li>
                {% endif %}
              </ul>
            </nav>
          {% endif %}

        {% endif %}
      </div>
    </div>
  </div>


{% endblock content %}

{% block script %}
  <script>
      const SELECTED = "list-item_selected";
      const userList = document.getElementById("userList");
      const date = document.getElementById("date");

      if (userList != null) {
          for (let userItem of userList.children) {
              userItem.addEventListener("click", onUserClick);
          }
      }

      function onUserClick(event) {
          let element = event.target.closest(".list-group-item");

          selectUser(element);
      }

      function selectUser(element) {
          element.classList.add(SELECTED);
          element.scrollIntoView({behavior: "smooth", block: "center"});
          updateSearchParam(element.id);
      }

      function updateSearchParam(userId) {
          debounce(() => {
              let url = new URL(window.location.href);
              url.searchParams.delete("search");
              url.searchParams.append("search", userId);

              // if location changed, reload page
              let newUrl = url.toString();
              if (window.location.href !== newUrl) {
                  reloadPage(newUrl);
              }
          })();
      }

      function parseQueryParams() {
          let url = new URL(window.location.href);
          url.searchParams.forEach((value, key) => {
              let target = document.getElementById(key);
              if (target) target.value = value;
              if (key === "search") {
                  let userItem = document.getElementById(value);
                  if (userItem) selectUser(userItem);
              } else if (key === "date") {
                  let dateLabel = document.getElementById("dateLabel");
                  dateLabel.textContent = value;
              }
          });
      }

      parseQueryParams();

  </script>


  <script type="text/javascript"
          src="{{ url_for("qa_helper.static", filename="js/moment.min.js") }}"></script>
  <script type="text/javascript"
          src="{{ url_for("qa_helper.static", filename="js/daterangepicker.min.js") }}"></script>
  <script src="{{ url_for("qa_helper.static", filename="js/utils.js") }}"></script>
  <script>
      $(function () {
          let dateInput = document.getElementById("date");
          let dateLabel = document.getElementById("dateLabel");

          let start;
          let end;
          if (dateInput.value !== "") {
              let parts = dateInput.value.split(" - ");
              start = parts[0];
              end = parts[1];
              start = moment(start, "DD-MM-YYYY")
              end = moment(end, "DD-MM-YYYY")
          } else {
              end = moment();
              start = moment().subtract({days: 1});
          }

          function onDatesPicked(start, end) {
              start = start.format('DD-MM-YYYY');
              end = end.format('DD-MM-YYYY');
              let value = start === end ? start : `${start} - ${end}`;
              dateLabel.textContent = dateInput.value = value;
          }

          $('#datepicker').daterangepicker({
              startDate: start,
              endDate: end,
              autoApply: true,
          }, onDatesPicked);
          onDatesPicked(start, end);
      });
  </script>
{% endblock script %}
