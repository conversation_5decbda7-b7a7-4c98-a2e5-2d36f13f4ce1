from huma_plugins.components.natural_conditions.adapter import NaturalConditionsAdapter
from huma_plugins.components.natural_conditions.router import (
    RetrieveNaturalConditionsRequestObject,
    RetrieveNaturalConditionsResponse,
)
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams


class RetrieveNaturalConditionsUseCase(UseCase):
    @autoparams()
    def __init__(self, adapter: NaturalConditionsAdapter):
        self._adapter = adapter

    def process_request(self, request_object: RetrieveNaturalConditionsRequestObject):
        air_quality = self._adapter.retrieve_air_quality_data(
            latitude=request_object.latitude,
            longitude=request_object.longitude,
        )
        pollen_data = self._adapter.retrieve_pollen_data(
            latitude=request_object.latitude,
            longitude=request_object.longitude,
            language=request_object.language.value,
        )
        return RetrieveNaturalConditionsResponse.from_dict(dict(airQuality=air_quality, pollen=pollen_data))
