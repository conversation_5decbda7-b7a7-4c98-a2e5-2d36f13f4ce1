{"deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2a"}, "name": "Reminder Test", "status": "DEPLOYED", "moduleConfigs": [{"moduleId": "BloodPressure", "moduleName": "BloodPressure", "status": "ENABLED", "id": {"$oid": "608089adc97ce231cb445eb4"}, "notificationData": {"title": "hu_blood_pressure_reminder_title", "body": "hu_blood_pressure_reminder_body"}, "order": 1, "configBody": {}, "about": "", "version": 0}, {"moduleId": "OxygenSaturation", "moduleName": "Oxygen Saturation", "status": "ENABLED", "id": {"$oid": "642c720d5ac8016189e20510"}, "notificationData": {"title": "Reminder", "body": "For Oxygen Saturation"}, "order": 2, "configBody": {"dataSource": {"fitbitCollection": true}}, "about": "", "version": 0}, {"moduleId": "SelfRatedHealth", "about": "Self-Rated Health is a single question that captures how healthy you think you are.", "configBody": {"buttons": [{"text": "Common.Next", "type": "NEXT"}, {"text": "hu_srh_submission_button", "type": "SUBMIT"}], "id": "SelfRatedHealth", "isForManager": false, "maxScore": 5, "name": "hu_srh_moduleName", "pages": [{"items": [{"description": "", "format": "TEXTCHOICE", "id": "hu_srh_health", "options": [{"label": "hu_srh_health_label1", "value": "1", "weight": 1}, {"label": "hu_srh_health_label2", "value": "2", "weight": 2}, {"label": "hu_srh_health_label3", "value": "3", "weight": 3}, {"label": "hu_srh_health_label4", "value": "4", "weight": 4}, {"label": "hu_srh_health_label5", "value": "5", "weight": 5}], "order": 1, "required": true, "selectionCriteria": "SINGLE", "text": "hu_srh_health_text"}], "order": 1, "type": "QUESTION"}], "publisherName": "RT", "submissionPage": {"buttonText": "hu_srh_submission_button", "description": "hu_srh_submission_description", "id": "hu_srh_submission", "order": 2, "text": "hu_srh_submission_text", "type": "SUBMISSION"}}, "createDateTime": "2023-06-13T09:40:07.470000Z", "id": "64883977c4889b6d8c753024", "localizationPrefix": "hu_srh", "moduleName": "", "order": 3, "ragThresholds": [], "schedule": {"friendlyText": "AS NEEDED", "isoDuration": "P1D", "timesOfDay": [], "timesPerDuration": 1}, "status": "ENABLED", "updateDateTime": "2023-06-13T09:40:07.470000Z", "version": 1}, {"id": {"$oid": "5f1824ba504787d8d89ebeca"}, "moduleId": "MedicationsV2", "moduleName": "Medications", "notificationData": {"title": "Medications", "body": "Take your %time %medicationName"}, "about": "Medications are an important part of your care plan. You can use the medications list to keep track of, and refer to dosages as and when you need to. Your care team will have access to this list and can add medications when needed. Please note, the list is not a prescription.", "status": "ENABLED", "configBody": {"types": {"inhaler": {"medicationNames": ["Symbicort"], "units": ["puffs"]}}, "flows": {"inhaler": {"frequencies": {"relieve": {"lowerBound": 5, "upperBound": 10}, "maintain": {"lowerBound": 1, "upperBound": 10}, "relieveMaintain": {"lowerBound": 10, "upperBound": 300}}, "scheduleBuffer": "PT1H"}}}}, {"id": {"$oid": "644bdea1dfb15c2fdcfd50bc"}, "about": "Sleep config", "configBody": {}, "moduleId": "Sleep", "moduleName": "Sleep", "ragThresholds": [], "schedule": {}, "status": "ENABLED"}, {"id": {"$oid": "5e94b2007773091c9a592995"}, "moduleId": "Medications", "about": "string", "moduleName": "Medications", "schedule": {"isoDuration": "P1W", "timesPerDuration": 1, "timesOfDay": ["BEFORE_DINNER"]}, "status": "ENABLED"}, {"id": {"$oid": "5e94b2007773091c9a592652"}, "moduleId": "Questionnaire", "about": "string", "configBody": {"id": "749e6294-034e-4366-a9c9-83027d5c0fe6", "filledBy": "User", "pages": [], "groupedQuestionnaire": true}, "moduleName": "Questionnaire", "status": "ENABLED"}], "localizations": {"en": {"hu_blood_pressure_reminder_title": "Test Blood Pressure", "hu_blood_pressure_reminder_body": "Test BP body"}, "nl": {"hu_blood_pressure_reminder_title": "<PERSON><PERSON>edd<PERSON> testen", "hu_blood_pressure_reminder_body": "Test bloeddruk lichaam"}}}], "calendar": [{"_id": {"$oid": "5e8cc88d0e8f49bbe59d11bd"}, "updateDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "createDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "startDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "enabled": true, "userId": {"$oid": "5e84b0dab8dfa268b1180536"}, "model": "UserModuleReminder", "_cls": "MongoCalendarEvent", "recurrencePattern": "DTSTART:20200407T212417\nRRULE:FREQ=DAILY;BYHOUR=10;BYMINUTE=20", "isRecurring": true, "extraFields": {"moduleName": "BloodPressure", "moduleId": "BloodPressure", "moduleConfigId": "608089adc97ce231cb445eb4", "durationIso": "PT10H20M", "specificWeekDays": ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"]}}, {"_id": {"$oid": "5e8cc56eac80d64b9f06a801"}, "updateDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "createDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "startDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "enabled": false, "userId": {"$oid": "5e84b0dab8dfa268b1180536"}, "model": "UserModuleReminder", "_cls": "MongoCalendarEvent", "recurrencePattern": "DTSTART:20200407T212417\nRRULE:FREQ=WEEKLY;BYDAY=TU;BYHOUR=20;BYMINUTE=30", "isRecurring": true, "extraFields": {"moduleName": "BloodPressure", "durationIso": "PT20H30M", "specificWeekDays": ["TUE"], "moduleId": "BloodPressure", "moduleConfigId": "608089adc97ce231cb445eb4"}}, {"_id": {"$oid": "5e8cc88d0e8f49bbe59d11be"}, "updateDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "createDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "startDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "enabled": true, "userId": {"$oid": "5e84b0dab8dfa268b1180537"}, "model": "UserModuleReminder", "_cls": "MongoCalendarEvent", "recurrencePattern": "DTSTART:20200407T212417\nRRULE:FREQ=DAILY;BYHOUR=10;BYMINUTE=20", "isRecurring": true, "extraFields": {"moduleName": "BloodPressure", "moduleId": "BloodPressure", "moduleConfigId": "608089adc97ce231cb445eb4", "durationIso": "PT10H20M", "specificWeekDays": ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"]}}, {"_id": {"$oid": "5e84b0dab8dfa268b1180536"}, "updateDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "createDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "startDateTime": {"$date": "2020-04-07T21:24:17.218Z"}, "enabled": true, "userId": {"$oid": "5e8cc88d0e8f49bbe59d11bd"}, "model": "UserModuleReminder", "_cls": "MongoCalendarEvent", "recurrencePattern": "DTSTART:20200407T212417\nRRULE:FREQ=MONTHLY;BYMONTHDAY=1,3;BYHOUR=10;BYMINUTE=20", "isRecurring": true, "extraFields": {"moduleName": "BloodPressure", "durationIso": "PT10H20M", "moduleId": "BloodPressure", "moduleConfigId": "608089adc97ce231cb445eb4", "specificMonthDays": [1, 3]}}], "huma_auth_user": [{"_id": {"$oid": "5e84b0dab8dfa268b1180536"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+441700000000", "displayName": "Super"}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999999", "displayName": "test", "userAttributes": {"familyName": "test", "givenName": "test", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}, {"_id": {"$oid": "642cac5bf5e874463e89afad"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999998", "displayName": "testUser", "userAttributes": {"familyName": "testUser", "givenName": "testUser", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "1586422340"}}, "updateDateTime": {"$date": {"$numberInt": "1586422340"}}}], "user": [{"_id": {"$oid": "642cac5bf5e874463e89afad"}, "givenName": "Super", "familyName": "Admin", "email": "<EMAIL>", "phoneNumber": "+441700000000", "roles": [{"roleId": "SuperAdmin", "resource": "deployment/*", "userType": "SuperAdmin", "isActive": true}]}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999999999", "roles": [{"roleId": "Admin", "resource": "deployment/5d386cc6ff885918d96edb2a", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "5e84b0dab8dfa268b1180536"}, "givenName": "testUser", "familyName": "testUser", "email": "<EMAIL>", "phoneNumber": "+380999999998", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2a", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "5e84b0dab8dfa268b1180537"}, "givenName": "testUser1", "familyName": "testUser1", "email": "<EMAIL>", "phoneNumber": "+380999999997", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2a", "isActive": true}], "timezone": "UTC"}]}