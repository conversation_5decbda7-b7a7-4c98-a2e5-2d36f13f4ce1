from datetime import datetime, timedelta

from huma_plugins.components.reminder.models.reminder import UserModuleReminder
from huma_plugins.components.reminder.models.user_reminder import UserPersonalReminder
from huma_plugins.components.reminder.router.reminder_requests import (
    RemindersFilterOption,
    RetrieveEventsRequestObject,
)
from huma_plugins.components.reminder.router.responses import RetrieveRemindersResponse
from sdk.calendar.dtos.calendar_event import CalendarEventDTO
from sdk.calendar.service.calendar_service import CalendarService
from sdk.calendar.utils import get_dt_from_str
from sdk.common.constants import VALUE_IN
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.date_utils import localize_from_utc, localize_to_utc


class RetrieveCalendarEventsUseCase(UseCase):
    request_object = RetrieveEventsRequestObject

    def __init__(self):
        self._calendar_service = CalendarService()

    def process_request(self, request_object: RetrieveEventsRequestObject) -> RetrieveRemindersResponse:
        days_in_year = 365
        days_in_month = 30
        user_12_am = self._today_12_am()

        def condition(event: CalendarEventDTO):
            if request_object.filter is RemindersFilterOption.ALL:
                return True
            reminder_start = get_dt_from_str(event.startDateTime)
            if request_object.filter is RemindersFilterOption.HISTORY:
                return not event.enabled or reminder_start < user_12_am
            if request_object.filter is RemindersFilterOption.DONE:
                return event.completeDateTime is not None
            if request_object.filter is RemindersFilterOption.CURRENT:
                return event.enabled and event.completeDateTime is None and reminder_start > user_12_am
            if request_object.filter is RemindersFilterOption.UPCOMING:
                return reminder_start > datetime.now()
            return True

        end_date = datetime.now() + timedelta(days=days_in_year)

        history_depth = days_in_month
        if request_object.filter is RemindersFilterOption.HISTORY:
            history_depth = days_in_year

        start_date = datetime.now() - timedelta(days=history_depth)

        if request_object.filter is RemindersFilterOption.UPCOMING:
            start_date = datetime.now()

        reminders = self._calendar_service.retrieve_calendar_events_between_two_dates(
            start=start_date,
            end=end_date,
            timezone=request_object.user.user.timezone,
            enabled=True,
            userId=request_object.user.id,
            model={VALUE_IN: [UserModuleReminder.__name__, UserPersonalReminder.__name__]},
        )
        skip = request_object.skip
        limit = request_object.limit
        reminders = list(filter(condition, reminders))
        total = len(reminders)
        reminders.sort(key=lambda x: x.startDateTime)
        return RetrieveRemindersResponse(
            reminders=reminders[skip : skip + limit],
            skip=skip,
            limit=limit,
            total=total,
        )

    def _today_12_am(self):
        """
        Returns the current day at 12:00 AM in the user's timezone.
        """
        user_timezone = self.request_object.user.user.timezone
        user_date_at_12_am = localize_from_utc(datetime.utcnow(), user_timezone).replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        return localize_to_utc(user_date_at_12_am, user_timezone)
