from huma_plugins.components.reminder.models.reminder import UserModuleReminder
from huma_plugins.components.reminder.router.reminder_requests import (
    DeleteRemindersRequestObject,
    DeleteUserRemindersRequestObject,
)
from huma_plugins.components.reminder.use_case.delete_reminders_use_case import (
    DeleteRemindersUseCase,
)
from huma_plugins.components.reminder.use_case.delete_user_reminders_use_case import (
    DeleteUserRemindersUseCase,
)
from sdk.authorization.events import PostUserProfileUpdateEvent, PostUserOffBoardEvent
from sdk.authorization.dtos.role.role import RoleName
from sdk.calendar.service.calendar_service import CalendarService
from sdk.common.monitoring import report_exception
from sdk.deployment.events import ModuleConfigDeleteDisableEvent
from sdk.deployment.dtos.deployment import DeploymentDTO
from sdk.deployment.service.deployment_service import DeploymentService


def update_reminders_language_localization(event: PostUserProfileUpdateEvent):
    if not (new_language := event.updated_fields.language):
        return
    if not event.previous_state:
        return

    old_language = event.previous_state.language
    user_id = event.previous_state.id
    role = event.previous_state.roles[0]

    if role.roleId != RoleName.USER or old_language == new_language:
        return

    calendar_service = CalendarService()
    deployment = DeploymentService().retrieve_deployment(deployment_id=role.resource_id())
    events: list[UserModuleReminder] = calendar_service.retrieve_raw_events(  # type: ignore
        userId=user_id, model=UserModuleReminder.__name__
    )
    for cal_event in events:
        if not cal_event.moduleId or not cal_event.moduleConfigId:
            continue

        reminder_title, reminder_description = _get_module_reminder_translations(
            deployment, new_language, cal_event.moduleId, cal_event.moduleConfigId
        )
        if not (reminder_title and reminder_description):
            continue

        cal_event.title = reminder_title
        cal_event.description = reminder_description

        try:
            calendar_service.update_calendar_event(cal_event.id, cal_event, None)
        except Exception as error:
            report_exception(error)


def _get_module_reminder_translations(
    deployment: DeploymentDTO, new_language: str, module_id: str, module_config_id: str
):
    new_locales = deployment.get_localization(new_language)
    module = deployment.find_module_config(module_id, module_config_id)

    if module and module.notificationData:
        title = module.notificationData.title
        body = module.notificationData.body
        return new_locales.get(title), new_locales.get(body)

    return None, None


def process_module_config_deletion_disablement_for_reminders(
    event: ModuleConfigDeleteDisableEvent,
):
    req_obj = DeleteRemindersRequestObject.from_dict(
        {DeleteRemindersRequestObject.MODULE_CONFIG_ID: event.module_config_id}
    )
    DeleteRemindersUseCase().execute(req_obj)


def delete_user_reminders(event: PostUserOffBoardEvent):
    req_obj = DeleteUserRemindersRequestObject.from_dict({DeleteUserRemindersRequestObject.USER_ID: event.user_id})
    DeleteUserRemindersUseCase().execute(req_obj)
