from huma_plugins.components.reminder.models.reminder import UserModuleReminder
from sdk import convertibleclass
from sdk.common.usecase.response_object import ResponseObject
from sdk.common.utils.convertible import required_field
from sdk.common.utils.marshmallow.schemas import BaseSchema


@convertibleclass
class ReminderResponseData(UserModuleReminder, ResponseObject):
    class Meta(BaseSchema.Meta):
        exclude = [UserModuleReminder.EXTRA_FIELDS]


@convertibleclass
class RetrieveRemindersResponse(ResponseObject):
    REMINDERS = "reminders"
    TOTAL = "total"
    SKIP = "skip"
    LIMIT = "limit"

    reminders: list[ReminderResponseData] = required_field()
    total: int = required_field()
    skip: int = required_field()
    limit: int = required_field()
