from flask import g

from huma_plugins.components.diary.router.diary_requests import (
    RetrieveDiariesCountRequestObject,
    RetrieveDiariesListRequestObject,
    RetrieveDiariesRequestObject,
    RetrieveDiaryByIdRequestObject,
)
from huma_plugins.components.diary.router.diary_responses import (
    DiaryCategoryResponse,
    RetrieveDiariesCountResponseObject,
    RetrieveDiariesResponseObject,
    RetrieveDiaryByIdResponse,
)
from huma_plugins.components.diary.use_case.diary_use_case import (
    RetrieveDiariesCountUseCase,
    RetrieveDiariesUseCase,
    RetrieveSupportedDiariesUseCase,
    RetrieveDiaryByIdUseCase,
)
from sdk.security import Access, ProtectedBlueprint

api = ProtectedBlueprint(
    "diary_route",
    __name__,
    url_prefix="/api/extensions/v1/user",
)


@api.post("/<user_id>/diary/search")
@api.requires(Access.SELF.VIEW_DATA | Access.USER.VIEW_DATA)
@api.input(RetrieveDiariesRequestObject.Schema, arg_name="request_object")
@api.output(RetrieveDiariesResponseObject.Schema, 200)
def retrieve_diaries(user_id, request_object: RetrieveDiariesRequestObject):
    return RetrieveDiariesUseCase().execute(request_object)


@api.get("/<user_id>/diary/count")
@api.requires(Access.SELF.VIEW_DATA | Access.USER.VIEW_DATA)
@api.output(RetrieveDiariesCountResponseObject.Schema(many=True))
def retrieve_diaries_count(user_id):
    req = RetrieveDiariesCountRequestObject.from_dict({RetrieveDiariesCountRequestObject.USER_ID: user_id})
    return RetrieveDiariesCountUseCase().execute(req)


@api.get("/<user_id>/diary/list")
@api.requires(Access.SELF.VIEW_DATA | Access.USER.VIEW_DATA)
@api.output(DiaryCategoryResponse.Schema(many=True))
def retrieve_diaries_list(user_id):
    req = RetrieveDiariesListRequestObject.from_dict(
        {RetrieveDiariesListRequestObject.DEPLOYMENT_ID: g.authz_path_user.deployment_id()}
    )
    return RetrieveSupportedDiariesUseCase().execute(req)


@api.get("/<user_id>/diary/<id>")
@api.requires(Access.SELF.VIEW_DATA | Access.USER.VIEW_DATA)
@api.input(RetrieveDiaryByIdRequestObject.Schema, arg_name="request_object")
@api.output(RetrieveDiaryByIdResponse.Schema)
def retrieve_diary_by_id(user_id, id, request_object: RetrieveDiaryByIdRequestObject):
    return RetrieveDiaryByIdUseCase().execute(request_object)
