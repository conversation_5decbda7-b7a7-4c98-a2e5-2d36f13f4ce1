from huma_plugins.components.diary.tests.IntegrationTests.samples import START_DATE_TIME
from sdk.module_result.modules import WHQModule

WHQ_MC_ID = "674ed5cf050e7626aade3cfd"


def whq_primitive_dict() -> dict:
    return {
        "moduleId": WHQModule.moduleId,
        "moduleConfigId": WHQ_MC_ID,
        "value": 17,
        "ragThreshold": {"WHQ": {"value": {"color": "#FFFFFF", "severity": 1, "isCustom": False}}},
        "flags": {"gray": 0, "amber": 0, "red": 0},
        "source": None,
    }


def whq_questionnaire_dict() -> dict:
    return {
        "moduleId": WHQModule.moduleId,
        "moduleConfigId": WHQ_MC_ID,
        "source": None,
        "answers": [],
        "questionnaireId": "questionnaire_id",
        "questionnaireName": "questionnaire_name",
    }


def whq_diary_dict() -> dict:
    return {
        "details": [
            {"key": "Bluebelle Wound Healing Questionnaire (out of 41)", "value": "17"},
            {"key": "Date & Time", "value": START_DATE_TIME},
        ],
        "diaryBG": "#FFFFFF",
        "diaryChips": [],
        "diarySidebar": "#B7B8B9",
        "diaryType": "WHQ",
        "primitiveType": "WHQ",
        "moduleConfigId": WHQ_MC_ID,
        "flags": {"amber": 0, "gray": 1, "red": 0},
        "title": "<b>Bluebelle Wound Healing Questionnaire</b>",
        "unit": ["out of 41"],
        "value": ["17"],
    }
