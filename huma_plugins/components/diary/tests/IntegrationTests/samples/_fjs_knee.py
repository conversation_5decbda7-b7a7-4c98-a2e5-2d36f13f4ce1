from huma_plugins.components.diary.tests.IntegrationTests.samples import START_DATE_TIME
from sdk.module_result.modules import FJSKneeScoreModule

FJS_KNEE_MC_ID = "674ed3dd050e7626aade3cdc"


def fjs_knee_score_questionnaire_dict() -> dict:
    return {
        "moduleId": FJSKneeScoreModule.moduleId,
        "moduleConfigId": FJS_KNEE_MC_ID,
        "source": None,
        "answers": [],
        "questionnaireId": "92e6bbae-1633-4f9c-b5ee-dbd83727a313",
        "questionnaireName": "FJSKnee",
        "value": 0,
        "ragThreshold": {"FJSKnee": {"value": {"color": "#FBCCD7", "severity": 3, "isCustom": False}}},
        "flags": {"red": 1, "amber": 0, "gray": 0},
    }


def fjs_knee_score_diary_dict() -> dict:
    return {
        "details": [
            {"key": "FJS Knee Score (out of 100)", "value": "0"},
            {"key": "Date & Time", "value": START_DATE_TIME},
        ],
        "diaryBG": "#FDE5EB",
        "diaryChips": [],
        "diarySidebar": "#EB0037",
        "diaryType": "FJSKnee",
        "primitiveType": "Questionnaire",
        "moduleConfigId": FJS_KNEE_MC_ID,
        "flags": {"amber": 0, "gray": 0, "red": 1},
        "title": "<b>FJS Knee Score</b>",
        "unit": ["out of 100"],
        "value": ["0"],
    }
