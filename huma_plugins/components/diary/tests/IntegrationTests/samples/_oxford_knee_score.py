from huma_plugins.components.diary.tests.IntegrationTests.samples import START_DATE_TIME
from sdk.module_result.modules import OxfordKneeScoreQuestionnaireModule

OXFORD_KNEE_SCORE_MC_ID = "674ed4ce050e7626aade3cec"


def oxford_knee_score_primitive_dict() -> dict:
    return {
        "moduleId": OxfordKneeScoreQuestionnaireModule.moduleId,
        "moduleConfigId": OXFORD_KNEE_SCORE_MC_ID,
        "legsData": [
            {
                "pain": 4,
                "washing": 3,
                "transport": 3,
                "walk": 4,
                "meal": 2,
                "limping": 2,
                "kneelDown": 3,
                "bed": 4,
                "usualWork": 2,
                "letYouDown": 4,
                "shopping": 3,
                "stairs": 3,
                "score": 23,
                "legAffected": 0,
            }
        ],
        "legAffected": 0,
        "rightKneeScore": 23,
        "ragThreshold": {
            "OxfordKneeScore": {
                "leftKneeScore": {},
                "rightKneeScore": {
                    "color": "#FFDA9F",
                    "severity": 2,
                    "isCustom": False,
                },
                "severities": [2],
            }
        },
        "flags": {"red": 0, "amber": 1, "gray": 0},
        "source": None,
    }


def oxford_knee_score_diary_dict() -> dict:
    return {
        "details": [
            {"key": "Oxford Knee Score Left|Right (out of 48)", "value": "---|23"},
            {"key": "Date & Time", "value": START_DATE_TIME},
        ],
        "diaryBG": "#FFF7EB",
        "diaryChips": [],
        "diarySidebar": "#FFDA9F",
        "diaryType": "OxfordKneeScore",
        "primitiveType": "OxfordKneeScore",
        "moduleConfigId": OXFORD_KNEE_SCORE_MC_ID,
        "flags": {"amber": 1, "gray": 0, "red": 0},
        "title": "<b>Oxford Knee Score</b>",
        "value": ["---|23"],
        "unit": ["Left|Right (out of 48)"],
    }
