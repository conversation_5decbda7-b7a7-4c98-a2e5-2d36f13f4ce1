from huma_plugins.components.diary.tests.IntegrationTests.samples import START_DATE_TIME
from sdk.module_result.modules import RespiratoryRateModule

RESPIRATORY_RATE_MC_ID = "674ed550050e7626aade3cf4"


def respiratory_rate_primitive_dict() -> dict:
    return {
        "moduleId": RespiratoryRateModule.moduleId,
        "moduleConfigId": RESPIRATORY_RATE_MC_ID,
        "value": 25,
        "source": "HumaDeviceKit; version: 1.0.0",
        "ragThreshold": {"RespiratoryRate": {"value": {"color": "#FBCCD7", "severity": 3, "isCustom": False}}},
        "flags": {"red": 1, "amber": 0, "gray": 0},
    }


def respiratory_rate_diary_dict() -> dict:
    return {
        "details": [
            {"key": "Respiratory Rate (rpm)", "value": "25"},
            {"key": "Date & Time", "value": START_DATE_TIME},
            {"key": "Measure type", "value": "HumaDeviceKit"},
        ],
        "diaryBG": "#FDE5EB",
        "diaryChips": [{"text": "HumaDeviceKit"}],
        "diarySidebar": "#EB0037",
        "diaryType": "RespiratoryRate",
        "primitiveType": "RespiratoryRate",
        "moduleConfigId": RESPIRATORY_RATE_MC_ID,
        "flags": {"amber": 0, "gray": 0, "red": 1},
        "title": "<b>Respiratory Rate</b>",
        "unit": ["rpm"],
        "value": ["25"],
    }
