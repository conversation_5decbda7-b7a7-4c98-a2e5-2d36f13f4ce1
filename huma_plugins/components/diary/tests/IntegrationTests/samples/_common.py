from huma_plugins.components.diary.tests.IntegrationTests.samples import (
    MOCK_NOW,
    SAMPLE_MODULE_RESULT_ID,
    START_DATE_TIME,
    USER_NO_DIARIES,
)


def common_diary_fields() -> dict:
    return {
        "userId": USER_NO_DIARIES,
        "diaryId": SAMPLE_MODULE_RESULT_ID,
        "diarySourceId": SAMPLE_MODULE_RESULT_ID,
        "detailsPresentation": "BOLD_BOTTOM",
        "visibility": ["CP", "APPS", "WIDGET"],
        "startDateTime": START_DATE_TIME,
        "updateDateTime": MOCK_NOW,
        "createDateTime": MOCK_NOW,
    }


def gray_rag_and_diary_colors() -> dict:
    return {
        "diaryBG": "#FFFFFF",
        "diarySidebar": "#B7B8B9",
        "flags": {"amber": 0, "gray": 1, "red": 0},
    }


def amber_rag_and_diary_colors() -> dict:
    return {
        "diaryBG": "#FFF7EB",
        "diarySidebar": "#FFDA9F",
        "flags": {"amber": 1, "gray": 0, "red": 0},
    }


def red_rag_and_diary_colors() -> dict:
    return {
        "diaryBG": "#FDE5EB",
        "diarySidebar": "#EB0037",
        "flags": {"amber": 0, "gray": 0, "red": 1},
    }
