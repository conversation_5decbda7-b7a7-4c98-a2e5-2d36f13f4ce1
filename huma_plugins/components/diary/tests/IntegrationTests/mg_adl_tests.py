from huma_plugins.components.diary.callbacks.diary_callback import (
    diary_module_result_callback,
)
from huma_plugins.components.diary.tests.IntegrationTests import (
    BaseDiaryTestCase,
    mock_utcnow,
)
from huma_plugins.components.diary.tests.IntegrationTests.samples import (
    mg_adl_diaries_response,
    mg_adl_primitive_dict,
    mg_adl_questionnaire_dict,
    mg_adl_submit_module_result_payload,
)
from sdk.module_result.modules import MGADLModule
from sdk.module_result.modules.mg_adl import MG_ADLDTO
from sdk.module_result.modules.questionnaire import QuestionnaireDTO


class MGADLTestCase(BaseDiaryTestCase):
    @mock_utcnow
    def test_create_and_search_mg_adl_diary(self):
        mg_adl_primitive = self._create_primitive(mg_adl_primitive_dict(), MG_ADLDTO.get_primitive_name())
        questionnaire_primitive = self._create_primitive(
            mg_adl_questionnaire_dict(), QuestionnaireDTO.get_primitive_name()
        )
        event = self._post_create_module_result_batch_event(
            [questionnaire_primitive, mg_adl_primitive, questionnaire_primitive]
        )
        diary_module_result_callback(event, False)

        diary_item_rsp = self._retrieve_single_created_diary(expected_diaries_count=5)
        expected_rsp = mg_adl_diaries_response()
        self.assertEqual(expected_rsp, diary_item_rsp)

    def test_order_of_diaries(self):
        payload = mg_adl_submit_module_result_payload()
        self._submit_module_results(payload, MGADLModule.moduleId)

        diary_item_rsp = self._retrieve_single_created_diary(expected_diaries_count=5)
        self.assertEqual("<b>Overall summary score</b> (MG-ADL)", diary_item_rsp[0]["title"])
        self.assertEqual("<b>Ocular dysfunction</b> (MG-ADL)", diary_item_rsp[1]["title"])
        self.assertEqual("<b>Respiratory dysfunction</b> (MG-ADL)", diary_item_rsp[2]["title"])
        self.assertEqual("<b>Extremity dysfunction</b> (MG-ADL)", diary_item_rsp[3]["title"])
        self.assertEqual("<b>Oropharyngeal dysfunction</b> (MG-ADL)", diary_item_rsp[4]["title"])
