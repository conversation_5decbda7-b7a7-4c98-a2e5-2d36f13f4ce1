from huma_plugins.components.kardia.router import RetrievePatientRecordingsRequestObject
from ._base import BaseKardiaUseCase


class RetrievePatientRecordingsUseCase(BaseKardiaUseCase):
    def process_request(self, request_object: RetrievePatientRecordingsRequestObject):
        patient_recordings = self._kardia_integration_client.retrieve_patient_recordings(request_object.patientId)
        return patient_recordings
