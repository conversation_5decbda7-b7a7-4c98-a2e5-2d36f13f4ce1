from huma_plugins.components.report_incident.modules.report_incident import ReportIncidentModule

from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.services.authorization import AuthorizationService
from sdk.authorization.use_cases.assign_static_label_to_user_use_case import assign_static_label
from sdk.module_result.event_bus.post_create_module_results_batch_event import PostCreateModuleResultBatchEvent


def assign_immediate_action_static_label_to_user(event: PostCreateModuleResultBatchEvent):
    """Assigns "IMMEDIATE_ACTION" static label to a user when they submit an Incident Report module result."""
    if event.module_id != ReportIncidentModule.moduleId:
        return

    label_text = "IMMEDIATE_ACTION"
    service = AuthorizationService()
    user = service.retrieve_simple_user_profile(user_id=event.user_id)
    auth_user = AuthorizedUser(user)
    if not auth_user.is_user():
        return

    deployment = auth_user.deployment
    if not deployment.features or not deployment.features.labels:
        return

    system_user = service.retrieve_system_user()
    assign_static_label(label_text, event.user_id, system_user, deployment)
