from huma_plugins.components.user_rating.use_cases._base import BaseUserRatingUseCase
from huma_plugins.components.user_rating.router.rating_request_objects import (
    SearchArticleRatingRequestObject,
)


class SearchArticleRatingUseCase(BaseUserRatingUseCase):
    def process_request(self, request_object: SearchArticleRatingRequestObject):
        return self._service.search_article_rating(
            request_object.userId, request_object.articleType.value, request_object.articleId
        )
