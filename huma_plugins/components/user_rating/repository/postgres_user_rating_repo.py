import datetime as dt

from bson import ObjectId

from huma_plugins.components.cms.models import CMSContent
from huma_plugins.components.user_rating.models.user_rating import UserRating
from sdk.common.exceptions.exceptions import ObjectDoesNotExist
from sdk.common.utils.validators import remove_none_values, model_to_dict
from huma_plugins.components.user_rating.exceptions import ArticleNotFoundException
from huma_plugins.components.user_rating.dtos.user_rating import (
    BaseUserRatingDTO,
    ArticleRatingDTO,
    ModuleRatingDTO,
    UserRatingDTO,
)
from huma_plugins.components.user_rating.repository.user_rating_repo import (
    UserRatingRepository,
)


class PostgresUserRatingRepository(UserRatingRepository):
    def create_module_rating(self, module_rating: ModuleRatingDTO) -> str:
        return self._create_user_rating(module_rating)

    def create_article_rating(self, article_rating: ArticleRatingDTO) -> str:
        if article_rating.articleType == ArticleRatingDTO.ArticleType.INTERNAL_CMS:
            existing_article = CMSContent.objects.filter(mongoId=article_rating.articleId).first()
            if not existing_article:
                raise ArticleNotFoundException(
                    f"Article for collection cmscontent and id {article_rating.articleId} not found"
                )

        return self._create_user_rating(article_rating)

    @staticmethod
    def _create_user_rating(user_rating: BaseUserRatingDTO) -> str:
        user_rating_data = user_rating.to_dict(
            ignored_fields=(
                BaseUserRatingDTO.CREATE_DATE_TIME,
                BaseUserRatingDTO.UPDATE_DATE_TIME,
            )
        )
        user_rating_data[BaseUserRatingDTO.CREATE_DATE_TIME] = dt.datetime.now(dt.UTC)
        user_rating_data[BaseUserRatingDTO.UPDATE_DATE_TIME] = dt.datetime.now(dt.UTC)
        new_rating = UserRating(**user_rating_data, mongoId=ObjectId())
        new_rating.save()
        return str(new_rating.mongoId)

    def search_article_rating(self, user_id: str, article_type: str, article_id: str) -> ArticleRatingDTO:
        user_rating_data = UserRating.objects.filter(
            userId=user_id, articleType=article_type, articleId=article_id
        ).first()
        if not user_rating_data:
            raise ObjectDoesNotExist("Article rating not found")
        return ArticleRatingDTO.from_dict(model_to_dict(user_rating_data), use_validator_field=False)

    def update_user_rating(self, user_rating: BaseUserRatingDTO) -> str:
        user_rating_dict = remove_none_values(user_rating.to_dict(ignored_fields=(BaseUserRatingDTO.UPDATE_DATE_TIME,)))
        user_rating_dict[BaseUserRatingDTO.UPDATE_DATE_TIME] = dt.datetime.now(dt.UTC)
        user_rating_id = user_rating_dict.pop(BaseUserRatingDTO.ID)
        query = UserRating.objects.filter(mongoId=user_rating_id)
        if not query.first():
            raise ObjectDoesNotExist("User rating not found")
        query.update(**user_rating_dict)
        return str(user_rating_id)

    def retrieve_user_ratings(self, deployment_id: str) -> list[dict]:
        user_ratings = UserRating.objects.filter(deploymentId=deployment_id).all()
        return [
            UserRatingDTO.from_dict(model_to_dict(rating), use_validator_field=False).to_dict()
            for rating in user_ratings
        ]
