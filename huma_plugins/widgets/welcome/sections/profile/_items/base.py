from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.deployment.dtos.deployment import DeploymentDTO


class ProfileSectionItem:
    iter_order: int
    name: str

    def __init__(self, onboarding_items: list, authz_user: AuthorizedUser):
        self.onboarding_items = onboarding_items
        self.authz_user = authz_user
        self.user = authz_user.user

    def add_onboarding_item(self):
        """Child classes should overwrite this method."""
        raise NotImplementedError

    @property
    def _deployment(self) -> DeploymentDTO:
        return self.authz_user.deployment
