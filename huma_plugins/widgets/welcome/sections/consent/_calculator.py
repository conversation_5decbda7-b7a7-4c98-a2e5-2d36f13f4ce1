from functools import cached_property

from huma_plugins.widgets.welcome._response_object import (
    AppSection,
    OnboardingItem,
)
from huma_plugins.widgets.welcome._welcome import SectionType
from huma_plugins.widgets.welcome.sections.calculator import SectionDetailsCalculator
from sdk.authorization.boarding.module import BoardingModule
from sdk.authorization.boarding.move_deployment_module import MoveUserModule
from sdk.deployment.boarding.consent_module import ConsentModule
from sdk.deployment.boarding.econsent_module import EConsentModule
from sdk.deployment.boarding.econsents_module import EConsentsModule
from sdk.deployment.boarding.helper_agreement_module import HelperAgreementModule
from sdk.deployment.boarding.us_consent_module import USConsentModule
from sdk.deployment.dtos.deployment import DeploymentDTO


class ConsentCalculator(SectionDetailsCalculator):
    def _calculate_details(self) -> dict:
        return {
            AppSection.TITLE: self.section.title,
            AppSection.ORDER: self.section.order,
            AppSection.TYPE: SectionType.CONSENT,
            AppSection.STATUS: self.status,
            AppSection.ONBOARDING_ITEMS: self.onboarding_items,
        }

    @cached_property
    def onboarding_items(self):
        onboarding_items = []
        for idx, module in enumerate(self._consent_section_modules):
            completed = module.is_module_completed(self.authz_user)
            item_dict = {
                OnboardingItem.ONBOARDING_ID: module.name,
                OnboardingItem.ORDER: idx,
                OnboardingItem.COMPLETED: completed,
                OnboardingItem.CONFIG_BODY: (
                    module.onboarding_config.configBody if module.name == MoveUserModule.name else None
                ),
            }
            onboarding_items.append(item_dict)
        return onboarding_items

    def is_completed(self) -> bool:
        return all((i[OnboardingItem.COMPLETED] for i in self.onboarding_items))

    @property
    def _deployment(self) -> DeploymentDTO:
        return self.authz_user.deployment

    @cached_property
    def _consent_section_modules(self) -> list[BoardingModule]:
        consent_section_modules = (
            ConsentModule,
            USConsentModule,
            EConsentModule,
            EConsentsModule,
            HelperAgreementModule,
            MoveUserModule,
        )
        modules = []
        for module_cls in consent_section_modules:
            module = module_cls(
                self._deployment.find_onboarding_config(module_cls.name),
                self.authz_user,
            )
            if module.is_enabled(self.authz_user):
                modules.append(module)

        return sorted(modules, key=lambda x: x.order())
