import json
from datetime import timed<PERSON><PERSON>
from functools import cached_property

from redis import Redis

from sdk.authorization.boarding.manager import BoardingManager
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.builder.models import GetWidgetDataRequestObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.common_functions_utils import find
from sdk.common.utils.flask_request_utils import (
    get_current_path_or_empty,
    get_current_url_rule_or_empty,
)
from sdk.common.utils.inject import autoparams
from sdk.deployment.dtos.deployment import DeploymentDTO
from ._response_object import AppSection, SectionStatus, WelcomeWidgetResponseObject
from ._welcome import SectionType, WelcomeWidgetSection
from .utils import redis_log_key


class WelcomeWidgetUseCase(UseCase):
    request_object: GetWidgetDataRequestObject
    response_schema = WelcomeWidgetResponseObject.Schema

    @autoparams()
    def __init__(self, redis: Redis):
        self._redis = redis

    def process_request(self, _) -> WelcomeWidgetResponseObject:
        widget_response = self.build_response()
        self._cache_completed_sections()

        if widget_response.completed:
            self._boarding_manager.finish_onboarding()

        return widget_response

    def build_response(self) -> WelcomeWidgetResponseObject:
        section_dicts = []
        cache_json: bytes | None = self._redis.get(self.redis_log_key)
        cache = json.loads(cache_json) if cache_json else []

        for section in self.sections:
            section_dicts.append(section.user_section_details(self.request_object.widget.config, cache))
        self._strikethrough_signup_if_nothing_completed(cache, section_dicts)

        all_completed = all((s.is_completed for s in self.sections))
        response_dict = {
            WelcomeWidgetResponseObject.COMPLETED: all_completed,
            WelcomeWidgetResponseObject.SECTIONS: section_dicts,
        }
        return WelcomeWidgetResponseObject.from_dict(response_dict, ignore_none=True)

    @property
    def sections(self) -> [WelcomeWidgetSection]:
        return self.request_object.widget.config.sections  # noqa

    @property
    def deployment(self) -> DeploymentDTO:
        return self.request_object.authzUser.deployment

    @property
    def authz_user(self) -> AuthorizedUser:
        return self.request_object.authzUser

    @cached_property
    def _boarding_manager(self):
        path = get_current_path_or_empty()
        url_rule = get_current_url_rule_or_empty()
        return BoardingManager(self.authz_user, path, url_rule)

    @property
    def redis_log_key(self) -> str:
        return redis_log_key(self.authz_user.id, self.deployment.id)

    def _strikethrough_signup_if_nothing_completed(self, cache, section_dicts):
        """Business logic requires Signup section to be strikethrough in case if no
        other sections are completed yet."""
        if cache:
            return
        completed_section_types = [s.type for s in self.sections if s.is_completed]
        if completed_section_types == [SectionType.SIGNUP]:
            signup_section_dict = find(lambda s: s[AppSection.TYPE] == SectionType.SIGNUP, section_dicts)
            signup_section_dict[AppSection.STATUS] = SectionStatus.COMPLETED_STRIKETHROUGH

    def _cache_completed_sections(self):
        """Don't cache Signup when it's the only completed."""
        completed_sections = [s.identifier for s in self.sections if s.is_completed]
        signup_section = find(lambda s: s.type == SectionType.SIGNUP, self.sections)
        if completed_sections == [signup_section.identifier]:
            return
        self._redis.setex(self.redis_log_key, timedelta(hours=72), json.dumps(completed_sections))
