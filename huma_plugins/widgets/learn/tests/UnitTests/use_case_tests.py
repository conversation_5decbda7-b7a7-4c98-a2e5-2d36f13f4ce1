import unittest
from datetime import datetime
from functools import cached_property
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

from bson import ObjectId

from huma_plugins.components.cms.dtos.cms_learn_article import (
    CMSLearnArticle,
    CMSLearnArticleContent,
    CMSArticleContentType,
)
from huma_plugins.widgets.learn._featured_articles import (
    FeaturedArticlesUIWidget,
    _FeaturedArticlesWidgetConfig,
)
from huma_plugins.widgets.learn._use_case import FeaturedArticlesUseCase
from huma_plugins.widgets.learn.queries import library
from sdk.authorization.dtos.user import UserDTO
from sdk.builder.models import GetWidgetDataRequestObject
from sdk.common.utils import inject
from sdk.deployment.dtos.learn import LearnArticleType
from sdk.organization.repository.organization_repository import OrganizationRepository

IMAGE_FILE_ID = "5f7e6b3b7b7d6d0001a0f22a"
GET_URL_USE_CASE = "huma_plugins.components.cms.adapters.cms_converter.GetSignedUrlUseCaseV1"


class FeaturedArticlesUseCasePatched(FeaturedArticlesUseCase):
    @cached_property
    def _user(self) -> UserDTO:
        return _user_sample()

    def _retrieve_articles(self):
        return [
            CMSLearnArticle(
                id="5f7e6b3b7b7d6d0001a0f1a4",
                order=0,
                title="Test Article",
                content=CMSLearnArticleContent(
                    body="Best day of my life",
                    timeToRead=10,
                    contentType=CMSArticleContentType.MARKDOWN,
                    isValid=True,
                ),
                type=LearnArticleType.MEDIUM,
                tags=["tag1"],
                updateDateTime=datetime(2024, 1, 1),
                createDateTime=datetime(2024, 1, 1),
            ),
            CMSLearnArticle(
                id="5f7e6b3b7b7d6d0001a0f1a5",
                order=1,
                title="Test Article 2",
                content=CMSLearnArticleContent(
                    body="Normal day of my life",
                    timeToRead=20,
                    contentType=CMSArticleContentType.MARKDOWN,
                    isValid=True,
                ),
                type=LearnArticleType.MEDIUM,
                tags=["tag1", "tag2"],
                updateDateTime=datetime(2024, 1, 2),
                createDateTime=datetime(2024, 1, 2),
            ),
            CMSLearnArticle(
                id="5f7e6b3b7b7d6d0001a0f1a6",
                order=2,
                title="Test Article 3",
                content=CMSLearnArticleContent(
                    body="Worst day of my life",
                    timeToRead=30,
                    contentType=CMSArticleContentType.MARKDOWN,
                    isValid=True,
                ),
                type=LearnArticleType.MEDIUM,
                tags=[],
                updateDateTime=datetime(2024, 1, 3),
                createDateTime=datetime(2024, 1, 3),
            ),
            CMSLearnArticle(
                id="5f7e6b3b7b7d6d0001a0f1a7",
                order=3,
                title="Draft Article 4",
                content=CMSLearnArticleContent(
                    body="Draft day of my life",
                    timeToRead=5,
                    contentType=CMSArticleContentType.MARKDOWN,
                    isValid=True,
                ),
                type=LearnArticleType.MEDIUM,
                tags=[],
                updateDateTime=datetime(2024, 1, 3),
                createDateTime=datetime(2024, 1, 3),
            ),
        ]


class TestFeaturedArticlesUseCase(unittest.TestCase):
    def setUp(self):
        self.use_case = FeaturedArticlesUseCasePatched()
        self.org_repo = MagicMock(spec=OrganizationRepository)
        self.mock_request = Mock(spec=GetWidgetDataRequestObject)
        self.mock_request.authzUser.deployment.get_localization.return_value = {}
        self.mock_request.authzUser.deployment.learn.articles = []
        self.mock_request.widget = _widget_sample()

        self.get_signed_url = patch(GET_URL_USE_CASE).start()
        self.get_signed_url().execute.return_value = Mock(url="https://example.com/thumbnail.jpg")

        def bind(binder):
            binder.bind(OrganizationRepository, self.org_repo)

        inject.clear_and_configure(bind)

    def tearDown(self):
        inject.clear()
        patch.stopall()
        return super().tearDown()

    def test_process_request__query_all(self):
        self.mock_request.widget.config.query = "limit(all(articles), 5)"
        result = self.use_case.execute(self.mock_request)
        self.assertEqual(4, len(result))
        self.assertEqual("5f7e6b3b7b7d6d0001a0f1a4", result[0].id)
        self.assertEqual("5f7e6b3b7b7d6d0001a0f1a5", result[1].id)
        self.assertEqual("5f7e6b3b7b7d6d0001a0f1a6", result[2].id)

    def test_process_request__query_unread(self):
        self.mock_request.widget.config.query = "limit(unread(articles), 2)"
        result = self.use_case.execute(self.mock_request)
        self.assertEqual(2, len(result))

    def test_process_request__query_random(self):
        self.mock_request.widget.config.query = "random(articles, 1)"
        result = self.use_case.execute(self.mock_request)
        self.assertEqual(1, len(result))

    def test_process_request__custom_func(self):
        def custom_filter(articles: list[CMSLearnArticle]) -> list[CMSLearnArticle]:
            return [a for a in articles if len(a.title) > 12]

        library.register(custom_filter)

        self.mock_request.widget.config.query = "custom_filter(articles)"
        result = self.use_case.execute(self.mock_request)
        self.assertEqual(3, len(result))

    def test_process_request__variables(self):
        query = "limit(all(articles), 1 if data.HeartRate.value >= 80 else 2)"
        self.mock_request.widget.config.query = query
        result = self.use_case.execute(self.mock_request)
        self.assertEqual(2, len(result))

    def test_process_request__duplicates(self):
        query = "articles + articles"  # 3 + 3
        self.mock_request.widget.config.query = query
        result = self.use_case.execute(self.mock_request)
        self.assertEqual(4, len(result))
        self.assertNotEqual(result[0].id, result[1].id)
        self.assertNotEqual(result[0].id, result[2].id)
        self.assertNotEqual(result[1].id, result[2].id)

    def test_process_request__global_limit(self):
        """Make sure max of 15 articles are shown"""

        # Create 20 articles
        articles = [
            CMSLearnArticle(
                id=str(ObjectId()),
                order=0,
                title=f"Test Article {i}",
                content=CMSLearnArticleContent(
                    body="Best day of my life",
                    timeToRead=10,
                    contentType=CMSArticleContentType.MARKDOWN,
                    isValid=True,
                ),
                type=LearnArticleType.MEDIUM,
                tags=[],
                updateDateTime=datetime(2024, 1, i + 1),
                createDateTime=datetime(2024, 1, i + 1),
            )
            for i in range(20)
        ]

        query = "articles"
        self.mock_request.widget.config.query = query
        with patch.object(FeaturedArticlesUseCasePatched, "_retrieve_articles", lambda s: articles):
            result = self.use_case.execute(self.mock_request)
            self.assertEqual(15, len(result))

    def test_process_request__tag(self):
        query = "filter(articles, tags='tag1')"
        self.mock_request.widget.config.query = query
        result = self.use_case.execute(self.mock_request)
        self.assertEqual(2, len(result))

    def test_process_request__multiple_tags(self):
        query = "filter(articles, tags='tag1,tag2')"
        self.mock_request.widget.config.query = query
        result = self.use_case.execute(self.mock_request)
        self.assertEqual(2, len(result))

    def test_process_request__no_tags_match(self):
        query = "filter(articles, tags='tag3')"
        self.mock_request.widget.config.query = query
        result = self.use_case.execute(self.mock_request)
        self.assertEqual(0, len(result))


def _widget_sample():
    return FeaturedArticlesUIWidget(
        id="5f7e6b3b7b7d6d0001a0f1a7",
        type="com.huma.widget.featuredArticles",
        config=_FeaturedArticlesWidgetConfig(
            title="Featured Articles",
            query="all(articles)",
            size=LearnArticleType.LARGE,
            sort=_FeaturedArticlesWidgetConfig.Sort.OLDEST,
        ),
    )


def _user_sample():
    return UserDTO(
        dateOfBirth=datetime(1990, 1, 1),
        gender=UserDTO.Gender.MALE,
        biologicalSex=UserDTO.BiologicalSex.MALE,
        recentModuleResults={"5f7e6b3b7b7d6d0001a0f1a8": [{"HeartRate": {"value": 70}}]},
    )
