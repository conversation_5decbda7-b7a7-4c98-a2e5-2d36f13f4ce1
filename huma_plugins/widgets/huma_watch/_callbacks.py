from sdk.builder.events import CreateWidgetEvent
from sdk.deployment.service.deployment_service import DeploymentService
from sdk.deployment.use_case.device_use_cases import HUMA_WATCH

APPS_KEY = "features.customAppConfig.thirdPartyApps.apps"


def enable_flags(event: CreateWidgetEvent):
    from ._app_widget import HumaWatchWidget

    if event.widget.type != HumaWatchWidget.id:
        return

    if event.widget.enabled:
        fields = {f"{APPS_KEY}.{HUMA_WATCH}": True}
        _update_deployment_fields(event.deployment.id, fields)


def _update_deployment_fields(deployment_id: str, update: dict):
    DeploymentService().update_deployment_fields(deployment_id, update)
