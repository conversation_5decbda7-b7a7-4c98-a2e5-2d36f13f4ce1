from abc import ABC

from sdk.authorization.dtos.user import UserDTO


class ModuleRecommendationEngine(ABC):
    response_object = None

    def __init__(self, user: UserDTO, config, language):
        self.user = user
        self.module_id = config.moduleId
        self.module_config_id = config.moduleConfigId
        self.language = language

    def get_recommendation(self) -> dict:
        change = self.assess_latest_submission()
        recommendation = self.get_recommendation_strings(change["direction"])
        result_dict = {
            **change,
            **recommendation,
        }
        return self.response_object.from_dict(result_dict)

    def assess_latest_submission(self) -> dict:
        raise NotImplementedError()

    def get_recommendation_strings(self, change) -> dict:
        raise NotImplementedError()
