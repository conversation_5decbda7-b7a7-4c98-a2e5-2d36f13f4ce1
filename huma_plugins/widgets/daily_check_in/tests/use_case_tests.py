from unittest import TestCase
from unittest.mock import patch

from huma_plugins.widgets.daily_check_in import DailyCheckInUIWidget
from huma_plugins.widgets.daily_check_in._use_case import DailyCheckInWidgetUseCase
from sdk.builder.models import GetWidgetDataRequestObject
from sdk.calendar.utils import get_dt_from_str
from sdk.module_result.dtos.module_config import ModuleConfig
from sdk.module_result.modules.questionnaire import QuestionnaireAnswer, QuestionnaireDTO


class DailyCheckInWidgetUseCaseTestCase(TestCase):
    def test_process_request__no_submit(self):
        with patch.object(DailyCheckInWidgetUseCase, "_last_primitive", None):
            use_case = DailyCheckInWidgetUseCase()
            response = use_case.execute(GetWidgetDataRequestObject())

            self.assertEqual("", response.unit)
            self.assertEqual(["--"], response.value)
            self.assertEqual([], response.data)
            self.assertEqual([], response.footer)

            self.assertIsNone(response.date)
            self.assertIsNone(response.description)
            self.assertIsNone(response.seen)

    @patch("huma_plugins.widgets.daily_check_in._use_case.ModuleResultService")
    def test_process_request(self, module_service):
        submit_date = get_dt_from_str("2025-01-01T00:00:00Z")
        last_primitive = QuestionnaireDTO(
            answers=[QuestionnaireAnswer(answerText="1")],
            startDateTime=get_dt_from_str("2025-01-01T00:00:00Z"),
        )
        config_dict = {
            "type": "com.huma.widget.vitals.daily_check_in",
            "order": 1,
            "config": {
                "moduleId": "DailyCheckIn",
                "moduleConfigId": "65735f9c98a7608a440acb5e",
                "card": {"showSeen": True},
            },
        }
        widget_config = DailyCheckInUIWidget.from_dict(config_dict).config
        with (
            patch.object(DailyCheckInWidgetUseCase, "_last_primitive", last_primitive),
            patch.object(DailyCheckInWidgetUseCase, "_widget_config", widget_config),
            patch.object(DailyCheckInWidgetUseCase, "_module_config", ModuleConfig.from_dict(_dci_config)),
        ):
            use_case = DailyCheckInWidgetUseCase()
            module_service().is_primitive_seen.return_value = True
            response = use_case.execute(GetWidgetDataRequestObject())

            self.assertEqual(["Yes"], response.value)
            self.assertTrue(response.seen)

            self.assertEqual("", response.unit)
            self.assertEqual([], response.data)
            self.assertEqual([], response.footer)

            self.assertEqual(submit_date, response.date)
            self.assertIsNone(response.description)

            module_service().is_primitive_seen.return_value = False
            response = use_case.execute(GetWidgetDataRequestObject())

            self.assertFalse(response.seen)


_dci_config = {
    "id": "67b4386aaf1621a6c12590ef",
    "about": "About Daily Check In",
    "order": 5,
    "status": "ENABLED",
    "version": 1,
    "moduleId": "DailyCheckIn",
    "schedule": {"timesOfDay": [], "timesPerDuration": 0},
    "configBody": {
        "id": "78c86a2f-693d-4fba-afa9-2edb5dfb5d48",
        "name": "Daily Check-in",
        "pages": [
            {
                "type": "QUESTION",
                "items": [
                    {
                        "id": "69fabf93-9c60-4111-8302-f93fb6aee0a7",
                        "text": "Are you feeling better than yesterday?",
                        "order": 1,
                        "format": "TEXTCHOICE",
                        "options": [
                            {"label": "No", "value": "0", "weight": 0},
                            {"label": "Yes", "value": "1", "weight": 1},
                            {"label": "Same", "value": "2", "weight": 2},
                        ],
                        "required": True,
                        "description": "",
                        "selectionCriteria": "SINGLE",
                    }
                ],
                "order": 1,
            }
        ],
        "isForManager": False,
        "publisherName": "RT",
        "submissionPage": {
            "id": "3ae656e6-35f7-42b2-bebd-f9d420c99211",
            "text": "Submit below to complete the Activity.",
            "type": "SUBMISSION",
            "order": 2,
            "buttonText": "Submit",
            "description": "Scroll up to change any of your answers. Proceed to submit when you are ready.",
        },
    },
    "createDateTime": "2025-02-18T07:36:10.910558Z",
    "updateDateTime": "2025-02-18T07:38:44.425215Z",
}
