from sdk.builder.widget import AppWidget
from sdk.deployment.component import DeploymentComponent
from sdk.module_result.component import ModuleResultComponent
from ._questionnaire import CMSQuestionnaireUIWidget
from huma_plugins.widgets.questionnaire._use_case import QuestionnaireWidgetUseCase


class CMSQuestionnaireWidget(AppWidget):
    id = "com.huma.widget.questionnaire.cms"
    dependencies = [DeploymentComponent, ModuleResultComponent]

    @property
    def config_class(self) -> type[CMSQuestionnaireUIWidget]:
        return CMSQuestionnaireUIWidget

    @property
    def use_case(self) -> QuestionnaireWidgetUseCase:
        return QuestionnaireWidgetUseCase()
