from sdk.builder.events import CreateWidgetEvent, DeleteWidgetEvent
from sdk.deployment.service.deployment_service import DeploymentService
from sdk.key_action.models.config import KeyActionConfig

TODO_WIDGET_ID = "com.huma.widget.todo"


def enable_flags(event: CreateWidgetEvent):
    if event.widget.type != TODO_WIDGET_ID:
        return

    update = {"keyActionsEnabled": True, "features.timeline": True}
    _update_deployment_fields(event.deployment.id, update)


def disable_flags(event: DeleteWidgetEvent):
    if event.widget.type != TODO_WIDGET_ID:
        return

    update = {"keyActionsEnabled": False, "features.timeline": False}
    _update_deployment_fields(event.deployment.id, update)


def _update_deployment_fields(deployment_id: str, update: dict):
    DeploymentService().update_deployment_fields(deployment_id, update)


def _delete_selected_key_actions(deployment_id: str, config_ids: set[str]):
    service = DeploymentService()
    for event_id in config_ids:
        service.delete_key_action(deployment_id, event_id)


def delete_key_actions(event: DeleteWidgetEvent):
    if event.widget.type != TODO_WIDGET_ID:
        return

    service = DeploymentService()
    service.delete_key_actions(event.deployment.id, exclude_types=(KeyActionConfig.Type.TODO.value,))
