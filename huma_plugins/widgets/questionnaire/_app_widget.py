from sdk.builder.widget import AppWidget
from sdk.deployment.component import DeploymentComponent
from sdk.module_result.component import ModuleResultComponent
from ._questionnaire import QuestionnaireUIWidget
from ._use_case import QuestionnaireWidgetUseCase


class QuestionnaireWidget(AppWidget):
    id = "com.huma.widget.questionnaire"
    dependencies = [DeploymentComponent, ModuleResultComponent]

    @property
    def config_class(self) -> type[QuestionnaireUIWidget]:
        return QuestionnaireUIWidget

    @property
    def use_case(self) -> QuestionnaireWidgetUseCase:
        return QuestionnaireWidgetUseCase()
