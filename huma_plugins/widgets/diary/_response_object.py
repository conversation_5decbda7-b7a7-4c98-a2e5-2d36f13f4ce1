from datetime import datetime
from typing import List

from marshmallow import post_dump
from flask import g

from huma_plugins.components.diary.utils import replace_localization_placeholders
from sdk import convertibleclass
from sdk.common.usecase.response_object import ResponseObject
from sdk.common.utils.convertible import required_field, default_field, meta
from sdk.common.utils.json_utils import replace_key_values
from sdk.common.utils.validators import default_datetime_meta
from sdk.module_result.dtos.diary_item import DiaryBackgroundColor


@convertibleclass
class DiaryOverview:
    TITLE = "title"
    VALUE = "value"
    SIDE_BAR_COLOR = "diarySidebar"
    VALUE_TEXT_COLOR = "valueTextColor"
    START_DATE_TIME = "startDateTime"
    LOCALIZATION_DATA = "localizationData"

    title: str = required_field()
    value: str = default_field()
    diarySidebar: DiaryBackgroundColor = default_field(metadata=meta(by_value=True))
    valueTextColor: DiaryBackgroundColor = default_field(metadata=meta(by_value=True))
    startDateTime: datetime = required_field(metadata=default_datetime_meta())
    localizationData: dict = default_field()

    @post_dump
    def apply_localization(self, data, **kwargs):
        data = replace_key_values(data, g.authz_user.localization, string_list_translator=True)
        localization_data = data.pop(DiaryOverview.LOCALIZATION_DATA, {})
        if not localization_data:
            return data
        return replace_localization_placeholders(data, localization_data)


@convertibleclass
class DiaryWidgetResponseObject(ResponseObject):
    ITEMS = "items"

    items: List[DiaryOverview] = required_field()
