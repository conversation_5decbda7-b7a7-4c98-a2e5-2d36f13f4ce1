import typing as t
from functools import reduce

from sdk import convertibleclass
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.events.get_badges_event import GetUserBadgesEvent
from sdk.common.adapter.event_bus_adapter import EventBusAdapter
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.utils import inject
from sdk.common.utils.convertible import required_field
from sdk.common.utils.inject import autoparams
from sdk.deployment.dtos.builder import UIWidget
from sdk.deployment.dtos.deployment import DeploymentDTO
from ._utils import get_profile_widget
from .exceptions import ProfileWidgetExistsException
from .manager import ProfileSectionsManager
from .settings import Setting, SettingType


@convertibleclass
class _ProfileWidgetConfig:
    NON_CUSTOMIZABLE_SECTIONS = [
        SettingType.MENU_TITLE,
        SettingType.MFA,
        SettingType.PROFILE_DETAILS,
        SettingType.PREFERRED_UNITS,
        SettingType.DEVICES,
        SettingType.PERSONAL_DOCUMENTS,
        SettingType.ABOUT,
        SettingType.E_LABEL,
        SettingType.E_IFU,
        SettingType.SIGN_OUT,
    ]

    BODY = "body"

    body: list[Setting] = required_field()

    def post_init(self):
        self.body = self.build_settings()

    def build_settings(self) -> list[Setting]:
        settings = []
        for setting_data in self.body:
            setting_dict = {
                Setting.TYPE: setting_data.type,
                Setting.DATA: setting_data.data,
            }
            setting = Setting.create_from_dict(setting_dict)
            settings.append(setting)
        return settings

    @classmethod
    def validate(cls, config: t.Self):
        setting_types = [s.type for s in config.body]
        duplicates = {s for s in setting_types if setting_types.count(s) > 1}

        if duplicates and duplicates != {SettingType.MENU_TITLE}:
            raise InvalidRequestException(f"Duplicate sections found: [{', '.join(duplicates)}]")

    def configure(self, authz_user: AuthorizedUser, deployment: DeploymentDTO):
        """
        Configure the widget to be presented to the user.
        Builds the profile layout based on the user settings and state.
        """
        sections_manager = inject.instance(ProfileSectionsManager)
        layout = sections_manager.build_layout(
            authz_user,
            deployment,
            self.get_badges(authz_user),
            extras=self.body,
        )
        self.body = layout.to_list()

    def post_configure(self, authz_user: AuthorizedUser, deployment: DeploymentDTO) -> None:
        """
        Run the layout through the processor chain to apply any external transformations to the layout.
        """
        sections_manager = inject.instance(ProfileSectionsManager)
        new_layout = sections_manager.run_layout_through_processors_chain(self.body, authz_user, deployment)
        self.body = new_layout.to_list()

    @staticmethod
    @autoparams("event_bus")
    def get_badges(authz_user: AuthorizedUser, event_bus: EventBusAdapter) -> dict:
        event = GetUserBadgesEvent(authz_user.id, authz_user.deployment_id())
        badges_results = event_bus.emit(event)
        if not badges_results:
            return {}
        return reduce(lambda a, b: {**a, **b}, badges_results)

    def validate_no_immutable_settings(self):
        invalid_settings = [setting for setting in self.body if setting.type in self.NON_CUSTOMIZABLE_SECTIONS]
        if invalid_settings:
            raise InvalidRequestException(
                f"Widget should not contain non-customizable profile settings: "
                f"{', '.join(setting.type for setting in invalid_settings)}"
            )


@convertibleclass
class ProfileUIWidget(UIWidget):
    CONFIG = "config"

    config: _ProfileWidgetConfig = required_field()

    def configure(self, authz_user: AuthorizedUser, deployment: DeploymentDTO, **kwargs):
        if not authz_user.is_user():
            return

        self.config.configure(authz_user=authz_user, deployment=deployment)

    def post_configure(self, authz_user: AuthorizedUser, deployment: DeploymentDTO):
        self.config.post_configure(authz_user, deployment)

    def pre_create_validation(self, deployment: DeploymentDTO) -> None:
        if bool(get_profile_widget(deployment)):
            raise ProfileWidgetExistsException
        self.config.validate_no_immutable_settings()

    def pre_update_validation(self, deployment: DeploymentDTO) -> None:
        self.config.validate_no_immutable_settings()

    def get_badges_count(self) -> int:
        return sum(getattr(setting.data, "badgesCount", 0) or 0 for setting in self.config.body)
