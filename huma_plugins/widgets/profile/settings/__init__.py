from ._about import AboutSetting
from ._app_lock import AppLockSetting
from ._app_support import AppSupportData, AppSupportSetting
from ._appointments import AppointmentsSetting
from ._base import Setting, SettingType
from ._data_export import DataExportSetting
from ._devices import DevicesSetting
from ._e_ifu import EIfuSetting
from ._e_label import ELabelSetting
from ._header import HeaderSetting, HeaderSettingData, ProfilePictureStatus
from ._medications import MedicationsSetting
from ._menu_title import MenuTitleSetting, MenuTitleSettingData
from ._mfa import MfaSetting
from ._personal_documents import PersonalDocumentsSetting
from ._preferred_units import PreferredUnitsSetting
from ._profile_details import ProfileDetailsSetting
from ._sign_out import SignOutSetting

available_settings = (
    AboutSetting,
    AppLockSetting,
    AppSupportSetting,
    AppointmentsSetting,
    DataExportSetting,
    DevicesSetting,
    ELabelSetting,
    EIfuSetting,
    HeaderSetting,
    MedicationsSetting,
    MenuTitleSetting,
    MfaSetting,
    PersonalDocumentsSetting,
    PreferredUnitsSetting,
    ProfileDetailsSetting,
    SignOutSetting,
)
