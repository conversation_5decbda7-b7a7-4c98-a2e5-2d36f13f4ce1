import importlib
from functools import cached_property

import inject

from simple_runner.adapters.storage import StorageAdapter
from simple_runner.apps.addon.exceptions import AddonNotRegisteredException, RunHandlerException
from simple_runner.apps.addon.models import Hand<PERSON>, LoadedAddon
from simple_runner.apps.addon.serializers.import_addon import ImportAddonRequestObject
from simple_runner.apps.addon.serializers.run_handler import RunH<PERSON>lerRequestObject, RunHandlerSuccessResponse
from simple_runner.apps.addon.use_cases import ImportPackageUseCase
from simple_runner.apps.addon.use_cases._base import BaseUseCase
from simple_runner.apps.addon.utils import ADDON_REGISTRY


class RunHandlerUseCase(BaseUseCase):
    request_object: RunHandlerRequestObject

    @inject.autoparams("adapter")
    def __init__(self, adapter: StorageAdapter):
        self.adapter = adapter
        self.bucket_name = adapter.bucket
        self.bucket_path = None
        self.key = None

    def init_args(self):
        addon_id_path = self.request_object.addon_id.replace("/", "_").replace(":", "_").replace(".", "_")
        version_path = self.request_object.version.replace(".", "_")
        self.bucket_path = f"addons/{addon_id_path}/{version_path}"
        self.key = f"{addon_id_path}@{version_path}"

    def process_request(self) -> RunHandlerSuccessResponse | None:
        self.init_args()
        if not self.addon:
            self.find_and_import_addon_or_break()

        module_path = f"addons.{self.addon.module_path}.{self.handler.path}".replace("/", ".")
        module = importlib.import_module(module_path)

        if not (function_ := getattr(module, self.handler.entry)):
            raise RunHandlerException(f"Module {module_path} doesn't have {self.handler.entry} entry")

        # TODO: Add to the documentation that PRE_CREATE function must return schema {"data_record": dict}
        try:
            result = function_(self.request_object.submission_data)
            return RunHandlerSuccessResponse(data_record=result["data_record"])
        except Exception as e:
            raise RunHandlerException(f"Exception while executing handler: {e}")

    @property
    def addon(self) -> LoadedAddon | None:
        return ADDON_REGISTRY.get(self.key)

    @cached_property
    def handler(self) -> Handler:
        handler = next(filter(lambda h: h.name == self.request_object.handler, self.addon.manifest.handlers), None)
        if not handler:
            raise RunHandlerException(f"Handler {self.request_object.handler} doesn't exist in the manifest")
        return handler

    def find_and_import_addon_or_break(self):
        package_path = f"{self.bucket_path}/package.zip"
        manifest_path = f"{self.bucket_path}/manifest.yaml"
        if not self.adapter.file_exist(self.bucket_name, package_path) or not self.adapter.file_exist(
            self.bucket_name, manifest_path
        ):
            raise AddonNotRegisteredException(f"Addon not registered: {self.key}")
        package, _, _ = self.adapter.download_file(self.bucket_name, package_path)
        manifest, _, _ = self.adapter.download_file(self.bucket_name, manifest_path)
        req_obj = ImportAddonRequestObject(package=package, manifest=manifest, initial_import=False)
        ImportPackageUseCase().execute(req_obj)
