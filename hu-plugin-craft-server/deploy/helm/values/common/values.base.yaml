replicaCount:
image:
  repository: ghcr.io/huma-engineering/hu-plugin-craft-server
  tag: "wrong-image"

entrypoint: "/server/manage.py"
command: "run-server"
configPath: "/server/config.yaml"

existingSecret:
  enabled: true
  name: "hu-plugin-craft-server-secrets"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: 999m
    nginx.ingress.kubernetes.io/client-body-buffer-size: 999m
    nginx.ingress.kubernetes.io/enable-cors: "true"
    # yamllint disable rule:line-length
    nginx.ingress.kubernetes.io/cors-allow-headers: "x-hu-locale,x-org-id,x-deployment-id,x-hu-user-agent,DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Request-ID,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,x-amz-user-agent"
    # yamllint enable
    nginx.ingress.kubernetes.io/proxy-send-timeout: "1200"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "1200"
    nginx.ingress.kubernetes.io/cors-expose-headers: "*"

env:
  JSON_LOGGING_ENABLED: "1"
  MP_FROM_EMAIL: <EMAIL>
  MP_APNS_AUTH_KEY_FILE_PATH: /server/apns-key.p8
  MP_FCM_SA_FILE_PATH: /server/firebase-key.json
  MP_PAM_INTEGRATION_CLIENT_EXT_ID: "443890432"
  MP_PAM_INTEGRATION_SUB_GROUP_EXT_ID: "568471853"
  MP_EMAIL_ADAPTER: "mailgun"
  MP_SMTP_HOST: smtp.gmail.com
  MP_SMTP_EMAIL: "<EMAIL>"
  MP_SMTP_PASSWORD: "test"
  SUPPORT_EMAIL: <EMAIL>
