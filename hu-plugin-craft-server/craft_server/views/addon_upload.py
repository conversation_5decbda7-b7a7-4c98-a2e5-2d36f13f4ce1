import yaml
from django.db import transaction
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response
from rest_framework.views import APIView
from yaml import safe_load

from craft_server.authentications import RunnerAuthentication
from craft_server.permissions import HasRunnerUserPermission
from craft_server.serializers import AddonManifestSerializer


class AddonUploadView(APIView):
    authentication_classes = [RunnerAuthentication]
    permission_classes = [HasRunnerUserPermission]
    parser_classes = [MultiPartParser]

    def post(self, request, *args, **kwargs):
        if not (manifest_file := request.FILES.get("manifest")):
            return Response({"error": "No manifest file provided"}, status=400)
        try:
            manifest_data = safe_load(manifest_file.read())
        except yaml.YAMLError as e:
            return Response({"error": f"Invalid YAML file: {str(e)}"}, status=400)
        if not isinstance(manifest_data, dict):
            return Response({"error": "Invalid manifest file"}, status=400)

        serializer = AddonManifestSerializer(data=manifest_data)

        if serializer.is_valid():
            with transaction.atomic():
                serializer.save()
            return Response(serializer.data, status=201)
        return Response(serializer.errors, status=400)
