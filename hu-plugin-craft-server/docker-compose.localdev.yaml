services:

  server:
    container_name: server
    build:
      context: .
      dockerfile: docker/server/Dockerfile
    restart: on-failure
    command: python manage.py run-server --prod false
    depends_on:
      - pymongodb-primary
    ports:
      - "3911:3901"
    links:
      - pymongodb-primary
      - py-minio
      - py-redis
    env_file:
      - ./dev.env
    environment:
      MP_MONGODB_URL: **************************************************
      MP_SOURCE_MONGODB_URL: **************************************************
      MP_REDIS_URL: redis://default:redispassword@py-redis:6379
    healthcheck:
      test: "echo '1';"
      start_period: 2s
    volumes:
      - ./app:/server/app

  server-beat:
    container_name: server-beat
    build:
      context: .
      dockerfile: docker/server/Dockerfile
    command: python manage.py run-server --prod false --run worker
    restart: on-failure
    depends_on:
      - pymongodb-primary
    links:
      - pymongodb-primary
      - py-minio
      - py-redis
    env_file:
      - ./dev.env
    environment:
      MP_MONGODB_URL: **************************************************
      MP_SOURCE_MONGODB_URL: **************************************************
      MP_REDIS_URL: redis://default:redispassword@py-redis:6379
    healthcheck:
      test: "echo '1';"
      start_period: 2s
    volumes:
      - ./app:/server/app

  server-worker:
    container_name: server-worker
    build:
      context: .
      dockerfile: docker/server/Dockerfile
    command: python manage.py run-server --prod false --run worker
    restart: on-failure
    depends_on:
      - pymongodb-primary
    links:
      - pymongodb-primary
      - py-minio
      - py-redis
    env_file:
      - ./dev.env
    environment:
      MP_MONGODB_URL: **************************************************
      MP_SOURCE_MONGODB_URL: **************************************************
      MP_REDIS_URL: redis://default:redispassword@py-redis:6379
    healthcheck:
      test: "echo '1';"
      start_period: 2s
    volumes:
      - ./app:/server/app

  server-cdc:
    container_name: server-cdc
    build:
      context: .
      dockerfile: docker/server/Dockerfile
    command: python manage.py run-server --prod false --run cdc
    restart: on-failure
    depends_on:
      - pymongodb-primary
    links:
      - pymongodb-primary
      - py-minio
      - py-redis
    env_file:
      - ./dev.env
    environment:
      MP_MONGODB_URL: **************************************************
      MP_SOURCE_MONGODB_URL: **************************************************
      MP_REDIS_URL: redis://default:redispassword@py-redis:6379
    healthcheck:
      test: "echo '1';"
      start_period: 2s
    volumes:
      - ./app:/server/app

volumes:
  py_redisdata:
  py_miniodata:
  pymongodb_master_data:
    driver: local
