server:
  supportEmail: "<EMAIL>"
  videoCall:
    enable: true

  invitation:
    shortenedCodeLength: 16

  exportDeployment:
    enableAuth: true

  adapters:
    gcs:
      serviceAccountKeyFilePath: !ENV ${MP_GCS_SA_FILE_PATH}
    azureBlobStorage:
      connectionString: !ENV ${MP_AZURE_BLOB_STORAGE_CONN_STR}
    apnsPush:
      useSandbox: true
      bundleId: "com.huma.HumaApp.qa"
    twilioSms:
      default:
        accountSid: !ENV ${MP_TWILIO_ACCOUNT_SID}
        authToken: !ENV ${MP_TWILIO_AUTH_TOKEN}
        sourcePhoneNumber: "+************"
    twilioSmsVerification:
      default:
        templateKey: "SMSVerificationTemplate"
        templateAndroidKey: "SMSAndroidVerificationTemplate"
        serviceName: "Huma"
        useTwilioVerify: false
        twilioVerifyServiceSid: !ENV ${MP_TWILIO_VERIFY_SERVICE_SID}
    mailgunEmail:
      domainUrl: preprodemail.humatherapeutics.cn
      mailgunApiUrlTemplate: https://api.mailgun.net/v3/{0}/messages
    minio:
      secure: false
      baseUrl: !ENV ${MP_MINIO_BASE_URL}
    aliCloudSms:
      accessKeyId: !ENV ${MP_ALI_CLOUD_ACCESS_KEY_ID}
      accessKeySecret: !ENV ${MP_ALI_CLOUD_ACCESS_KEY_SECRET}
      params:
        region: cn-beijing
        domain: sms-intl.ap-southeast-1.aliyuncs.com
        fromId: 迈达普
        templateCode: SMS_10585524
    oss:
      accessKeyId: !ENV ${MP_ALI_CLOUD_ACCESS_KEY_ID}
      accessKeySecret: !ENV ${MP_ALI_CLOUD_ACCESS_KEY_SECRET}
      url: https://oss-eu-central-1.aliyuncs.com
    aliCloudPush:
      accessKeyId: !ENV ${MP_ALI_CLOUD_PUSH_ACCESS_KEY}
      accessKeySecret: !ENV ${MP_ALI_CLOUD_PUSH_ACCESS_KEY_SECRET}
      region: cn-beijing
      appKey: !ENV ${MP_ALI_CLOUD_APP_KEY}
