name: <PERSON>gger Restore MongoDB dump for PR preview database
on:
  workflow_dispatch:
    inputs:
      cluster_location:
        description: GKE region
        default: europe-west2
        required: true
      cluster_name:
        description: GKE cluster name
        default: hu-uk-sandbox-pr-preview-gke
        required: true
        type: choice
        options:
          - hu-uk-sandbox-compose-ci-cd-gke
          - hu-uk-sandbox-pr-preview-gke
      db_provider:
        description: MongoDB cluster provider
        default: kubedb
        required: true
        type: choice
        options:
          - atlas
          - kubedb
      gcs_source_bucket:
        description: the bucket name of a data source
        default: hu-europe2-github-action-pr-preview-bucket
        required: true
      is_full_db_dump:
        description: Full database snapshots
        default: false
        type: boolean
      issue_number:
        description: Pull request issue number
        required: true
      restore_options:
        description: Additional mongorestore options
        required: false
      snapshot_kind:
        description: Snapshot kind
        required: true
        default: rpm
        type: choice
        options:
          - dct
          - rpm
      snapshot_stage:
        description: Snapshot stage
        default: qa-auto
        required: true
        type: choice
        options:
          - dev
          - qa-auto
          - qa
      storage_provider:
        description: Object storage provider
        default: gcs
        required: true
        type: choice
        options:
          - azure
          - gcs
          - s3

jobs:
  restore-kubedb:
    runs-on: ubuntu-latest
    steps:
      - name: Restore to KubeDB MongoDB
        id: restore
        uses: huma-engineering/huma-github-actions/restore-mongodb-archived-db@v0.38.0
        with:
          archive_name: ${{ inputs.snapshot_stage == 'qa-auto' && format('{0}gcp_uk_qa_auto_{1}.gz',
            inputs.is_full_db_dump && 'full_' || '', inputs.snapshot_kind) ||
            format('{0}gcp_uk_{1}_{2}.gz', inputs.is_full_db_dump && 'full_' || '',
            inputs.snapshot_stage, inputs.snapshot_kind) }}
          cluster_name: ${{ github.event.inputs.cluster_name }}
          db_name: pr-preview-sdk-${{ github.event.inputs.issue_number }}-db
          db_provider: ${{ github.event.inputs.db_provider }}
          db_secret_name: pr-preview-sdk-${{ github.event.inputs.issue_number }}-db
          gcp_credentials: ${{ secrets.GKE_SA_KEY }}
          dump_source_bucket: ${{ github.event.inputs.gcs_source_bucket }}
          cluster_location: ${{ github.event.inputs.cluster_location }}
          old_bucket_name: ${{ format('hu-gcp-uk-{0}-{1}-bck', inputs.snapshot_stage, inputs.snapshot_kind) }}
          restore_options: ${{ github.event.inputs.restore_options }}
          secret_namespace: pr-preview-sdk-${{ github.event.inputs.issue_number }}
          source_db_name: ${{ inputs.snapshot_stage == 'qa-auto' &&
            format('gcp_uk_qa_auto_{0}', inputs.snapshot_kind) ||
            format('gcp_uk_{0}_{1}', inputs.snapshot_stage, inputs.snapshot_kind) }}
          storage_provider: ${{ github.event.inputs.storage_provider }}
          storage_secret_name: pr-preview-sdk-${{ github.event.inputs.issue_number }}-bucket-secret

      - name: print
        run: echo "error=${{ steps.restore.outputs.error }}"
