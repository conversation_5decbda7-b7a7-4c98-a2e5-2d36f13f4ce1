from dataclasses import field
from enum import Enum

from sdk import convertibleclass
from sdk.common.utils.convertible import default_field
from sdk.phoenix.config.server_config import BasePhoenixConfig


class PlatformType(Enum):
    InternalAI = "InternalAI"
    VertexAI = "VertexAI"


@convertibleclass
class PlatformConfig:
    apiKey: str = default_field()
    baseUrl: str = default_field()


@convertibleclass
class InternalAIConfig(BasePhoenixConfig):
    armFatigue: PlatformConfig = default_field()
    ptosis: PlatformConfig = default_field()
    voiceAssessments: PlatformConfig = default_field()


@convertibleclass
class AdaptersConfig:
    internalAI: InternalAIConfig = field(default_factory=InternalAIConfig)


@convertibleclass
class InferenceConfig(BasePhoenixConfig):
    platform: PlatformType = field(default=PlatformType.InternalAI)
    adapters: AdaptersConfig = field(default_factory=AdaptersConfig)
