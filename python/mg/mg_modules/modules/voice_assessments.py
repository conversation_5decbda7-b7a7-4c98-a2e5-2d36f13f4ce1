from pathlib import Path

import i18n

from mg.mg_modules.modules.calculators.voice_assessments_calculator import (
    VoiceAssessmentsCalculator,
)
from mg.mg_modules.modules.primitives import DysphoniaDTO
from mg.mg_modules.modules.primitives.primitive_dysarthria import (
    DysarthriaDTO,
)
from sdk.common.utils.apiflask.multiple_input import InputType
from sdk.common.utils.validators import read_json_file
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.modules.module import Module


class VoiceAssessmentsModule(Module):
    moduleId = "VoiceAssessments"
    calculator = VoiceAssessmentsCalculator
    primitives = [DysphoniaDTO, DysarthriaDTO]
    inputPrimitives = (DysphoniaDTO, DysarthriaDTO)
    inputType = InputType.ANY_OF
    processAsync = True

    def get_validation_schema(self):
        return read_json_file("./schemas/voice_assessments_schema.json", Path(__file__).parent)

    def calculate(self, primitive: PrimitiveDTO):
        if not isinstance(primitive, (DysphoniaDTO, DysarthriaDTO)):
            return

        self.calculator().calculate(primitive, self.config)

    def prepare_notification_data(self, primitives: list[PrimitiveDTO], language: str):
        notification_template = {"body": i18n.t("VoiceAssessments.body", locale=language)}

        dysphonia_primitive = next((p for p in (primitives or []) if isinstance(p, DysphoniaDTO)), None)
        dysarthria_primitive = next((p for p in (primitives or []) if isinstance(p, DysarthriaDTO)), None)
        if dysphonia_primitive and dysarthria_primitive:
            action = "VOICE_ASSESSMENTS_MODULE_RESULT_READY"
            notification_template["title"] = i18n.t("VoiceAssessments.success.title", locale=language)
            notification_data = {
                "submitterId": dysphonia_primitive.userId,
                "moduleId": dysphonia_primitive.moduleId,
                "moduleConfigId": dysphonia_primitive.moduleConfigId,
                "moduleResultId": dysphonia_primitive.id,
                "dysphoniaProbability": str(dysphonia_primitive.probability),
                "dysarthriaProbability": str(dysarthria_primitive.probability),
                "dysphoniaConfidence": dysphonia_primitive.confidence,
                "dysarthriaConfidence": dysarthria_primitive.confidence,
            }
        else:
            action = "VOICE_ASSESSMENTS_MODULE_RESULT_FAILED"
            notification_template["title"] = i18n.t("VoiceAssessments.failure.title", locale=language)
            notification_data = {"error": i18n.t("VoiceAssessments.failure.body", locale=language)}
        return action, notification_template, notification_data
