//
//  SurgeryChecklistDateView.swift
//  HumaSurgeryChecklistWidget
//
//  Created by <PERSON> on 19.11.2024.
//

import SwiftUI

struct SurgeryChecklistDateView: View, OnActionCompatible {
    enum Action {
        case edit
    }

    // MARK: - Public Properties
    
    let date: Date?
    let showsEdit: Bool

    var handlers: [Action: ActionHandler] = [:]

    // MARK: - Life Cycle

    var body: some View {
        HStack {
            calendarIcon
            VStack(spacing: 4) {
                titleText
                dateText
            }
            Spacer()
            editButton
                .transition(.opacity)
                .animation(.default, value: showsEdit)
                .visible(showsEdit)
        }
    }
}

// MARK: - Private Helpers

private extension SurgeryChecklistDateView {
    var calendarIcon: some View {
        Image("sc_calendar", bundle: Bundle(for: SurgeryChecklistBundle.self))
            .renderingMode(.template)
            .frame(square: 24)
            .foregroundColor(.grey)
            .padding(.trailing, 10)
    }

    var titleText: some View {
        Text("Surgery date")
            .font(.default)
            .foregroundColor(.grey)
            .aligned(to: .leading)
    }

    var dateText: some View {
        Text(date?.globalAppFormat ?? "---")
            .font(.xSmall)
            .foregroundColor(.grey)
            .aligned(to: .leading)
            .animation(.default, value: date)
    }

    var editButton: some View {
        Button("Edit") {
            handleAction(.edit)
        }
        .pillStyle()
    }
}

// MARK: - Preview

#Preview {
    struct SurgeryChecklistDateViewPreview: View  {
        @State var showsEdit = true
        @State var showsDate = true

        var body: some View {
            VStack {
                SurgeryChecklistDateView(
                    date: showsDate ? Date() : nil,
                    showsEdit: showsEdit
                )
                .on(.edit) { print("edit tap") }
                .padding()
                .background(.white)

                HStack {
                    Button(action: { withAnimation { showsDate.toggle() } }) {
                        Text(showsDate ? "HIDE DATE" : "SHOW DATE")
                            .bold()
                            .underline()
                            .foregroundStyle(.purple)
                    }
                    .bouncyStyle()

                    Spacer()

                    Button(action: { withAnimation { showsEdit.toggle() } }) {
                        Text(showsEdit ? "HIDE EDIT" : "SHOW EDIT")
                            .bold()
                            .underline()
                            .foregroundStyle(.purple)
                    }
                    .bouncyStyle()
                }
            }
        }
    }

    return ZStack {
        Color.lightGrey3
            .ignoresSafeArea()
        ScrollView {
            VStack {
                SurgeryChecklistDateView(date: Date(), showsEdit: false)
                    .padding()
                    .background(.white)
                    .padding()

                SurgeryChecklistDateView(date: nil, showsEdit: false)
                    .padding()
                    .background(.white)
                    .padding()

                SurgeryChecklistDateView(date: Date(), showsEdit: true)
                    .on(.edit) { print("edit tap") }
                    .padding()
                    .background(.white)
                    .padding()

                SurgeryChecklistDateViewPreview()
                    .padding()
            }
        }
    }
}
