//
//  CDSCardViewModel.swift
//  HumaRochePlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import Combine
import HumaFoundation

final class CDSCardViewModel: AnyCDSCardViewModel {

    @Published private(set) var config: CDSWidgetConfig
    @Published private(set) var content: ContentState<GetWidgetDataResponse> = .loading

    private var access: Bool = false
    private var isLoadingData = false
    private let widgetConfigInfo: WidgetConfig.Info
    private let repository: AnyCDSWidgetRepository
    private let onboardingManager: AnyLateOnboardingManager

    /// Event to open detail page on tap of `show all` footer button.
    var onStartQuestionnaire = TriggeredEvent<Void>()
    /// To notificat UIKit updates.
    let onChange = VoidEvent()

    init(
        widgetConfigInfo: WidgetConfig.Info,
        config: CDSWidgetConfig,
        repository: AnyCDSWidgetRepository,
        onboardingManager: AnyLateOnboardingManager,
        content: ContentState<GetWidgetDataResponse> = .loading
    ) {
        self.repository = repository
        self.widgetConfigInfo = widgetConfigInfo
        self.config = config
        self.content = content
        self.onboardingManager = onboardingManager
    }

    func loadData() async {
        guard !isLoadingData else { return }
        isLoadingData = true

        do {
            let newResponse = try await repository.getWidgetData(
                widgetType: widgetConfigInfo.type,
                widgetID: widgetConfigInfo.id
            )
            Task { @MainActor in
                isLoadingData = false
                content = .results(newResponse)
                onChange.trigger()
            }
        } catch {
            Task { @MainActor in
                isLoadingData = false
                content = .empty
                onChange.trigger()
            }
        }
    }

    func setPregnancyDate(date: Date) async {
        Task {
            try? await repository.setPregnancyDate(date: date)
            await loadData()
        }
    }

    func ensureOnboardingCompletion() -> Bool {
        onboardingManager.ensureOnboardingCompletion()
    }

}
