//
//  ConfirmationDialogViewModel.swift
//  HemoPlugin
//
//  Created by <PERSON> on 20/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

final class ConfirmationDialogViewModel: ObservableObject {
    @Published var isPresented: Bool = false
    var title: String
    var message: String?
    var confirmTitle: String
    var cancelTitle: String
    var onConfirm: TriggeredEvent<Void> = .init()
    var onCancel: TriggeredEvent<Void> = .init()

    init(
        title: String,
        message: String? = nil,
        confirmTitle: String = HumaStrings.commonActionConfirm,
        cancelTitle: String = HumaStrings.commonActionCancel
    ) {
        self.title = title
        self.message = message
        self.confirmTitle = confirmTitle
        self.cancelTitle = cancelTitle
    }

    func show() {
        self.isPresented = true
    }

    func dismiss() {
        self.isPresented = false
    }
}
