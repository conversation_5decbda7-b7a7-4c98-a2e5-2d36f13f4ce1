//
//  MockConfigurationRepository.swift
//  HumaModulesTests
//
//  Created by <PERSON> on 21.08.2023.
//  Copyright © 2023 Huma Therapeutics Ltd. All rights reserved.
//

import HumaFoundation

final class MockConfigurationRepository {
    enum Call {
        case getConfiguration
        case refreshConfiguration
        /// The case to avoid **lastCall** property be optional
        case nothing
    }

    private(set) var calls: [Call] = []
    var lastCall: Call { calls.last ?? .nothing }

    var currentConfiguration: AnyConfiguration? = MockConfiguration()

    // MARK: - Methods

    init() { }
}

// MARK: - AnyConfigurationRepository

extension MockConfigurationRepository: AnyConfigurationRepository {
    func getConfiguration(completion: @escaping RepositoryCompletion<AnyConfiguration>) {
        calls.append(.getConfiguration)
        if let configuration = currentConfiguration {
            return completion(.success(configuration))
        }
        completion(.failure(.noResults))
    }

    func refreshConfiguration(completion: @escaping RepositoryCompletion<AnyConfiguration>) {
        calls.append(.refreshConfiguration)
        getConfiguration(completion: completion)
    }

    func observeConfiguration(
        forceRefresh: Bool,
        changeListener: @escaping RepositoryCompletion<AnyConfiguration>
    ) -> Disposable {
        getConfiguration(completion: changeListener)
        return AnyDisposable()
    }
}

extension MockConfigurationRepository: AnyDeploymentConfigurationRepository {
    var configuration: DeploymentConfiguration? {
        nil
    }
    func observeConfiguration(changeListener: @escaping RepositoryCompletion<DeploymentConfiguration>) -> Disposable {
        AnyDisposable()
    }
    func getConfiguration(completion: @escaping RepositoryCompletion<DeploymentConfiguration>) {

    }
    func refreshConfiguration(completion: @escaping RepositoryCompletion<DeploymentConfiguration>) {

    }
    func getConsent(_ completion: @escaping RepositoryCompletion<ConsentForm>) {

    }
    func getEConsent(_ completion: @escaping RepositoryCompletion<EConsentForm>) {

    }
    func resetCache() {

    }

}
