//
//  CircularInitialView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct CircularInitialView: View {
    let initial: String
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(Color.lightGrey3, lineWidth: 1)
                .frame(width: 30, height: 30)

            Text(initial)
                .font(.default)
                .foregroundColor(Color.lightGrey3)
        }
    }
}

#Preview {
    CircularInitialView(initial: "R")
}
