//
// UnitPickerSheetView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 17/03/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

struct UnitPickerSheetView: View {
    @State private var selectedIndex: Int = 0

    let options: [OptionItem]
    let onDone: (Int) -> Void
    let onCancel: () -> Void
    var body: some View {
        VStack(spacing: 0) {
            // Toolbar
            HStack {
                But<PERSON>(HumaStrings.commonActionCancel) {
                    onCancel()
                }
                .foregroundColor(.charcoalGrey)

                Spacer()

                Text(HumaStrings.pluginHemophiliaSelectUnit)
                    .font(.headline)

                Spacer()

                Button(HumaStrings.commonActionDone) {
                    onDone(selectedIndex)
                }
                .foregroundColor(.charcoalGrey)
            }
            .padding()
            .background(Color(.systemGroupedBackground))

            // Option picker
            Picker("", selection: $selectedIndex) {
                ForEach(0..<options.count, id: \.self) { index in
                    Text(options[index].title)
                        .tag(index)
                }
            }
            .pickerStyle(WheelPickerStyle())
            .frame(height: 200)
            .padding()
        }
        .background(.white)
    }
}
