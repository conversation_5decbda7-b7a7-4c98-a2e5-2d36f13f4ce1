//
//  MedicationScannerView.swift
//  HemoPlugin
//
//  Created by <PERSON> on 21/02/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI
import AVFoundation
import UIKit

struct MedicationScannerView: View {
    var scannedCode: String?
    @Binding var isPresented: Bool
    @State private var isFlashlightOn: Bool = false
    @StateObject private var scannerViewModel = ScannerViewModel()
    @State private var scanTimeoutTask: DispatchWorkItem? = nil
    // Optional medication resolver closure
    var medicationResolver: ((String) -> CMSMedicationResponse.MedicationItem?)? = nil
    var didConfirmMedication: ((CMSMedicationResponse.MedicationItem) -> Void)? = nil

    var body: some View {
        ZStack {
            ScannerRepresentable(
                viewModel: scannerViewModel,
                onCodeScanned: { code in
                    // Cancel timeout if scanned
                    scanTimeoutTask?.cancel()
                    scanTimeoutTask = nil
                    if let resolver = medicationResolver, let med = resolver(code) {
                        isPresented = false
                        didConfirmMedication?(med)
                        HemoJournalWidget.showSnackBar(message: HumaStrings.pluginHemophiliaScanMedicationQrcodeSuccess)
                    } else {
                        HemoJournalWidget.showSnackBar(message: HumaStrings.pluginHemophiliaScanMedicationQrcodeFailed)
                    }
                }
            )
            .padding(.top, 30)
            .overlay {
                // Rectangle focus box overlay
                GeometryReader { geometry in
                    let width: CGFloat = min(geometry.size.width * 0.7, 280)
                    let height: CGFloat = width
                    ZStack {
                        // Dimmed background
                        Color.black.opacity(0.5)
                            .mask(
                                Rectangle()
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 16)
                                            .frame(width: width, height: height)
                                            .blendMode(.destinationOut)
                                    )
                            )
                            .compositingGroup()
                        // Rectangle border
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white, lineWidth: 3)
                            .frame(width: width, height: height)
                            .shadow(radius: 8)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
                .allowsHitTesting(false)
                // Overlay for scanner
                VStack {
                    // Header
                    HStack {
                        HeaderView(title: HumaStrings.onboardingFailsafeScanQrTitle, style: .compact) {
                            isPresented = false
                        }
                        .padding(.horizontal, Dimensions.horizontalPadding)
                    }
                    .background(.white)

                    // Flashlight toggle
                    flashButton

                    Spacer()

                    // Footer
                    footerView
                }
            }
        }
        .onAppear {
            // Start 120s timeout
            let task = DispatchWorkItem {
                HemoJournalWidget.showSnackBar(message: HumaStrings.pluginHemophiliaScanMedicationQrcodeFailed)
                isPresented = false
            }
            scanTimeoutTask = task
            DispatchQueue.main.asyncAfter(deadline: .now() + 120, execute: task)
        }
        .onDisappear {
            // Cancel timeout on close
            scanTimeoutTask?.cancel()
            scanTimeoutTask = nil
        }
    }
}

private extension MedicationScannerView {

    var flashButton: some View {
        Button(action: {
            isFlashlightOn.toggle()
            scannerViewModel.toggleFlashlight(isOn: isFlashlightOn)
        }) {
            Image(
                isFlashlightOn ? HumaAssets.icFlashlightOn.name : HumaAssets.icFlashlightOff.name,
                bundle: HumaFoundationBundle
                    .bundle)
            .padding(.trailing, Dimensions.horizontalPadding)
        }
        .aligned(to: .trailing)
    }

    var footerView: some View {
        HStack {
            Image(HumaAssets.icQrCode.name, bundle: HumaFoundationBundle.bundle)

            Text(HumaStrings.pluginHemophiliaPlaceTheBarcodeWithinTheWindowToScan)
                .font(.xSmall)
                .foregroundColor(.charcoalGrey)
                .padding(.vertical, 22)
        }
        .frame(maxWidth: .infinity)
        .background(.white)
    }
}

// UIViewControllerRepresentable for AVCaptureSession
private struct ScannerRepresentable: UIViewControllerRepresentable {
    var viewModel: ScannerViewModel
    var onCodeScanned: (String) -> Void

    func makeUIViewController(context: Context) -> ScannerViewController {
        let scannerVC = ScannerViewController()
        scannerVC.delegate = context.coordinator
        scannerVC.viewModel = viewModel
        return scannerVC
    }

    func updateUIViewController(_ uiViewController: ScannerViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self, onCodeScanned: onCodeScanned)
    }

    class Coordinator: NSObject, AVCaptureMetadataOutputObjectsDelegate {
        var parent: ScannerRepresentable
        // Track only the last scanned value to prevent duplicate processing
        private var lastScannedValue: String?
        var onCodeScanned: (String) -> Void

        init(_ parent: ScannerRepresentable, onCodeScanned: @escaping (String) -> Void) {
            self.parent = parent
            self.onCodeScanned = onCodeScanned
        }

        func metadataOutput(_ output: AVCaptureMetadataOutput, didOutput metadataObjects: [AVMetadataObject], from connection: AVCaptureConnection) {
            if let metadataObject = metadataObjects.first {
                guard let readableObject = metadataObject as? AVMetadataMachineReadableCodeObject,
                      let stringValue = readableObject.stringValue else { return }

                if lastScannedValue != stringValue {
                    lastScannedValue = stringValue

                    // Provide haptic feedback for the new code
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)

                    // Update the scanned code
                    DispatchQueue.main.async { [weak self] in
                        self?.onCodeScanned(stringValue)
                    }
                }
            }
        }
    }
}

// ViewModel for the scanner to handle flashlight
private class ScannerViewModel: ObservableObject {
    @Published var isFlashlightOn: Bool = false
    private var device: AVCaptureDevice?

    func setDevice(_ captureDevice: AVCaptureDevice?) {
        self.device = captureDevice
    }

    func toggleFlashlight(isOn: Bool) {
        guard let device = device, device.hasTorch else { return }

        do {
            try device.lockForConfiguration()
            device.torchMode = isOn ? .on : .off
            device.unlockForConfiguration()
            isFlashlightOn = isOn
        } catch {
            print("Error toggling flashlight: \(error.localizedDescription)")
        }
    }
}

// UIViewController for camera capture session
private class ScannerViewController: UIViewController {
    var captureSession: AVCaptureSession!
    var previewLayer: AVCaptureVideoPreviewLayer!
    var delegate: AVCaptureMetadataOutputObjectsDelegate?
    var viewModel: ScannerViewModel?

    override func viewDidLoad() {
        super.viewDidLoad()
        setupCaptureSession()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        if captureSession?.isRunning == false {
            DispatchQueue.global(qos: .userInitiated).async {
                self.captureSession.startRunning()
            }
        }
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        if captureSession?.isRunning == true {
            captureSession.stopRunning()
        }
    }

    private func setupCaptureSession() {
        captureSession = AVCaptureSession()

        guard let videoCaptureDevice = AVCaptureDevice.default(for: .video) else { return }
        viewModel?.setDevice(videoCaptureDevice)

        let videoInput: AVCaptureDeviceInput

        do {
            videoInput = try AVCaptureDeviceInput(device: videoCaptureDevice)
        } catch {
            return
        }

        if captureSession.canAddInput(videoInput) {
            captureSession.addInput(videoInput)
        } else {
            return
        }

        let metadataOutput = AVCaptureMetadataOutput()

        if captureSession.canAddOutput(metadataOutput) {
            captureSession.addOutput(metadataOutput)

            metadataOutput.setMetadataObjectsDelegate(delegate, queue: DispatchQueue.main)
            metadataOutput.metadataObjectTypes = [
                .ean8, .ean13, .pdf417, .qr, .code39, .code93, .code128, .upce
            ]
        } else {
            return
        }

        previewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        previewLayer.frame = view.layer.bounds
        previewLayer.videoGravity = .resizeAspectFill
        view.layer.addSublayer(previewLayer)

        // Start the session in a background thread
        DispatchQueue.global(qos: .userInitiated).async {
            self.captureSession.startRunning()
        }
    }
}

#Preview {
    MedicationScannerView(isPresented: .constant(true))
}
