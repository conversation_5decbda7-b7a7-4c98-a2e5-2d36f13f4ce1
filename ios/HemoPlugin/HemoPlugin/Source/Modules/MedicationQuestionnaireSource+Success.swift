//
//  MedicationQuestionnaireSource+Success.swift
//  HemoPlugin
//
//  Created by <PERSON> on 20/05/2025.
//  Copyright © 2025 Huma Therapeutics Ltd. All rights reserved.
//

import SwiftUI

extension MedicationQuestionnaireSource {
    func makeSuccessScreen(viewModel: QuestionnaireViewModel) -> AnyView? {
        AnyView(
            SuccessScreen(
                title: HumaStrings.submissionTitleThankYou,
                subtitle: HumaStrings.submissionDescriptionResultsSubmitted,
                imageName: HumaAssets.thumbsUpBig.name,
                onDone: {
                viewModel.showSuccessScreen = false
                viewModel.pendingDismiss = false
                viewModel.shouldDismiss = true
            })
        )
    }
}
