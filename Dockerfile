FROM python:3.11-bullseye
COPY --from=ghcr.io/astral-sh/uv:0.4.30 /uv /uvx /bin/

RUN apt-get -y update && \
    apt-get install -y ffmpeg make && \
    apt-get clean

WORKDIR /server

COPY ./libs/ /server/libs

COPY pyproject.toml uv.lock manage.py /server/

RUN sed -i 's|../huma-server-sdk|libs/huma-server-sdk|g' pyproject.toml && sed -i 's|../huma-server-sdk|libs/huma-server-sdk|g' uv.lock

ENV UV_PROJECT_ENVIRONMENT="/usr/local/"
RUN uv sync --no-install-project --dev --locked

COPY huma_plugins /server/huma_plugins

COPY Makefile .pytest.ini .coveragerc README.md /server/

CMD ["make", "plugins/tests/run"]
