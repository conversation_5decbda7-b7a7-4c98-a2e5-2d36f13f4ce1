### process_request_for_cpt_mapping_1(2) - CPT_99453_98975(CPT_99454_98976)
- calendar
- calendar (winter)
- classic
  - PERIOD       | get first billable period start date
  - PERIOD       | get current billing period start/end
  - SUBMISSION   | get total submission days count from current billing period start to end
  - CPT / RULE   | calculate billing status
    
***

### process_request_for_cpt_mapping_3 - CPT_99457_98980
- VIDEO_CALL     | has video calls
- TIME_TRACKING  | get time tracking in period
- ?              | find the earliest billable date (>=20 mins completed | call answered)
- CPT / RULE     | calculate billing status

***

### process_request_for_cpt_mapping_4 - CPT_99458_98981
- VIDEO_CALL     | has video calls
- TIME_TRACKING  | get time tracking in period
- ?              | find the earliest billable date (>=40 mins completed | call answered)
- ? x2           | find the earliest billable date (>=60 mins completed | call answered)
- CPT / RULE     | calculate billing status



