namespaces:
  - name: {{ .Values.namespace }}
    create: true
    labels:
      # this will fetch regcreds
      #dockerconfigjson: enabled
    externalSecrets:
      enabled: true
      secrets:
        - name: "regcred"
          spec:
            secretStoreRef:
              name: "gcp-secret-store"
              kind: ClusterSecretStore
            refreshInterval: "50s"
            dataFrom:
              - extract:
                  key: "common-dockerconfigjson-creds"
            target:
              name: "regcred"
              creationPolicy: 'Owner'
              deletionPolicy: 'Delete'
              template:
                type: kubernetes.io/dockerconfigjson

        - name: "prod-api-huma-com-tls-crt"
          spec:
            secretStoreRef:
              name: "gcp-secret-store"
              kind: ClusterSecretStore
            refreshInterval: "50s"
            dataFrom:
              - extract:
                  key: "prod-api-huma-com-tls-crt"
            target:
              name: "prod-api-huma-com-tls-crt"
              creationPolicy: 'Owner'
              deletionPolicy: 'Delete'
              template:
                type: kubernetes.io/tls

        - name: "secret-from"
          spec:
            secretStoreRef:
              name: "gcp-secret-store"
              kind: ClusterSecretStore
            refreshInterval: "50s"
            dataFrom:
              - extract:
                  key: "gcp-jp-prod-rpm-creds"
              - extract:
                  key: "prod-houndify-creds"
              - extract:
                  key: "eu-prod-twilio-creds"
              - extract:
                  key: "prod-firebase-creds"
              - extract:
                  key: "prod-apns-creds"
              - extract:
                  key: "prod-us-pam-creds"
              - extract:
                  key: "prod-metabase-creds"
              - extract:
                  key: "prod-eu-mailgun-creds"
              - extract:
                  key: "prod-aws-email-creds"
              - extract:
                  key: "prod-us-kardia-creds"
              - extract:
                  key: "prod-openai-creds"
              - extract:
                  key: "prod-cms-creds"
              - extract:
                  key: "prod-deployment-templates-creds"
              - extract:
                  key: "prod-unleash-creds"
            target:
              name: "rpm-secrets"
              creationPolicy: 'Owner'
              deletionPolicy: 'Delete'
