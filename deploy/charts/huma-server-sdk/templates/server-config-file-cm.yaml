apiVersion: v1
kind: ConfigMap
metadata:
  labels: {{include "ppserver.labels" $ | nindent 4}}
  name: server-config-file-cm
  namespace: {{$.Release.Namespace}}
data:
  config.yaml: |-
    {{ .Values.configString | nindent 4 -}}
        {{- if eq $.Values.storage.provider "s3" }}
        minio:
          secure: true
          accessKey: !ENV ${MP_MINIO_ACCESS_KEY}
          secretKey: !ENV ${MP_MINIO_SECRET_KEY}
          url: !ENV ${MP_MINIO_URL}
        {{- else if eq $.Values.storage.provider "gcs" }}
        gcs:
          serviceAccountKeyFilePath: !ENV ${MP_GCS_SA_FILE_PATH}
        {{- else if eq $.Values.storage.provider "azure" }}
        azureBlobStorage:
          connectionString: !ENV ${MP_AZURE_BLOB_STORAGE_CONN_STR}
        {{- end }}

      storage:
        {{- if eq $.Values.storage.provider "s3" }}
        storageAdapter: MINIO
        {{- else if eq $.Values.storage.provider "gcs" }}
        storageAdapter: GCS
        {{- else if eq $.Values.storage.provider "azure" }}
        storageAdapter: AZURE
        {{- end }}
        enable: true
        allowedBuckets:
          - !ENV ${MP_BUCKET_NAME}
        defaultBucket: !ENV ${MP_BUCKET_NAME}
        rateLimit:
          write: "60/minute"
          read: "60 per minute"
