{{- $name := include "ppserver.fullname" $ -}}
apiVersion: operator.huma.com/v1alpha1
kind: ApplicationComposition
metadata:
  labels: {{ include "ppserver.labels" $ | nindent 4 }}
  name: ppserver
  namespace: {{ $.Release.Namespace }}
spec:
  commonSpec:
    env:
      - name: MP_HOST_URL
        value: {{ $name }}.sbx.huma.com
      - name: MP_WEB_APP_URL
        value: {{ $name }}.sbx.huma.com
      - name: MP_DEEPLINK_MOBILE_BASE_URL
        value: https://{{ $name }}.sbx.huma.com
      - name: MP_MANAGER_WEB_BASE_URL
        value: https://{{ $name }}.sbx.huma.com
      - name: MP_USER_WEB_BASE_URL
        value: https://{{ $name }}.sbx.huma.com
      - name: MP_FITBIT_REDIRECT_URI
        value: https://{{ $name }}.sbx.huma.com
      {{- include "ppserver.dynamicDatabaseEnv" $ | nindent 6 }}
      {{- include "ppserver.dynamicRedisEnv" $ | nindent 6 }}
      {{- include "ppserver.dynamicStorageEnv" $ | indent 6 }}
      {{- include "ppserver.staticSnapshotEnv" $ | indent 6 }}
      {{- include "ppserver.dynamicSnapshotEnv" $ | indent 6 }}
      {{- include "ppserver.snapshotFilesEnv" $ | indent 6 }}
      {{- include "ppserver.previewOverwriteEnv" $ | indent 6 }}
    envFrom:
      - configMapRef:
          name: server-dev-env-cm
      {{- if not $.Values.server.snapshotEnv.enabled }}
      - secretRef:
          name: server-dev-env-secret
      {{- end }}
    secretFiles:
      {{- if eq $.Values.storage.provider "gcs" }}
      - mountPath: /etc/gcp
        secretName: {{ $name }}-bucket-secret
        items:
          - key: privateKeyData
            path: credentials.json
      {{- end }}
      {{- include "ppserver.snapshotFilesMounts" $ | indent 6 }}
    files:
      - path: /app/apps/ppserver/config
        configMap:
          name: server-config-file-cm
          items:
            - key: config.yaml
              path: config.yaml
    image: "{{ $.Values.server.image }}:{{ $.Values.server.tag }}"
  apps:
    {{- if $.Values.server.enabled }}
    - name: ppserver
      memory: 384Mi
      command:
        - python
      args:
        - {{ include "ppserver.entrypoint" $ }}
        - --config
        - /app/apps/ppserver/config/config.yaml
        - --prod
        - {{ $.Values.server.productionEnable | toString | quote }}
        - --run
        - server
      domain:
        name: {{ $name }}.sbx.huma.com
        tlsSecretName: pp-star-sbx-huma-com-secret
      http:
        paths:
          - path: /api/
            service: ppserver
          - path: /apidocs/
            service: ppserver
          - path: /flasgger_static/
            service: ppserver
          - path: /health/
            service: ppserver
          - path: /helper/
            service: ppserver
          - path: /.well-known/
            service: ppserver
          - path: /version
            pathType: Exact
            service: ppserver
          - path: /apispec_all.json
            pathType: Exact
            service: ppserver
          - path: /metrics
            pathType: Exact
            service: ppserver
          - path: /
            pathType: Prefix
            service: cp
          - path: {{ $.Values.mongoExpress.baseUrl }}
            pathType: Prefix
            service: mongo-express
        enablePermissiveCORS: true
        corsAllowHeaders: "x-hu-locale,x-org-id,x-deployment-id,x-huma-device-id,x-hu-user-agent,DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Request-ID,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,x-amz-user-agent"
      ports:
        - 80:80
        {{- if eq $.Values.mongodb.provider "kubedb" }}
        - 27017:27017
        {{- end }}
      livenessProbe:
        httpGet:
          path: /health/live
          port: 80
        initialDelaySeconds: 5
        periodSeconds: 10
      readinessProbe:
        httpGet:
          path: /health/ready
          port: 80
        initialDelaySeconds: 5
        periodSeconds: 10
      replicas: {{ $.Values.server.replicaCount }}
    {{- end }}
    {{- if $.Values.worker.enabled }}
    - name: ppserver-worker
      autoscale:
        min: 1
        max: {{ $.Values.worker.replicaCount }}
        cpu-utilization: 200
        memory-utilization: 200
      cpu: 450m
      memory: 500Mi
      command:
        - python
      args:
        - {{ include "ppserver.entrypoint" $ }}
        - --config
        - /app/apps/ppserver/config/config.yaml
        - --prod
        - {{ $.Values.server.productionEnable | toString | quote }}
        - --run
        - worker
      ports:
        - 80
    {{- end }}
    {{- if $.Values.workerNotification.enabled }}
    - name: ppserver-worker-notification
      command:
        - python
      args:
        - {{ include "ppserver.entrypoint" $ }}
        - --config
        - /app/apps/ppserver/config/config.yaml
        - --prod
        - {{ $.Values.server.productionEnable | toString | quote }}
        - --run
        - worker
        - --queue
        - notification
      memory: 256Mi
      ports:
        - 80
      replicas: {{ $.Values.workerNotification.replicaCount }}
    {{- end }}
    {{- if $.Values.beat.enabled }}
    - name: ppserver-beat
      command:
        - python
      args:
        - {{ include "ppserver.entrypoint" $ }}
        - --config
        - /app/apps/ppserver/config/config.yaml
        - --prod
        - {{ $.Values.server.productionEnable | toString | quote }}
        - --run
        - beat
      memory: 256Mi
      ports:
        - 80
      replicas: {{ $.Values.beat.replicaCount }}
    {{- end }}
    {{- if $.Values.cdc.enabled }}
    - name: ppserver-cdc
      command:
        - python
      args:
        - {{ include "ppserver.entrypoint" $ }}
        - --config
        - /app/apps/ppserver/config/config.yaml
        - --prod
        - {{ $.Values.server.productionEnable | toString | quote }}
        - --run
        - cdc
      memory: 384Mi
      ports:
        - 80
      replicas: {{ $.Values.cdc.replicaCount }}
    {{- end }}
