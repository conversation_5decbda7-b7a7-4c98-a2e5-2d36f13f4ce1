FROM python:3.11-bullseye AS base
COPY --from=ghcr.io/astral-sh/uv:0.4.30 /uv /uvx /bin/

WORKDIR /server

ARG GIT_COMMIT=""
ARG GIT_BRANCH="master"

LABEL branch=${GIT_BRANCH}
LABEL commit=${GIT_COMMIT}

RUN apt-get -y update && apt-get install -y ffmpeg git

ARG GITHUB_TOKEN
RUN git config --global url."https://_:${GITHUB_TOKEN}@github.com/".insteadOf https://github.com/

COPY pyproject.toml uv.lock ./config.yaml ./manage.py ./hsp-config.yaml ./dev.env ./conftest.py ./.pytest.ini ./.coveragerc ./Makefile /server/

RUN set -xe && export HSP_VERSION=$(grep 'huma-server-platform' pyproject.toml | sed -n 's/.*rev = "\([^"]*\)".*/\1/p') && \
    echo "Extracted version: $HSP_VERSION" && \
    pip install git+https://_:${GITHUB_TOKEN}@github.com/huma-engineering/huma-server-platform.git@$HSP_VERSION

ENV UV_PROJECT_ENVIRONMENT="/usr/local/"
RUN hsp sync && \
    uv sync --no-install-project --locked --dev && \
    python manage.py install-dependencies

COPY ./app /server/app
COPY ./hspproviders /server/hspproviders

ENV GIT_COMMIT=${GIT_COMMIT}
ENV GIT_BRANCH=${GIT_BRANCH}

RUN mkdir /tmp/metrics

CMD ["make", "SHELL=/bin/sh", "PYTHONPATH=/server", "server/tests/run-full-tests-in-docker-compose"]

FROM base AS hcp

COPY hcp-config.yaml /server/config.yaml

CMD ["make", "SHELL=/bin/sh", "PYTHONPATH=/server", "server/tests/run-full-tests-in-docker-compose"]
