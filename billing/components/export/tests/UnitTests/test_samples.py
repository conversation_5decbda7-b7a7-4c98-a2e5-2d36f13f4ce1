from datetime import datetime

from bson import ObjectId

from billing.components.core.dtos.billing_models import (
    BillingRemoteTimeTrackingDTO,
)
from sdk.authorization.dtos.user import UserDTO

USER_ID = "5fe0b3bb2896c6d525461087"
CLINICIAN_ID = "5fe0b3bb2896c6d525461089"


sample_clinician = [UserDTO(id=CLINICIAN_ID, givenName="Tom", familyName="<PERSON>")]
sample_user = [UserDTO(id=USER_ID, givenName="<PERSON>", familyName="<PERSON>")]


def get_sample_automated_log():
    return [
        BillingRemoteTimeTrackingDTO(
            id=ObjectId("654128fd83a863ae18af9d45"),
            clinicianId=CLINICIAN_ID,
            createDateTime=datetime(2023, 10, 5, 16, 19, 9, 106000),
            deploymentId=ObjectId("5d386cc6ff885918d96edb2c"),
            effectiveDuration=37.0,
            effectiveEndDateTime=datetime(2023, 10, 3, 14, 50, 59, 39000),
            effectiveStartDateTime=datetime(2023, 10, 3, 14, 50, 22, 39000),
            endDateTime=datetime(2023, 10, 3, 14, 50, 59, 39000),
            startDateTime=datetime(2023, 10, 3, 14, 50, 22, 39000),
            userId=USER_ID,
        )
    ]
