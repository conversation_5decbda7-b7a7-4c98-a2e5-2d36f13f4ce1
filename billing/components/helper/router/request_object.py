from dataclasses import field
from datetime import datetime

from sdk import convertibleclass
from sdk.common.usecase.request_object import RequestObject
from sdk.common.utils.convertible import default_field, required_field, meta
from sdk.common.utils.validators import default_datetime_meta, validate_object_id


@convertibleclass
class CallHelperRequestObject(RequestObject):
    userId: str = default_field()
    deploymentId: str = default_field()
    managerId: str = default_field()
    startDateTime: datetime = default_field(metadata=default_datetime_meta())
    endDateTime: datetime = default_field(metadata=default_datetime_meta())
    noteId: str = default_field()


@convertibleclass
class QAHelperBillingProfileHistoryLogRequestObject(RequestObject):
    USER_ID = "userId"
    DEPLOYMENT_ID = "deploymentId"

    userId: str = required_field(metadata=meta(validate_object_id))
    deploymentId: str = required_field(metadata=meta(validate_object_id))
    createDateTime: datetime = required_field(metadata=default_datetime_meta())
    data: dict = default_field()

    def post_init(self):
        self.data = self.data or {}
        main_keys = ["userId", "deploymentId", "_id"]
        for key in main_keys:
            if key in self.data:
                self.data.pop(key)


@convertibleclass
class SubmissionHelperRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"
    USER_ID = "userId"
    PRIMITIVE_ID = "primitiveId"
    PRIMITIVE_CLASS_NAME = "primitiveClassName"
    DEVICE_NAME = "deviceName"
    DEVICE_DETAILS = "deviceDetails"
    SOURCE = "source"
    START_DATE_TIME = "startDateTime"

    deploymentId: str = required_field()
    userId: str = required_field()
    primitiveId: str = required_field()
    primitiveClassName: str = required_field()
    deviceName: str = required_field()
    deviceDetails: str = required_field()
    source: str = required_field()
    startDateTime: datetime = field(default_factory=datetime.utcnow, metadata=default_datetime_meta())
    isCompleted: bool = default_field()
