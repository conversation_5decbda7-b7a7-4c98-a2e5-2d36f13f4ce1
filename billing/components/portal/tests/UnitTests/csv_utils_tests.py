import unittest
from unittest.mock import mock_open, patch

from billing.components.portal.use_case.utils.csv_utils import CSVDictReader


class CSVDictReaderTestCase(unittest.TestCase):
    def setUp(self):
        # Sample CSV data for testing
        self.csv_data = "Name,Age,City\nJohn,30,New York\nJane,25,Boston\nBob,40,Chicago"
        self.expected_header = ["Name", "Age", "City"]
        self.expected_rows = [["John", "30", "New York"], ["Jane", "25", "Boston"], ["Bob", "40", "Chicago"]]
        self.expected_dicts = [
            {"Name": "<PERSON>", "Age": "30", "City": "New York"},
            {"Name": "<PERSON>", "Age": "25", "City": "Boston"},
            {"Name": "Bob", "Age": "40", "City": "Chicago"},
        ]
        self.test_file_path = "/path/to/test.csv"

    @patch("os.path.exists")
    @patch("builtins.open", new_callable=mock_open)
    @patch("csv.reader")
    def test_init_and_read_csv(self, mock_csv_reader, mock_file, mock_exists):
        mock_exists.return_value = True
        mock_csv_reader.return_value = [self.expected_header, *self.expected_rows].__iter__()

        reader = CSVDictReader(self.test_file_path)

        mock_exists.assert_called_once_with(self.test_file_path)

        mock_file.assert_called_once_with(self.test_file_path, mode="r", newline="", encoding="utf-8")

        self.assertEqual(reader.header, self.expected_header)
        self.assertEqual(reader.rows, self.expected_rows)

    @patch("os.path.exists")
    def test_file_not_found(self, mock_exists):
        mock_exists.return_value = False

        with self.assertRaises(FileNotFoundError):
            CSVDictReader(self.test_file_path)

    @patch("os.path.exists")
    @patch("builtins.open", new_callable=mock_open)
    @patch("csv.reader")
    def test_iteration(self, mock_csv_reader, mock_file, mock_exists):
        mock_exists.return_value = True
        mock_csv_reader.return_value = [self.expected_header, *self.expected_rows].__iter__()

        reader = CSVDictReader(self.test_file_path)

        rows = list(reader)
        self.assertEqual(rows, self.expected_dicts)

    @patch("os.path.exists")
    @patch("builtins.open", new_callable=mock_open)
    @patch("csv.reader")
    def test_len(self, mock_csv_reader, mock_file, mock_exists):
        mock_exists.return_value = True
        mock_csv_reader.return_value = [self.expected_header, *self.expected_rows].__iter__()

        reader = CSVDictReader(self.test_file_path)

        self.assertEqual(len(reader), len(self.expected_rows))

    @patch("os.path.exists")
    @patch("builtins.open", new_callable=mock_open)
    @patch("csv.reader")
    def test_getitem(self, mock_csv_reader, mock_file, mock_exists):
        mock_exists.return_value = True
        mock_csv_reader.return_value = [self.expected_header, *self.expected_rows].__iter__()

        reader = CSVDictReader(self.test_file_path)

        for i, expected_dict in enumerate(self.expected_dicts):
            self.assertEqual(reader[i], expected_dict)

        with self.assertRaises(IndexError):
            reader[-1]  # Negative index

        with self.assertRaises(IndexError):
            reader[len(self.expected_rows)]  # Index too large

    @patch("os.path.exists")
    @patch("builtins.open", new_callable=mock_open)
    @patch("csv.reader")
    def test_get_header(self, mock_csv_reader, mock_file, mock_exists):
        mock_exists.return_value = True
        mock_csv_reader.return_value = [self.expected_header, *self.expected_rows].__iter__()

        reader = CSVDictReader(self.test_file_path)

        self.assertEqual(reader.get_header(), self.expected_header)

    @patch("os.path.exists")
    @patch("builtins.open", new_callable=mock_open)
    @patch("csv.reader")
    def test_add_dict_row(self, mock_csv_reader, mock_file, mock_exists):
        mock_exists.return_value = True
        mock_csv_reader.return_value = [self.expected_header, *self.expected_rows].__iter__()

        reader = CSVDictReader(self.test_file_path)
        new_row = {"Name": "Alice", "Age": "35", "City": "Seattle"}
        reader.add_dict_row(new_row)

        self.assertEqual(len(reader.rows), len(self.expected_rows) + 1)
        self.assertEqual(reader.rows[-1], ["Alice", "35", "Seattle"])
        self.assertEqual(reader[len(reader) - 1], new_row)

    @patch("os.path.exists")
    @patch("builtins.open", new_callable=mock_open)
    @patch("csv.reader")
    def test_add_dict_row_invalid_type(self, mock_csv_reader, mock_file, mock_exists):
        mock_exists.return_value = True
        mock_csv_reader.return_value = [self.expected_header, *self.expected_rows].__iter__()

        reader = CSVDictReader(self.test_file_path)
        with self.assertRaises(ValueError):
            reader.add_dict_row("not a dictionary")

    @patch("os.path.exists")
    @patch("builtins.open", new_callable=mock_open)
    @patch("csv.reader")
    def test_add_dict_row_invalid_keys(self, mock_csv_reader, mock_file, mock_exists):
        mock_exists.return_value = True
        mock_csv_reader.return_value = [self.expected_header, *self.expected_rows].__iter__()

        reader = CSVDictReader(self.test_file_path)
        with self.assertRaises(ValueError):
            reader.add_dict_row({"Name": "Alice", "Age": "35"})  # Missing "City" key

        with self.assertRaises(ValueError):
            reader.add_dict_row({"Name": "Alice", "Age": "35", "Country": "USA"})  # Wrong key

    @patch("os.path.exists")
    @patch("builtins.open", new_callable=mock_open)
    @patch("csv.reader")
    def test_add_list_row(self, mock_csv_reader, mock_file, mock_exists):
        mock_exists.return_value = True
        mock_csv_reader.return_value = [self.expected_header, *self.expected_rows].__iter__()

        reader = CSVDictReader(self.test_file_path)
        new_row = ["Alice", "35", "Seattle"]
        reader.add_list_row(new_row)

        self.assertEqual(len(reader.rows), len(self.expected_rows) + 1)
        self.assertEqual(reader.rows[-1], new_row)
        self.assertEqual(reader[len(reader) - 1], {"Name": "Alice", "Age": "35", "City": "Seattle"})

    @patch("os.path.exists")
    @patch("builtins.open", new_callable=mock_open)
    @patch("csv.reader")
    def test_add_list_row_invalid_type(self, mock_csv_reader, mock_file, mock_exists):
        mock_exists.return_value = True
        mock_csv_reader.return_value = [self.expected_header, *self.expected_rows].__iter__()

        reader = CSVDictReader(self.test_file_path)
        with self.assertRaises(ValueError):
            reader.add_list_row({"Name": "Alice"})  # Not a list

    @patch("os.path.exists")
    @patch("builtins.open", new_callable=mock_open)
    @patch("csv.reader")
    def test_add_list_row_invalid_length(self, mock_csv_reader, mock_file, mock_exists):
        mock_exists.return_value = True
        mock_csv_reader.return_value = [self.expected_header, *self.expected_rows].__iter__()

        reader = CSVDictReader(self.test_file_path)
        with self.assertRaises(ValueError):
            reader.add_list_row(["Alice", "35"])  # Too short

        with self.assertRaises(ValueError):
            reader.add_list_row(["Alice", "35", "Seattle", "USA"])  # Too long


if __name__ == "__main__":
    unittest.main()
