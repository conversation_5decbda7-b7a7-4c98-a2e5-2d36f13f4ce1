import logging
from datetime import datetime

import ujson as json
from celery.schedules import crontab
from dateutil.relativedelta import relativedelta

from billing.components.billing_integration.adapters.generic.ehr_billing_api import (
    EHRBillingApi,
)
from billing.components.billing_integration.adapters.utils import (
    convert_huma_billing_report_to_redox,
    get_billing_key,
)
from billing.components.export.use_case.billing_monthly_export import (
    BillingMonthlyExportableUseCase,
    BillingMonthlyEHRExportableUseCase,
)
from huma_plugins.components.export.dtos.export_models import ExportParameters
from huma_plugins.components.export.use_case.export_request_objects import ExportRequestObject, ExportUsersRequestObject
from huma_plugins.components.export.use_case.export_use_cases import (
    ExportDeploymentUseCase,
)
from huma_plugins.components.integration.adapters.generic.ehr_api import EHRApi
from huma_plugins.components.integration.adapters.utils import (
    FIRST_NAME,
    LAST_NAME,
    PATIENT,
    PATIENT_DEMOGRAPHICS,
    PATIENT_IDENTIFIERS,
    fix_case,
    lowercase,
)
from huma_plugins.components.integration.dtos.generic_ehr import GenericEHRDTO
from huma_plugins.components.integration.dtos.integration import IntegrationDTO
from huma_plugins.components.integration.dtos.patient_profile import PatientProfileDTO
from huma_plugins.components.integration.repository.integration_repository import (
    IntegrationRepository,
)
from huma_plugins.components.integration.router.integration_requests import (
    CreatePatientProfileRequestObject,
)
from huma_plugins.components.integration.tasks import DOB, IDENTIFIERS
from huma_plugins.components.integration.use_case.patient_profile_use_case import (
    CreatePatientProfileUseCase,
    FindPatientProfileByUserIdUseCase,
)
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.authorization.services.authorization import AuthorizationService
from sdk.celery.app import celery_app
from sdk.common.exceptions.exceptions import ObjectDoesNotExist
from sdk.common.monitoring import report_exception
from sdk.common.utils import inject
from sdk.organization.repository.organization_repository import (
    OrganizationRepository,
)

logger = logging.getLogger(__name__)

TRANSACTIONS = "Transactions"
META = "Meta"
ERRORS = "Errors"
ERROR_DETAIL = "errorDetail"
LOCATION = "Location"
LOCATION_ABR = "locationAbr"
PLACEOFSERVICE_ABR = "placeOfServiceAbr"
LOCATION_DEPARTMENT = "locationDepartment"

MRN = "MRN"


@celery_app.on_after_finalize.connect
def setup_periodic_billing_tasks(sender, **kwargs):
    # Call prepare_reminders task every month
    sender.add_periodic_task(
        crontab(hour=10, minute=0, day_of_month="3"),
        send_monthly_billing_report.s(),
        name="Event executor for monthly billing report",
    )


@celery_app.task
def send_monthly_billing_report(include_users: list = None, dry_run: bool = False):
    processed_users = []

    integration_repo: IntegrationRepository = inject.instance(IntegrationRepository)

    matched_integrations = integration_repo.retrieve_integrations_by_params(
        enabled=True, integration_type=IntegrationDTO.Type.GENERICEHR.value
    )

    for integration in matched_integrations:
        # check if we have a destination for the financials to send before we continue
        destination = None
        # find the right destination
        for dt in integration.generic_ehr.destination:
            if dt.destination_func == GenericEHRDTO.Destination.DestinationFunc.send_financial_transactions:
                destination = dt
                break

        if not destination:
            # it means there is no endpoint for this function
            logger.error("send_financial_transactions destination not registered")
            return

        deployment_ids = []

        org_repo = inject.instance(OrganizationRepository)

        for organization_id in integration.organizationIds:
            organization = org_repo.retrieve_organization(organization_id=organization_id)
            deployment_ids.extend(organization.deploymentIds)

        # for now we have only 1 deployment in generic integration
        for deployment_id in integration.deploymentIds:
            if deployment_id not in deployment_ids:
                deployment_ids.append(deployment_id)

        for deployment_id in deployment_ids:
            try:
                billing_dict = export_monthly_billing(deployment_id)
                logger.info(
                    "Exported monthly billing for deployment: {} includes billing with size:{}".format(
                        deployment_id, len(billing_dict)
                    )
                )

                publish_billing_report(
                    billing_dict,
                    integration,
                    deployment_id,
                    include_users,
                    processed_users,
                    dry_run,
                )
            except Exception as e:
                logger.error(f"Failed to export monthly billing for deployment {deployment_id}: {e}")


def export_monthly_billing(deployment_id: str):
    # find the first date of month and last date of month
    now = datetime.now()
    start_date = datetime(year=now.year, month=now.month, day=1) - relativedelta(months=1)
    end_date = start_date + relativedelta(months=1, seconds=-1)

    # todo we are using superadmin role to do a monthly export (do we need to change that?)
    repo = inject.instance(AuthorizationRepository)
    system_user = repo.retrieve_user(check_super_admin=True)

    if not system_user:
        raise ObjectDoesNotExist("System user not found")

    data = {
        ExportParameters.DEPLOYMENT_ID: deployment_id,
        ExportParameters.FORMAT: ExportParameters.DataFormatOption.JSON.value,
        ExportParameters.FROM_DATE: start_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
        ExportParameters.MODULE_NAMES: [
            BillingMonthlyExportableUseCase.MODULE_NAME,
            BillingMonthlyEHRExportableUseCase.MODULE_NAME,
        ],
        ExportParameters.SINGLE_FILE_RESPONSE: True,
        ExportParameters.TO_DATE: end_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
        ExportRequestObject.REQUESTER_ID: system_user.id,
    }

    request_object = ExportUsersRequestObject.from_dict(data)
    use_case = ExportDeploymentUseCase()

    logger.info("Exporting monthly billing for deployment: {} with parameters {}".format(deployment_id, data))

    response_object = use_case.execute(request_object)
    with open(response_object.filePath, "rb") as f:
        return json.load(f)


@celery_app.task
def publish_billing_report(
    billing: dict,
    integration: IntegrationDTO,
    deployment_id: str,
    include_users: list = None,
    processed_users: list = None,
    dry_run: bool = False,
):
    """publish billing report to redox or any integration"""

    args = [
        integration.to_dict(include_none=False),
        billing,
        deployment_id,
        include_users,
        processed_users,
        dry_run,
    ]
    execute_integration_billing.delay(*args)


@celery_app.task
def execute_integration_billing(
    integration_dict: dict,
    billing: dict,
    deployment_id: str,
    include_users: list = [],
    processed_users: list = [],
    dry_run: bool = False,
):
    integration = IntegrationDTO.from_dict(integration_dict)

    financials_report = convert_huma_billing_report_to_redox(billing)

    billing_api = EHRBillingApi(integration)

    if not financials_report:
        logger.warning("There is no financial report for the deployment: {}".format(deployment_id))

    filtered_report = financials_report
    if include_users:
        filtered_report = [u for u in financials_report if u in include_users]

    for user in filtered_report:
        if user in processed_users:
            continue

        processed_users.append(user)

        try:
            key = get_billing_key(billing[user], user)

            if not key:
                continue

            # make sure user email always exists here
            user_email = billing[user][key][0]["user"]["email"]
            user_id = billing[user][key][0]["user"]["id"]

            patient_identifiers, patient_demographics = convert_to_redox_patient_json(user_email, user_id, integration)

            if not financials_report[user]:
                logger.warning(
                    "There is no financial report for the deployment: {} and user: {}".format(deployment_id, user)
                )

                continue

            if not patient_demographics or not patient_identifiers:
                logger.error(
                    "There is financial report for the deployment: {} and user: {} but there is no patient profile or it is empty for that user".format(
                        deployment_id, user
                    )
                )
                continue

            if len(financials_report[user]) > 0:
                location = financials_report[user][0]["location"]

                for transaction in financials_report[user]:
                    del transaction["location"]
            else:
                location = {
                    LOCATION_ABR: "",
                    PLACEOFSERVICE_ABR: "",
                    LOCATION_DEPARTMENT: "",
                }

            data = {
                TRANSACTIONS: financials_report[user],
                PATIENT: {
                    PATIENT_IDENTIFIERS: remove_non_digit_identifiers(patient_identifiers),
                    PATIENT_DEMOGRAPHICS: patient_demographics,
                },
                LOCATION: {
                    LOCATION_ABR: location[LOCATION_ABR],
                    PLACEOFSERVICE_ABR: location[PLACEOFSERVICE_ABR],
                    LOCATION_DEPARTMENT: location[LOCATION_DEPARTMENT],
                },
            }

            # check if the user in that deployment is active or not
            user_obj = AuthorizationService().retrieve_simple_user_profile(user_id=user_id)

            is_active = False

            for role in user_obj.roles:
                if role.resource_id() == deployment_id and role.isActive:
                    is_active = True

            if not is_active:
                logger.info(
                    f"skipping user:{user_id} in deployment {deployment_id} because it is not active anymore in that deployment"
                )

                continue

            if dry_run:
                logger.info(f"DRY RUN: Successfully created billing report for user:{user} with data: {data}")
                continue

            logger.info(f"Successfully created billing report for user:{user} with data: {data}")

            response = billing_api.send_financial_transactions(data)

            if response:
                if ERROR_DETAIL not in response and (
                    (isinstance(response, dict) and ERRORS not in response[META]) or (isinstance(response, list))
                ):
                    logger.debug(f"Successfully published billing report to redox for {integration.name}")
                    continue

                if ERROR_DETAIL in response:
                    message = f"Published data to redox for {integration.name} with error {response[ERROR_DETAIL]} "
                elif ERRORS in response[META]:
                    message = f"Published data to redox for {integration.name} with error {response[META][ERRORS]} "
                logger.error(message)
                exception = Exception(message)
                report_exception(
                    error=exception,
                    context_name="Integration",
                    context_content={
                        "integrationName": integration.name,
                        "integrationType": integration.integrationType,
                        "responseData": response,
                        "requestData": str(billing)[:100],
                    },
                )
                continue

            logger.error(f"Failed to publish billing report to redox for {integration.name} with empty response")
        except Exception as e:
            logger.error(f"Could not create and send billing report for user:{user} with error: {e}")
            report_exception(e)


# todo this part of the code is duplicated almost - can refactor so it could be reused
def convert_to_redox_patient_json(user_email: str, user_id: str, integration: IntegrationDTO):
    # search user see if they have user profile
    response = FindPatientProfileByUserIdUseCase().execute(user_id)
    patient_profile = response.value

    if not patient_profile:
        # if there is no patient profile for this user, search in ehr
        identifiers, demographics = patient_search_by_demographics(user_id, integration)

        return identifiers, demographics

    if patient_profile:
        profile = patient_profile.to_dict()

        demographics = {
            LAST_NAME: profile["lastName"],
            FIRST_NAME: profile["firstName"],
        }
        identifiers = profile["identifiers"]

        identifiers = fix_case(identifiers)

        return identifiers, demographics

    logger.info("There is no patient profile for this user with the email {}".format(user_email))

    return None, None


def patient_search_by_demographics(user_id: str, integration: IntegrationDTO):
    user = AuthorizationService().retrieve_simple_user_profile(user_id=user_id)

    email = user.email

    given_name = user.givenName
    family_name = user.familyName
    birth_date = user.dateOfBirth

    if birth_date and family_name and given_name:
        dob_string = birth_date.strftime("%Y-%m-%d")
        data = {
            PATIENT_DEMOGRAPHICS: {
                FIRST_NAME: given_name,
                LAST_NAME: family_name,
                DOB: dob_string,
            }
        }
        ehr_api = EHRApi(integration)
        patient_dict = ehr_api.query_patient_search(data)

        if patient_dict:
            identifiers = lowercase(patient_dict[IDENTIFIERS])

            payload = {
                PatientProfileDTO.USER_ID: user_id,
                PatientProfileDTO.IDENTIFIERS: identifiers,
                PatientProfileDTO.EMAIL: email,
                PatientProfileDTO.STATUS: PatientProfileDTO.Status.ON_BOARDED.value,
                PatientProfileDTO.FIRST_NAME: patient_dict[PATIENT_DEMOGRAPHICS][FIRST_NAME],
                PatientProfileDTO.LAST_NAME: patient_dict[PATIENT_DEMOGRAPHICS][LAST_NAME],
            }
            request_object = CreatePatientProfileRequestObject.from_dict(payload, ignore_none=True)
            response = CreatePatientProfileUseCase().execute(request_object)

            logger.info("Successfully saved patient profile with id : {}".format(response))

            identifiers = patient_dict[IDENTIFIERS]
            demographics = {
                LAST_NAME: patient_dict[PATIENT_DEMOGRAPHICS][LAST_NAME],
                FIRST_NAME: patient_dict[PATIENT_DEMOGRAPHICS][FIRST_NAME],
            }

            return identifiers, demographics
        else:
            message = "No patient found in EHR for given userId."

            logger.error(message, extra={"userId": user_id})
            exception = Exception(message)

            report_exception(
                error=exception,
                context_name="Integration",
                context_content={
                    "integrationName": integration.name,
                    "integrationType": integration.integrationType,
                },
                user_id=user_id,
            )
            return None, None


def remove_non_digit_identifiers(identifiers: list):
    new_identifiers = []

    if not identifiers:
        return identifiers

    for identifier in identifiers:
        if identifier["ID"].isdigit():
            new_identifiers.append(identifier)

    return new_identifiers
