from abc import ABC, abstractmethod
from typing import Optional

from billing.components.core.dtos.cpt_constants import CPTRuleTypes
from billing.components.core.interfaces.cpt_calculator import ICPTCalculator
from billing.components.core.interfaces.cpt_rules import ICPTRule, ICPTRulesEngine
from billing.components.core.router.billing_report_requests import CPTMappingNumber


class ICPTCalculatorFactory(ABC):
    """Factory interface for creating CPT calculators"""

    @abstractmethod
    def create_calculator(self, cpt_mapping: CPTMappingNumber) -> Optional[ICPTCalculator]:
        """Create appropriate calculator for CPT mapping"""
        pass

    @abstractmethod
    def register_calculator(self, cpt_mapping: CPTMappingNumber, calculator: ICPTCalculator) -> None:
        """Register a calculator for specific CPT mapping"""
        pass

    @abstractmethod
    def get_supported_mappings(self) -> list[CPTMappingNumber]:
        """Get all supported CPT mappings"""
        pass


class ICPTRulesEngineFactory(ABC):
    """Factory interface for creating CPT rules engines"""

    @abstractmethod
    def create_rules_engine(self, cpt_mapping: CPTMappingNumber) -> ICPTRulesEngine:
        """Create rules engine for specific CPT mapping"""
        pass

    @abstractmethod
    def create_rule(self, rule_type: CPTRuleTypes, **kwargs) -> ICPTRule:
        """Create individual rule of specified type"""
        pass

    @abstractmethod
    def get_default_rules_for_mapping(self, cpt_mapping: CPTMappingNumber) -> list[ICPTRule]:
        """Get default rules for CPT mapping"""
        pass
