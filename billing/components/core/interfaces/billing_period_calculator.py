from abc import ABC, abstractmethod
from datetime import date

from billing.components.core.dtos.billing_period import BillingPeriod


class BillingPeriodCalculator(ABC):
    """Abstract base class for billing period calculation strategies"""

    @abstractmethod
    def get_current_period(self, first_submission_date: date, reference_date: date, user_id: str) -> BillingPeriod:
        """Get current billing period for reference date"""
        pass

    @abstractmethod
    def get_period_for_user(self, user_id: str, reference_date: date) -> BillingPeriod:
        """Get billing period for specific user considering their history"""
        pass

    @abstractmethod
    def is_first_period_for_user(self, user_id: str, period: BillingPeriod) -> bool:
        """Check if this is the first billing period for user"""
        pass

    @abstractmethod
    def get_next_period(self, current_period: BillingPeriod) -> BillingPeriod:
        """Get the next billing period"""
        pass

    @abstractmethod
    def get_previous_period(self, current_period: BillingPeriod) -> BillingPeriod:
        """Get the previous billing period"""
        pass
