from abc import ABC, abstractmethod
from datetime import date, datetime
from typing import Optional

from billing.components.core.dtos.billing_context import BillingCalculationContext
from billing.components.core.dtos.cpt_result import CPTResult
from billing.components.core.router.billing_report_requests import CPTMappingNumber


class ICPTCalculator(ABC):
    """Base interface for CPT code calculators"""

    @abstractmethod
    def calculate(self, context: BillingCalculationContext) -> Optional[CPTResult]:
        """Calculate CPT code result based on context"""
        pass

    @abstractmethod
    def get_supported_cpt_mapping(self) -> CPTMappingNumber:
        """Get the CPT mapping number this calculator supports"""
        pass

    @abstractmethod
    def validate_context(self, context: BillingCalculationContext) -> bool:
        """Validate if context is suitable for this calculator"""
        pass


class ISubmissionBasedCPTCalculator(ICPTCalculator):
    """Interface for CPT calculators based on submission data"""

    @abstractmethod
    def calculate_submission_compliance(self, user_id: str, period_start: date, period_end: date) -> tuple[int, bool]:
        """Calculate submission compliance (days_count, is_compliant)"""
        pass

    @abstractmethod
    def find_compliance_date(self, user_id: str, period_start: date, period_end: date) -> Optional[date]:
        """Find date when submission compliance was achieved"""
        pass


class ITimeBasedCPTCalculator(ICPTCalculator):
    """Interface for CPT calculators based on time tracking"""

    @abstractmethod
    def calculate_time_compliance(self, user_id: str, start_date: datetime, end_date: datetime) -> tuple[float, bool]:
        """Calculate time compliance (minutes_spent, is_compliant)"""
        pass

    @abstractmethod
    def get_required_time_threshold(self) -> int:
        """Get minimum time threshold in seconds"""
        pass

    @abstractmethod
    def calculate_billable_time(self, total_time: float) -> float:
        """Calculate billable time from total time spent"""
        pass


class IVideoCallBasedCPTCalculator(ICPTCalculator):
    """Interface for CPT calculators requiring video calls"""

    @abstractmethod
    def has_required_video_calls(self, user_id: str, start_date: datetime, end_date: datetime) -> bool:
        """Check if user has required video calls"""
        pass

    @abstractmethod
    def get_earliest_call_date(self, user_id: str, start_date: datetime, end_date: datetime) -> Optional[datetime]:
        """Get earliest answered call date"""
        pass
