from abc import ABC, abstractmethod
from datetime import date
from typing import Optional

from billing.components.core.dtos.billing_context import BillingCalculationContext
from billing.components.core.router.billing_response_objects import CPTRecordStatusType


class ICPTRule(ABC):
    """Base interface for CPT billing rules"""

    @abstractmethod
    def evaluate(self, context: BillingCalculationContext) -> CPTRecordStatusType:
        """Evaluate the rule and return status"""
        pass

    @abstractmethod
    def get_rule_name(self) -> str:
        """Get rule identifier"""
        pass

    @abstractmethod
    def is_applicable(self, context: BillingCalculationContext) -> bool:
        """Check if rule is applicable to context"""
        pass


class ISubmissionRule(ICPTRule):
    """Interface for submission-based rules"""

    @abstractmethod
    def evaluate_submission_compliance(
        self, submission_days: int, days_in_period: int, days_elapsed: int, is_first_period: bool
    ) -> CPTRecordStatusType:
        """Evaluate submission compliance"""
        pass


class ITimeRule(ICPTRule):
    """Interface for time-based rules"""

    @abstractmethod
    def get_required_time_seconds(self) -> int:
        """Get required time in seconds"""
        pass

    @abstractmethod
    def evaluate_time_compliance(self, time_spent: float, has_prerequisite: bool) -> CPTRecordStatusType:
        """Evaluate time compliance"""
        pass


class IVideoCallRule(ICPTRule):
    """Interface for video call rules"""

    @abstractmethod
    def evaluate_call_requirement(self, has_answered_calls: bool, time_spent: float) -> CPTRecordStatusType:
        """Evaluate video call requirement"""
        pass


class ICPTRulesEngine(ABC):
    """Interface for CPT rules engine"""

    @abstractmethod
    def add_rule(self, rule: ICPTRule) -> None:
        """Add a rule to the engine"""
        pass

    @abstractmethod
    def remove_rule(self, rule_name: str) -> None:
        """Remove a rule from the engine"""
        pass

    @abstractmethod
    def evaluate_all_rules(self, context: BillingCalculationContext) -> CPTRecordStatusType:
        """Evaluate all applicable rules"""
        pass

    @abstractmethod
    def get_applicable_rules(self, context: BillingCalculationContext) -> list[ICPTRule]:
        """Get all applicable rules for context"""
        pass

    @abstractmethod
    def get_earliest_compliance_date(self, context: BillingCalculationContext) -> Optional[date]:
        """Get earliest date when all rules are satisfied"""
        pass
