import json
import logging
import os
from datetime import datetime

from dotenv import load_dotenv
from pymongo import MongoClient

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

USER_COLLECTION = "user"
UPDATE_DATE_TIME = "updateDateTime"


def update_icd_codes(db, deployment_ids, config, dry_run=True):
    """
    Update the ICD-10 codes for the given deployments.
    """

    for deployment_id in deployment_ids:
        update_icd_codes_for_deployment(db, deployment_id, config, dry_run)


def update_icd_codes_for_deployment(db, deployment_id, config, dry_run=True):
    """
    Update the ICD-10 codes for the given deployment.
    """

    old_codes = []
    new_codes = []

    for index, icd_10_code in enumerate(config["ICD_10_CODES"]["OLD"]):
        old_code = icd_10_code["Code"]
        new_code = config["ICD_10_CODES"]["NEW"][index]["Code"]

        old_codes.append(old_code)
        new_codes.append(new_code)

    no_icd_10_code_filter = {
        "componentsData.billing.diagnosis.icd10Code": {"$exists": False},
        "roles.roleId": "User",
        "roles.resource": "deployment/{}".format(deployment_id),
    }

    with_icd_10_code_filter = {
        "componentsData.billing.diagnosis.icd10Code": {"$exists": True},
        "roles.roleId": "User",
        "roles.resource": "deployment/{}".format(deployment_id),
    }

    no_icd_10_code_documents = db[USER_COLLECTION].find(no_icd_10_code_filter)
    with_icd_10_code_documents = db[USER_COLLECTION].find(with_icd_10_code_filter)

    for document in no_icd_10_code_documents:
        logger.info(f"Matching user : {document['_id']} for deployment {deployment_id} without ICD-10 code")

    # Print all users that have the ICD-10 code
    for document in with_icd_10_code_documents:
        icd_10_code_v = document["componentsData"]["billing"]["diagnosis"]["icd10Code"]

        if icd_10_code_v in old_codes:
            logger.info(
                f"Matching user : {document['_id']} for deployment {deployment_id} with ICD-10 code {icd_10_code_v} which is in the list"
            )
        else:
            logger.info(
                f"Matching user : {document['_id']} for deployment {deployment_id} with ICD-10 code {icd_10_code_v} which is not in the list"
            )

    for index, icd_10_code in enumerate(config["ICD_10_CODES"]["OLD"]):
        old_code = icd_10_code["Code"]
        new_code = config["ICD_10_CODES"]["NEW"][index]["Code"]

        utc_now = datetime.utcnow()
        new_values = {
            "$set": {
                "componentsData.billing.diagnosis.icd10Code": new_code,
                UPDATE_DATE_TIME: utc_now,
            }
        }
        # Query to find documents with the specified value
        query = {
            "componentsData.billing.diagnosis.icd10Code": old_code,
            "roles.roleId": "User",
            "roles.resource": "deployment/{}".format(deployment_id),
        }

        if dry_run:
            result_count = db[USER_COLLECTION].count_documents(query)
            logger.info(
                f"Users for deployment {deployment_id} with ICD-10 code {old_code} - result count: {result_count}"
            )
        else:
            result = db[USER_COLLECTION].update_many(query, new_values)
            logger.info(
                f"Users for deployment {deployment_id} replaced ICD-10 code {old_code} with new ICD-10 code {new_code} - result count: {result.modified_count}"
            )


def get_db():
    db_url = os.environ.get("MP_MONGODB_URL")
    db_name = os.environ.get("MP_DB_NAME")

    client = MongoClient(db_url)
    db = client.get_database(db_name)
    return db


def read_config(file_path="icd_codes_config.json"):
    """
    Read the configuration from the file. It's an object with the following structure:
    {
      "ICD_10_CODES": { icd-10 old/new config },
      "DEPLOYMENT_IDS": { deployment_ids config }
    }
    """
    with open(file_path, "r") as file:
        return json.load(file)


if __name__ == "__main__":
    try:
        load_dotenv(".env", override=True)
    except ImportError:
        ...

    DRY_RUN = os.getenv("DRY_RUN", "True").lower() not in ("false", "0", "f")

    database = get_db()
    configs = read_config()
    deployment_ids_key = os.environ.get("DEPLOYMENT_IDS_KEY") or "ASTHMA_PROD"
    logger.info(f"Using deployment ids key: {deployment_ids_key}")
    deployment_ids = configs["DEPLOYMENT_IDS"][deployment_ids_key]
    logger.info(f"Read {len(deployment_ids)} deployment ids")

    update_icd_codes(database, deployment_ids, configs, dry_run=DRY_RUN)

    database.client.close()
