from collections import defaultdict
from datetime import datetime, timedelta

from bson import ObjectId
from django.db.models import Q

from billing.components.core.dtos.billing_models import BillingMonitoringLogDTO
from billing.components.core.models import BillingMonitoringLog
from billing.components.core.repository.billing_repository import (
    BillingMonitoringLogRepository,
)
from sdk.common.exceptions.exceptions import ObjectDoesNotExist
from sdk.common.utils.validators import id_as_obj_id, model_to_dict


class PostgresBillingMonitoringLogRepository(BillingMonitoringLogRepository):
    def create_monitoring_log(self, log: BillingMonitoringLogDTO, original_log_id: str = None) -> str:
        log_id = str(ObjectId())
        doc = BillingMonitoringLog(
            **{
                **log.to_dict(),
                BillingMonitoringLog.MONGO_ID: log_id,
                BillingMonitoringLog.ORIGINAL_LOG_ID: original_log_id or log_id,
            }
        )
        doc.save()
        return doc.mongoId

    def retrieve_active_user_monitoring_logs(
        self,
        user_id: str,
        limit: int,
        skip: int,
    ) -> tuple[list[BillingMonitoringLogDTO], int]:
        query = Q(userId=user_id)
        query &= Q(status=BillingMonitoringLogDTO.BillingMonitoringLogStatus.ACTIVE.value)
        logs = BillingMonitoringLog.objects.filter(query).order_by("-startDateTime")[skip : skip + limit]
        total = BillingMonitoringLog.objects.filter(query).count()

        return [BillingMonitoringLogDTO.from_dict(model_to_dict(log)) for log in logs or []], total

    def retrieve_monitoring_log_by_id(self, log_id: str) -> BillingMonitoringLogDTO:
        log = BillingMonitoringLog.objects.filter(mongoId=log_id).first()
        if not log:
            raise ObjectDoesNotExist
        return BillingMonitoringLogDTO.from_dict(model_to_dict(log))

    def update_monitoring_log(self, log_id: str, updated_fields: dict) -> str:
        log = BillingMonitoringLog.objects.filter(mongoId=log_id).first()
        if not log:
            raise ObjectDoesNotExist
        for key, value in updated_fields.items():
            setattr(log, key, value)
        log.save()
        return log.mongoId

    def delete_user_data(self, user_id: str):
        deleted_count, _ = BillingMonitoringLog.objects.filter(userId=user_id).delete()
        return deleted_count

    def retrieve_user_monitoring_logs_from_to(
        self,
        from_dt: datetime,
        to_dt: datetime,
        user_ids: list[str],
        deployment_id: str = None,
    ) -> dict[str:list]:
        query = Q(startDateTime__gte=from_dt, startDateTime__lt=to_dt + timedelta(days=1))
        query &= Q(userId__in=user_ids)
        if deployment_id:
            query &= Q(deploymentId=deployment_id)

        logs = BillingMonitoringLog.objects.filter(query).order_by("userId", "initialCreateDateTime", "createDateTime")
        results = defaultdict(list)
        for log in logs:
            usr_id = log.userId
            results[usr_id].append(BillingMonitoringLogDTO.from_dict(model_to_dict(log), use_validator_field=False))

        return results

    @id_as_obj_id
    def move_logs_between_deployments(
        self,
        user_id: str,
        source_id: str,
        target_id: str,
        from_dt: datetime,
    ):
        """
        Move logs between deployments for a user in Django ORM.
        """
        query = Q(userId=user_id, startDateTime__gte=from_dt, deploymentId=source_id)

        BillingMonitoringLog.objects.filter(query).update(deploymentId=target_id)

    def retrieve_all_monitoring_logs_for_user(self, user_id: str) -> list[BillingMonitoringLogDTO]:
        query = Q(userId=user_id)
        logs = BillingMonitoringLog.objects.filter(query).order_by("createDateTime")
        return [BillingMonitoringLogDTO.from_dict(model_to_dict(log)) for log in logs]
