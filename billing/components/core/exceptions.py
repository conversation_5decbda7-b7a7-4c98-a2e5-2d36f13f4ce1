from sdk.common.exceptions.exceptions import DetailedException


class BillingErrorCodes:
    PRODUCT_TYPE_NOT_FOUND_FOR_DEPLOYMENT_BILLING = 200001
    SUBMISSION_RECORD_NOT_FOUND = 200002
    CLINICIAN_NOT_ACTIVE = 200003
    PATIENT_NOT_ACTIVE = 200004
    INVALID_LOG_DURATION = 200005
    # 200007 is used in SDK
    DIFFERENT_BILLING_CONFIGURATION = 200008


class CouldNotFoundProductTypeForDeploymentBilling(DetailedException):
    """If Product type doesn't exist for deployment billing."""

    def __init__(self, message=False):
        super().__init__(
            code=BillingErrorCodes.PRODUCT_TYPE_NOT_FOUND_FOR_DEPLOYMENT_BILLING,
            debug_message=message or "Product Type is not set for deployment",
            status_code=404,
        )


class NoSubmissionRecordExistsInHistory(DetailedException):
    """If User have no submission records for a specific deployment"""

    def __init__(self, message=False):
        super().__init__(
            code=BillingErrorCodes.SUBMISSION_RECORD_NOT_FOUND,
            debug_message=message or "No submission record exists",
            status_code=404,
        )


class ClinicianNotActive(DetailedException):
    """If Clinician is not active/registered at given time"""

    def __init__(self, message=False):
        super().__init__(
            code=BillingErrorCodes.CLINICIAN_NOT_ACTIVE,
            debug_message=message or "Clinician is not active",
            status_code=400,
        )


class PatientNotActive(DetailedException):
    """If Patient is not active/registered at given time"""

    def __init__(self, message=False):
        super().__init__(
            code=BillingErrorCodes.PATIENT_NOT_ACTIVE,
            debug_message=message or "Patient is not active",
            status_code=400,
        )


class InvalidLogDuration(DetailedException):
    """If Log duration is not valid"""

    def __init__(self, message=False):
        super().__init__(
            code=BillingErrorCodes.INVALID_LOG_DURATION,
            debug_message=message or "Log duration is not valid",
            status_code=400,
        )


class MoveBillingUserException(DetailedException):
    def __init__(self, message=False):
        super().__init__(
            code=BillingErrorCodes.DIFFERENT_BILLING_CONFIGURATION,
            debug_message=message or "Unable to move user due to different billing configurations",
            status_code=400,
        )
