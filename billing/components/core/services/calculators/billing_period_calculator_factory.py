from billing.components.core.interfaces.billing_period_calculator import BillingPeriodCalculator
from billing.components.core.services.billing_data_facade import BillingDataFacade
from billing.components.core.services.calculators.calendar_period_calculator import CalendarPeriodCalculator
from billing.components.core.services.calculators.classic_period_calculator import ClassicPeriodCalculator
from sdk.common.utils.inject import autoparams


class BillingPeriodCalculatorFactory:
    """Factory for creating billing period calculators"""

    @autoparams()
    def __init__(self, data_facade: BillingDataFacade):
        self.data_facade = data_facade

    def create_calculator(self, use_calendar_calculation: bool) -> BillingPeriodCalculator:
        """Create appropriate calculator based on calculation mode"""
        if use_calendar_calculation:
            return CalendarPeriodCalculator(self.data_facade)
        else:
            return ClassicPeriodCalculator(self.data_facade)
