from datetime import date, timedelta

from billing.components.core.dtos.billing_period import BillingPeriod
from billing.components.core.interfaces.billing_period_calculator import BillingPeriodCalculator
from billing.components.core.services.billing_data_facade import BillingDataFacade
from sdk.common.utils.inject import autoparams


class ClassicPeriodCalculator(BillingPeriodCalculator):
    """Calculator for classic 30-day billing periods"""

    @autoparams()
    def __init__(self, data_facade: BillingDataFacade):
        self.data_facade = data_facade

    def get_current_period(self, first_submission_date: date, reference_date: date, user_id: str) -> BillingPeriod:
        days_elapsed = (reference_date - first_submission_date).days % 30
        current_billing_start_date = reference_date - timedelta(days=days_elapsed)
        current_billing_end_date = current_billing_start_date + timedelta(days=29)
        days_in_period = (current_billing_end_date - current_billing_start_date).days % 30
        return BillingPeriod(
            startDate=current_billing_start_date,
            endDate=current_billing_end_date,
            periodType="STANDARD",
            daysInPeriod=days_in_period,
            isFirstPeriod=first_submission_date == current_billing_start_date,
            daysElapsed=days_elapsed,
        )

    def get_period_for_user(self, user_id: str, reference_date: date) -> BillingPeriod:
        """Get classic 30-day period based on user's first submission date"""
        first_submission_date = self.data_facade.get_first_submission_date(user_id)

        if not first_submission_date:
            # No submissions yet - return pending period starting from reference date
            return BillingPeriod(
                startDate=reference_date, endDate=reference_date + timedelta(days=29), periodType="STANDARD"
            )

        # Calculate how many 30-day periods have passed since first submission
        days_since_first = (reference_date - first_submission_date).days
        period_number = days_since_first // 30

        # Calculate current period start
        period_start = first_submission_date + timedelta(days=period_number * 30)
        period_end = period_start + timedelta(days=29)

        return BillingPeriod(startDate=period_start, endDate=period_end, periodType="STANDARD")

    def is_first_period_for_user(self, user_id: str, period: BillingPeriod) -> bool:
        """Check if this is the first classic period for user"""
        first_submission_date = self.data_facade.get_first_submission_date(user_id)
        if not first_submission_date:
            return True
        return period.startDate == first_submission_date

    def get_next_period(self, current_period: BillingPeriod) -> BillingPeriod:
        """Get next classic period"""
        next_start = current_period.endDate + timedelta(days=1)
        next_end = next_start + timedelta(days=29)
        return BillingPeriod(startDate=next_start, endDate=next_end, periodType="STANDARD")

    def get_previous_period(self, current_period: BillingPeriod) -> BillingPeriod:
        """Get previous classic period"""
        prev_end = current_period.startDate - timedelta(days=1)
        prev_start = prev_end - timedelta(days=29)
        return BillingPeriod(startDate=prev_start, endDate=prev_end, periodType="STANDARD")
