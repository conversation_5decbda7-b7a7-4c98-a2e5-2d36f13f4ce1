from datetime import date, timedelta

from billing.components.core.dtos.billing_period import BillingPeriod
from billing.components.core.helpers.billing_periods_calculators import BillingCalendarPeriodCalculator
from billing.components.core.interfaces.billing_period_calculator import BillingPeriodCalculator
from billing.components.core.services.billing_data_facade import BillingDataFacade
from sdk.common.utils.inject import autoparams


class CalendarPeriodCalculator(BillingPeriodCalculator):
    """Calculator for calendar-based billing periods"""

    @autoparams()
    def __init__(self, data_facade: BillingDataFacade):
        self.data_facade = data_facade  # TODO: replace with repo ?
        self.calendar_calculator = BillingCalendarPeriodCalculator()

    def get_current_period(self, first_submission_date: date, reference_date: date, user_id: str) -> BillingPeriod:
        """Get current calendar-based billing period"""
        period = self._get_calendar_period(reference_date)
        period.daysInPeriod = (period.endDate - period.startDate).days + 1
        period.daysElapsed = (reference_date - period.startDate).days
        period.isFirstPeriod = (first_submission_date == period.startDate,)
        return period

    def get_period_for_user(self, user_id: str, reference_date: date) -> BillingPeriod:
        """Get calendar period for user (same as current period since calendar is global)"""
        return self._get_calendar_period(reference_date)

    def is_first_period_for_user(self, user_id: str, period: BillingPeriod) -> bool:
        """Check if this is the first calendar period for user"""
        first_submission_date = self.data_facade.get_first_submission_date(user_id)
        if not first_submission_date:
            return True
        first_period = self._get_calendar_period(first_submission_date)
        return period.startDate == first_period.startDate

    def get_next_period(self, current_period: BillingPeriod) -> BillingPeriod:
        """Get next calendar period"""
        next_month_date = current_period.endDate + timedelta(days=1)
        return self._get_calendar_period(next_month_date)

    def get_previous_period(self, current_period: BillingPeriod) -> BillingPeriod:
        """Get previous calendar period"""
        prev_month_date = current_period.startDate - timedelta(days=1)
        return self._get_calendar_period(prev_month_date)

    def _get_calendar_period(self, reference_date: date) -> BillingPeriod:
        """Get calendar-based period using existing calculator"""
        start_date, end_date = self.calendar_calculator.calculate(reference_date)

        # Determine period type based on months
        period_type = "CALENDAR"
        if reference_date.month in BillingCalendarPeriodCalculator.WINTER_MONTHS:
            period_type = "WINTER"

        return BillingPeriod(startDate=start_date, endDate=end_date, periodType=period_type)
