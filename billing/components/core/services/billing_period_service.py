from datetime import datetime, date, timedelta, timezone

from billing.components.core.dtos.billing_period import BillingPeriod
from billing.components.core.dtos.cpt_constants import CPTSubmissionThresholds
from billing.components.core.helpers.export_billing_submission_helpers import (
    get_calendar_submission_period,
    generate_calendar_bp_periods,
)
from billing.components.core.interfaces.billing_period_calculator import BillingPeriodCalculator
from billing.components.core.repository.billing_repository import BillingSubmissionRepository
from billing.components.core.services.calculators.billing_period_calculator_factory import (
    BillingPeriodCalculatorFactory,
)
from sdk.common.utils.inject import autoparams


class BillingPeriodService:
    @autoparams("repo")
    def __init__(self, repo: BillingSubmissionRepository, use_calendar_calculation: bool):
        self.calculator_factory = BillingPeriodCalculatorFactory()
        self.repo = repo
        self.use_calendar_calculation = use_calendar_calculation

    @property
    def today(self):
        return datetime.now(timezone.utc).date()

    def get_first_billable_period(self, user_id: str, use_calendar_calculation: bool) -> BillingPeriod | None:
        """Get the first billable period for a user based on calculation mode"""
        calculator = self.calculator_factory.create_calculator(use_calendar_calculation)
        if not use_calendar_calculation:
            return self._get_first_or_current_billable_period(user_id, period_calculator=calculator)
        return self._get_first_or_current_calendar_billable_period(user_id, period_calculator=calculator)

    def get_current_period(
        self, user_id: str, first_submission_date: date, reference_date: date, use_calendar_calculation: bool
    ) -> BillingPeriod:
        """Get current billing period based on calculation mode"""
        calculator = self.calculator_factory.create_calculator(use_calendar_calculation)
        return calculator.get_current_period(first_submission_date, reference_date, user_id)

    def get_period_for_user(self, user_id: str, reference_date: date, use_calendar_calculation: bool) -> BillingPeriod:
        """Get billing period for specific user considering their first submission date"""
        calculator = self.calculator_factory.create_calculator(use_calendar_calculation)
        return calculator.get_period_for_user(user_id, reference_date)

    def is_first_period_for_user(self, user_id: str, period: BillingPeriod, use_calendar_calculation: bool) -> bool:
        """Check if this is the first billing period for user"""
        calculator = self.calculator_factory.create_calculator(use_calendar_calculation)
        return calculator.is_first_period_for_user(user_id, period)

    def get_period_containing_date(self, target_date: date, use_calendar_calculation: bool) -> BillingPeriod:
        """Get the billing period that contains the target date"""
        calculator = self.calculator_factory.create_calculator(use_calendar_calculation)
        return calculator.get_current_period(target_date, target_date, "")

    def get_next_period(self, current_period: BillingPeriod, use_calendar_calculation: bool) -> BillingPeriod:
        """Get the next billing period"""
        calculator = self.calculator_factory.create_calculator(use_calendar_calculation)
        return calculator.get_next_period(current_period)

    def get_previous_period(self, current_period: BillingPeriod, use_calendar_calculation: bool) -> BillingPeriod:
        """Get the previous billing period"""
        calculator = self.calculator_factory.create_calculator(use_calendar_calculation)
        return calculator.get_previous_period(current_period)

    def _get_first_or_current_billable_period(
        self,
        user_id: str,
        deployment_id: str = None,
        check_upper_bound: date = None,
        return_current_period: bool = True,
        period_calculator: BillingPeriodCalculator = None,
    ) -> BillingPeriod | None:
        """Find the first 30-day billable period (with at least 16 submissions) for a user"""
        submissions = self.repo.get_total_submissions_for_user_from_to(user_id, deployment_id)
        if not submissions:
            return None

        first_submission_date = min(submissions.keys())
        current_date = check_upper_bound or self.today
        # Iterate through 30-day periods starting from first submission
        period_start = first_submission_date
        while period_start <= current_date:
            period = period_calculator.get_current_period(first_submission_date, period_start, user_id)

            # Count submissions in this period
            submission_count = 0
            for submission_date, count in submissions.items():
                if period.startDate <= submission_date <= period.endDate:
                    submission_count += count

            # Check if this period has enough submissions to be billable
            if submission_count > CPTSubmissionThresholds.MINIMUM_COMPLIANCE_DAYS:
                return BillingPeriod(startDate=period.startDate, endDate=period.endDate, periodType="STANDARD")

            period_start += timedelta(days=1)

        # Fallback to current period if no billable period found
        if return_current_period:
            current_period = period_calculator.get_current_period(first_submission_date, self.today, user_id)
            return BillingPeriod(
                startDate=current_period.startDate, endDate=current_period.endDate, periodType="STANDARD"
            )
        return None

    def _get_first_or_current_calendar_billable_period(
        self, user_id: str, check_until: date = None, period_calculator: BillingPeriodCalculator = None
    ) -> BillingPeriod:
        """Find the first calendar billable period (monthly) with at least 16 submissions for a user"""
        user_first_submission = self.repo.find_user_first_submission(user_id)
        if not user_first_submission:
            return period_calculator.get_current_period(self.today, self.today, user_id)

        first_submission_date = user_first_submission.createDateTime.date()
        check_until = check_until or self.today

        # Get calendar submission period
        calendar_period_start, calendar_period_end = get_calendar_submission_period(first_submission_date)

        # Generate calendar billing periods
        calendar_bp_periods = generate_calendar_bp_periods(calendar_period_start, check_until)

        # Find billable calendar months
        billable_months = self.repo.find_billable_calendar_months_in_periods(user_id, calendar_bp_periods)

        if billable_months:
            first_billable_date = min(billable_months).replace(day=1)
            period = period_calculator.get_current_period(first_submission_date, first_billable_date, user_id)
            return period.startDate, period.endDate

        # Fallback to current month
        return period_calculator.get_current_period(first_submission_date, self.today, user_id)
