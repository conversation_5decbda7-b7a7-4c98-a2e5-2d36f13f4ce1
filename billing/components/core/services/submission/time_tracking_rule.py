from billing.components.core.dtos.billing_context import BillingCalculationContext
from billing.components.core.dtos.cpt_constants import CPTTimeThresholds
from billing.components.core.interfaces.cpt_rules import ITimeRule
from billing.components.core.router.billing_response_objects import CPTRecordStatusType


class TimeBasedRule(ITimeRule):
    def evaluate_time_compliance(self, time_spent: float, has_prerequisite: bool) -> CPTRecordStatusType:
        pass

    def __init__(self):
        self.periods = list(CPTTimeThresholds.__dict__.items())

    def get_required_time_seconds(self) -> int:
        return next(iter(self.required_time_seconds))

    def get_time_thresholds_compliance(
        self, time_spent: float, has_prerequisite: bool
    ) -> dict[str, CPTRecordStatusType]:
        """Evaluate time compliance based on time spent and VideoCall presence (prerequisite)"""
        compliance_status = {}
        for label, threshold in self.periods:
            if not has_prerequisite:
                compliance_status[label] = CPTRecordStatusType.PENDING
            elif time_spent < threshold:
                compliance_status[label] = CPTRecordStatusType.IN_PROGRESS
            else:
                compliance_status[label] = CPTRecordStatusType.COMPLETED

        return compliance_status

    def evaluate(self, context: BillingCalculationContext) -> CPTRecordStatusType:
        pass

    def get_rule_name(self) -> str:
        pass

    def is_applicable(self, context: BillingCalculationContext) -> bool:
        pass
