from billing.components.core.dtos.billing_context import BillingCalculationContext
from billing.components.core.dtos.cpt_constants import CPTSubmissionThresholds
from billing.components.core.interfaces.cpt_rules import ISubmissionRule
from billing.components.core.router.billing_response_objects import CPTRecordStatusType


class SubmissionRule(ISubmissionRule):
    def __init__(self, ongoing: bool = False):
        self.ongoing = ongoing
        self.required_days = CPTSubmissionThresholds.REQUIRED_SUBMISSION_DAYS

    def evaluate(self, context: BillingCalculationContext) -> CPTRecordStatusType:
        pass

    def get_rule_name(self) -> str:
        pass

    def is_applicable(self, context: BillingCalculationContext) -> bool:
        pass

    def evaluate_submission_compliance(
        self, submission_days: int, days_in_period: int, days_elapsed: int, is_first_period: bool
    ) -> CPTRecordStatusType:
        if not self._is_period_billable(is_first_period):
            return CPTRecordStatusType.NON_BILLABLE

        if submission_days == 0:
            if days_in_period - days_elapsed < self.required_days:
                return CPTRecordStatusType.INCOMPLETE
            return CPTRecordStatusType.PENDING

        if submission_days >= self.required_days:
            return CPTRecordStatusType.COMPLETED

        # Can still reach 16 days?
        if days_in_period - days_elapsed + submission_days < self.required_days:
            return CPTRecordStatusType.INCOMPLETE

        return CPTRecordStatusType.IN_PROGRESS

    def evaluate_rolling_period_compliance(
        self, submission_days: int, days_elapsed: int, is_first_period: bool
    ) -> CPTRecordStatusType:
        """
        Evaluate compliance for 30-day rolling periods.
        This method handles the specific logic for 30-day rolling billing periods.
        """
        if not self._is_period_billable(is_first_period):
            return CPTRecordStatusType.NON_BILLABLE

        if submission_days == 0:
            if days_elapsed >= 15:  # 30-day period, need 16 days, if 15+ days passed with 0 submissions
                return CPTRecordStatusType.INCOMPLETE
            return CPTRecordStatusType.PENDING

        if submission_days >= self.required_days:
            return CPTRecordStatusType.COMPLETED

        # Can still reach 16 days in remaining time?
        # For 30-day periods: remaining days = 30 - days_elapsed
        if 30 - days_elapsed + submission_days < self.required_days:
            return CPTRecordStatusType.INCOMPLETE

        return CPTRecordStatusType.IN_PROGRESS

    def _is_period_billable(self, is_first_period: bool) -> bool:
        return is_first_period or self.ongoing
