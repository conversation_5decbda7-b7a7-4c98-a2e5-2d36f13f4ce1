{"deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2c"}, "profile": {"fields": {"height": true, "validators": {"dateOfBirth": {"minISODuration": "-P100Y", "maxISODuration": "-P18Y"}}, "genderOptions": [{"displayName": "male", "value": "MALE"}, {"displayName": "female", "value": "FEMALE"}, {"displayName": "other", "value": "OTHER"}], "ethnicityOptions": [{"displayName": "alien", "value": "ALIEN"}]}}, "features": {"messaging": {"enabled": true, "messages": ["Great job! Keep up the good work.", "second message"]}, "labels": true}, "roles": [{"id": {"$oid": "5e8eeae1b707216625ca4203"}, "name": "Custom Role", "permissions": ["MANAGE_PATIENT_DATA", "VIEW_PATIENT_IDENTIFIER", "VIEW_PATIENT_DATA", "CONTACT_PATIENT"]}, {"id": {"$oid": "6009d18864a6786c2a2be181"}, "name": "Read<PERSON>nly", "permissions": ["VIEW_PATIENT_DATA"]}], "name": "hu_deployment_name", "description": "deployment description", "status": "DRAFT", "color": "0x007AFF", "version": 5, "country": "United Kingdom", "learn": {"id": {"$oid": "5e8eeae1b707216625ca4202"}, "sections": [{"id": {"$oid": "5e946c69e8002eac4a107f56"}, "title": "Test section", "order": 10, "articles": [{"id": {"$oid": "5e8c58176207e5f78023e655"}, "title": "article_ss three", "order": 10, "type": "SMALL", "thumbnailUrl": {"region": "eu", "key": "deployment/5d386cc6ff885918d96edb2c/section/5e946c69e8002eac4a107f56/article/5e8c58176207e5f78023e655/assets/sample.png", "bucket": "integrationtests"}, "content": {"type": "VIDEO", "timeToRead": "20m", "textDetails": "Here what you read", "videoUrl": {"bucket": "integrationtests", "key": "deployment/5d386cc6ff885918d96edb2c/section/5e946c69e8002eac4a107f56/article/5e8c58176207e5f78023e655/assets/sample.mp4", "region": "eu"}}, "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}}], "updateDateTime": {"$date": {"$numberLong": "1586796185235"}}, "createDateTime": {"$date": {"$numberLong": "1586796185235"}}}], "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}}, "moduleConfigs": [{"id": {"$oid": "5e94b2007773091c9a592650"}, "about": "string", "configBody": {}, "moduleId": "BloodPressure", "moduleName": "hu_BloodPressure_moduleName", "shortModuleName": "hu_BloodPressure_shortModuleName", "notificationData": {"title": "someTitle", "body": "someBody"}, "schedule": {"isoDuration": "P1W", "timesPerDuration": 1, "timesOfDay": ["BEFORE_DINNER"], "friendlyText": "hu_BloodPressure_schedule_friendlyText"}, "status": "ENABLED", "learnArticleIds": ["5e8c58176207e5f78023e655"]}, {"id": {"$oid": "6067143e1bb1c236e998ea39"}, "about": "string", "configBody": {}, "moduleId": "AZScreeningQuestionnaire", "moduleName": "AZScreeningQuestionnaire", "schedule": {"isoDuration": "P1W", "timesPerDuration": 1, "timesOfDay": ["BEFORE_DINNER"], "friendlyText": "AZScreeningQuestionnaire"}, "status": "ENABLED"}, {"id": {"$oid": "6067143e1bb1c236e998ea39"}, "about": "string", "configBody": {"complexSymptoms": [{"name": "Symptom rag field name", "scale": [{"severity": 1, "value": "mild"}, {"severity": 2, "value": "severe"}]}]}, "moduleId": "Symptom", "moduleName": "Symptom", "status": "ENABLED", "ragThresholds": [{"type": "VALUE", "severity": 3, "thresholdRange": [{"minValue": 1, "maxValue": 2}], "color": "#FBCCD7", "fieldName": "Symptom rag field name", "enabled": true}]}, {"id": {"$oid": "5e94b2007773091c9a592651"}, "about": "test", "configBody": {"isForManager": false, "pages": [], "name": "Awesome Name in ConfigBody", "complexSymptoms": [{"name": "<PERSON><PERSON>", "scale": [{"severity": 1, "value": "never"}, {"severity": 2, "value": "seldom"}]}]}, "moduleId": "Questionnaire", "moduleName": "Questionnaire", "schedule": {"isoDuration": "P1W", "timesPerDuration": 1, "timesOfDay": ["BEFORE_DINNER"]}, "status": "ENABLED"}], "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}, "consent": {"id": {"$oid": "5e9443789911c97c0b639374"}, "createDateTime": {"$date": {"$numberLong": "1586785704470"}}, "additionalConsentQuestions": [{"type": "isDataSharedForFutureStudies", "enabled": "ENABLED", "format": "BOOLEAN", "text": "Would you like your data to be used in future studies?", "order": 2}], "instituteName": "consent institute name", "instituteFullName": "string", "instituteTextDetails": "string", "enabled": "ENABLED", "revision": 1, "review": {"title": "review", "details": "please review the form below, and tap agree if you are ready to continue. if you have any questions or queries, please contact <NAME_EMAIL>"}, "signature": {"signatureTitle": "signature", "signatureDetails": "please sign using your finger in the box below", "nameTitle": "medopad consent", "nameDetails": "type your full name in text fields below", "hasMiddleName": true}, "sections": [{"type": "OVERVIEW", "details": "hu_consent_sections_0_details", "reviewDetails": "hu_consent_sections_0_reviewDetails"}, {"type": "DATA_PROCESSING", "details": "string", "reviewDetails": "string"}]}, "econsent": {"id": {"$oid": "5e9443789911c97c0b639444"}, "createDateTime": {"$date": {"$numberLong": "1586785704470"}}, "review": {"title": "review", "details": "please review the form below, and tap agree if you are ready to continue. if you have any questions or queries, please contact <NAME_EMAIL>"}, "enabled": "ENABLED", "title": "Informed consent form", "overviewText": "To participate in the trial study, please read  the consent form through in detail. \n\nIf you have any questions, please contact your study <NAME_EMAIL> or +44 1234 567 890 providing your consent to participate.", "contactText": "Please contact your study <NAME_EMAIL> if you have any questions.", "instituteFullName": "string", "instituteName": "string", "instituteTextDetails": "string", "revision": 1, "signature": {"signatureTitle": "Signature", "signatureDetails": "Please sign using your finger in the box below", "nameTitle": "Medopad Consent", "nameDetails": "Type your full name in text fields below", "hasMiddleName": true, "showFirstLastName": true}, "additionalConsentQuestions": [{"type": "isDataSharedForFutureStudies", "enabled": "ENABLED", "format": "BOOLEAN", "text": "Would you like your data to be used in future studies?", "order": 2}, {"type": "isDataSharedForResearch", "enabled": "ENABLED", "format": "BOOLEAN", "text": "Do you allow the research investigators to retrieve your data from the vaccine registry?", "order": 1}], "sections": [{"type": "INTRODUCTION", "title": "Introduction", "details": "INTRODUCTION", "reviewDetails": "You have been asked to participate in a clinical research study initiated, managed, and financed by ABC LAbs, who is the Sponsor of this study. Before your decide, it is important for you to understand why the research is being done and what it will involve. This informed consent form will provide you with essential information about this study and your rights as a study participant so that you can make an informed decision about your participation. Y \nYour decision to participate in this study is entirely voluntary. You will not lose any benefits to which you would otherwise be entitled if you refuse to participant. In addition, you may withdraw from the study at any time without penality or loss of benefits to which you are otherwise entitled. You will be informed in a timely manner, if any relevant new information about this drug or this study becomes available that may alter your willingness to continue to participate. If you agree, your General Practitioner will be told that you are taking part in this study.", "contentType": "IMAGE", "thumbnailLocation": {"key": "deployment/5d386cc6ff885918d96edb2c/econsent/assets/sample.png", "region": "eu", "bucket": "integrationtests"}}, {"type": "PURPOSE", "title": "PURPOSE", "reviewDetails": "You are being asked to participate in this clinical research study because you have high blood pressure and are already taking prescribed commercial Cligoliob for an approved dose and indication in your countr.\nYou are eligible to participate in this study because following discussions with your own doctor you have decided to continue taking Cigoliob.\nInformation regarding the use of Cigoliob may be obtained from the patient information leaflet which accompanies your supply of Cigoliob and from your own treating physician.\nThe purpose of this study is to assess the levels of Cigoliob in blood across the course of the study in study participants with high blood pressure.\nThis study is expected to enroll approximately 100 women who have high blood pressure while takingcommercial Cigoliob across approximately 13 centers throughout Canada, USA, Swizerland and selected countries in the European Union (possible including but not necessarily limited to France, Germany, Spain or Italy).", "contentType": "IMAGE", "thumbnailUrl": "https://www.roadrunnerrecords.com/sites/g/files/g2000005056/f/sample-4.jpg"}, {"type": "REVIEW_TO_SIGN", "title": "REVIEW_TO_SIGN", "reviewDetails": "I have read and understood this consent document. By signing this: \n• I confirm that I have had time to read carefully and understand the study participant informed consent provided for this study.\n• I confirm that I have had the opportunity to discuss the study and ask questions, and I am satisfied with the answers and explanations that I have been provided.\n• I give permission for my medical records to be reviewed by the Sponsor or designee and/or representatives of any Drug Reculatory Authorities such as the U.S. FDA and Insitutional Review Boards.\n• I understand that my participation is voluntary and that I am free to withdraw at any time without giving any reason and without my medical care or legal rights being affected.. I agree that the Sponsor can continue to use the information about my health collected during the study to preserve the integrity of the study, even if I withdraw from the study.", "contentType": "VIDEO", "videoLocation": {"key": "deployment/5d386cc6ff885918d96edb2c/econsent/assets/sample.mp4", "region": "eu", "bucket": "integrationtests"}, "thumbnailLocation": {"key": "deployment/5d386cc6ff885918d96edb2c/econsent/assets/sample.png", "region": "eu", "bucket": "integrationtests"}}, {"type": "DURING_THE_TRIAL", "title": "DURING_THE_TRIAL", "reviewDetails": "During the course of this trial, you’ll be asked to complete a series of task such as:\n• Entering your blood pressure in the morning every day in the Huma app\n•Recording your medication intake in the Huma app\n• Attending telemedicine video conferences with your study care team in the Huma app\n• Attending face-to-face appointments with your study care team every 3 months\nThere are some acitivites that you would not be able to do during the course of the trial. They are:\n• Donating blood\n• Traveling via plane or helicopter", "contentType": "VIDEO", "videoUrl": "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4", "thumbnailUrl": "https://www.roadrunnerrecords.com/sites/g/files/g2000005056/f/sample-4.jpg"}]}, "keyActionsEnabled": true, "keyActions": [{"id": {"$oid": "5f078582c565202bd6cb03af"}, "title": "PAM Questionnaire", "description": "You have a new activity for the DeTAP study. Please complete as soon as you are able to.", "deltaFromTriggerTime": "PT0M", "durationFromTrigger": "P6M", "type": "MODULE", "trigger": "SIGN_UP", "durationIso": "P1DT9H2M", "numberOfNotifications": {"$numberInt": "3"}, "moduleId": "BloodPressure", "moduleConfigId": "5e94b2007773091c9a592650", "updateDateTime": {"$date": {"$numberLong": "1594328450732"}}, "createDateTime": {"$date": {"$numberLong": "1594328450732"}}}, {"id": {"$oid": "5f078582c565202bd6cb03cf"}, "title": "Article KA", "description": "You have a new activity for the DeTAP study. Please complete as soon as you are able to.", "deltaFromTriggerTime": "PT0M", "durationFromTrigger": "P6M", "type": "LEARN", "trigger": "SIGN_UP", "durationIso": "P1DT9H2M", "numberOfNotifications": {"$numberInt": "3"}, "learnArticleId": "5e8c58176207e5f78023e655", "updateDateTime": {"$date": {"$numberLong": "1594328450732"}}, "createDateTime": {"$date": {"$numberLong": "1594328450732"}}}], "extraCustomFields": {"mediclinicNumber": {"errorMessage": "Insurance Number is incorrect", "validation": "\\d{7}", "onboardingCollectionText": "Please enter mediclinic number", "profileCollectionText": "Patient Unique ID", "description": "Please enter mediclinic number description", "required": true, "clinicianUpdate": true, "showClinicianHeader": true, "type": "TEXT", "order": 2}}, "icon": {"key": "deployment/5d386cc6ff885918d96edb2c/sample.png", "region": "eu", "bucket": "integrationtests"}}, {"_id": {"$oid": "612f153c1a297695e4506d53"}, "roles": [{"id": {"$oid": "5e8eeae1b707216625ca4203"}, "name": "Custom Role", "permissions": ["MANAGE_PATIENT_DATA", "VIEW_PATIENT_IDENTIFIER", "VIEW_PATIENT_DATA", "CONTACT_PATIENT"]}, {"id": {"$oid": "6009d18864a6786c2a2be181"}, "name": "Read<PERSON>nly", "permissions": ["VIEW_PATIENT_DATA"]}], "name": "hu_deployment_name", "description": "deployment description", "status": "DRAFT", "color": "0x007AFF", "country": "United Kingdom", "onboardingConfigs": [], "features": {"labels": true}, "labels": [{"id": {"$oid": "5e8eeae1b707216625ca4202"}, "text": "RECOVERED", "authorId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "createDateTime": "2022-04-12T11:35:21.435000Z"}, {"id": {"$oid": "5d386cc6ff885918d96edb2c"}, "text": "IN TREATMENT", "authorId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "createDateTime": "2022-04-12T11:35:21.435000Z"}], "learn": {"id": {"$oid": "5e8eeae1b707216625ca4202"}, "sections": [{"id": {"$oid": "5e946c69e8002eac4a107f56"}, "title": "Test section", "order": 10, "updateDateTime": {"$date": {"$numberLong": "1586796185235"}}, "createDateTime": {"$date": {"$numberLong": "1586796185235"}}}], "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}}, "moduleConfigs": [], "updateDateTime": {"$date": {"$numberLong": "1586433217042"}}, "createDateTime": {"$date": {"$numberLong": "1586433217042"}}, "keyActionsEnabled": true, "keyActions": [], "extraCustomFields": {"mediclinicNumber": {"errorMessage": "Insurance Number is incorrect", "validation": "\\d{7}", "onboardingCollectionText": "Please enter mediclinic number", "profileCollectionText": "Patient Unique ID", "description": "Please enter mediclinic number description", "required": true, "clinicianUpdate": true, "showClinicianHeader": true, "type": "TEXT", "order": 2}}, "icon": {"key": "deployment/612f153c1a297695e4506d53/sample.png", "region": "eu", "bucket": "integrationtests"}}], "calendar": [{"_id": {"$oid": "5f1ae2acdd5b79d19dce1623"}, "_cls": "MongoCalendarEvent", "model": "KeyAction", "title": "PAM Questionnaire", "description": "You have a new activity for the DeTAP study. Please complete as soon as you are able to.", "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "enabled": true, "isRecurring": true, "recurrencePattern": "DTSTART:20200725T090200\nRRULE:FREQ=DAILY;BYHOUR=9;BYMINUTE=2", "startDateTime": {"$date": {"$numberLong": "1595597460000"}}, "endDateTime": {"$date": {"$numberLong": "1910631060000"}}, "extraFields": {"moduleId": "Questionnaire", "moduleConfigId": "5f1acf888f76dbfa8b03a1a3", "keyActionConfigId": "5f078582c565202bd6cb03af"}, "snoozing": ["P3D", "P6D"], "updateDateTime": {"$date": {"$numberLong": "**********"}}, "createDateTime": {"$date": {"$numberLong": "**********"}}}], "huma_auth_user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999999", "displayName": "test", "userAttributes": {"familyName": "test", "givenName": "test", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999998", "displayName": "test", "userAttributes": {"familyName": "test", "givenName": "test", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "60071f359e7e44330f732037"}, "status": 1, "displayName": "clinician", "userAttributes": {"familyName": "clinician", "givenName": "clinician", "dob": "1988-02-20", "gender": "Male"}, "email": "<EMAIL>", "emailVerified": true, "phoneNumber": "+380999993452", "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "60642e821668fbf7381eefa0"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999911120", "displayName": "test", "userAttributes": {"familyName": "test", "givenName": "test", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5eda5db67adadfb49f7ff71d"}, "status": 1, "displayName": "adminUser", "userAttributes": {"familyName": "AdminUser", "givenName": "au", "dob": "1988-02-20", "gender": "Male"}, "email": "<EMAIL>", "phoneNumber": "+380999299995", "emailVerified": true, "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}], "organization": [{"_id": {"$oid": "5fde855f12db509a2785da06"}, "name": "ABC Pharmaceuticals EU Trials 123", "enrollmentTarget": 3000, "studyCompletionTarget": 2800, "status": "DEPLOYED", "deploymentIds": ["5d386cc6ff885918d96edb2c"]}, {"_id": {"$oid": "64523fbc8139538d60ba373d"}, "name": "Move Deployment ORG", "enrollmentTarget": 3000, "studyCompletionTarget": 2800, "status": "DEPLOYED", "deploymentIds": []}], "user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999999999", "masterKey": "88888888", "roles": [{"roleId": "SuperAdmin", "resource": "deployment/*", "userType": "User", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999999998", "masterKey": "88888888", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User"}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}, "language": "de", "finishedOnboarding": true, "boardingStatus": {"status": 0, "updateDateTime": {"$date": "2021-04-12T11:35:21.435000Z"}}}, {"_id": {"$oid": "60071f359e7e44330f732037"}, "givenName": "clinician", "familyName": "clinician", "email": "<EMAIL>", "phoneNumber": "+380999993452", "roles": [{"roleId": "Clinician", "resource": "deployment/5d386cc6ff885918d96edb2c"}], "timezone": "UTC"}, {"_id": {"$oid": "60642e821668fbf7381eefa0"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999911120", "masterKey": "88888888", "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User"}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}, {"_id": {"$oid": "5eda5db67adadfb49f7ff71d"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "phoneNumber": "+380999299995", "masterKey": "88888888", "roles": [{"roleId": "Administrator", "resource": "deployment/612f153c1a297695e4506d53"}], "timezone": "UTC", "createDateTime": {"$date": {"$numberInt": "**********"}}, "updateDateTime": {"$date": {"$numberInt": "**********"}}}], "billing_profile_log_provider": [{"_id": {"$oid": "64b94330e0c69b74a0ac391f"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "billingProviderName": "testBillingProviderName1", "createDateTime": {"$date": "2023-07-20T12:16:38.162Z"}}], "invitation": [{"_id": {"$oid": "605345882f7d4c18ef9e6dbc"}, "_cls": "MongoInvitation", "roles": [{"roleId": "User", "resource": "deployment/612f153c1a297695e4506d53", "isActive": true}], "email": "<EMAIL>", "code": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************.6M22glMJAavCoeHGf8CEDOWyn9SBNyITxQot7PpaHZn", "type": "PERSONAL", "expiresAt": {"$date": "2031-04-21T20:23:08.136Z"}, "createDateTime": {"$date": "2024-02-21T20:23:08.136Z"}, "deferredLink": "test_link_1"}]}