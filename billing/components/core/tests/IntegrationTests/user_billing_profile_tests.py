import io
from pathlib import Path
from unittest.mock import patch

from flask import url_for
from jwt import PyJWS

from billing.components.core.dtos.deployment_billing import (
    BILLING_FEATURES_KEY,
)
from billing.components.core.dtos.user_billing import (
    UserBilling,
    UserBillingFile,
)
from billing.tests.test_helpers import (
    sample_user_billing,
    sample_user_billing_not_enrolled,
)
from huma_plugins.components.export.tests.IntegrationTests import enabled_modules
from huma_plugins.components.extended_module_result.component import (
    ExtendedModuleResultComponent,
)
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.authorization.dtos.user import UserDTO
from sdk.authorization.models import User
from sdk.common.exceptions.exceptions import (
    FileNotFoundException,
    InvalidRequestException,
)
from sdk.common.utils.convertible import ConvertibleClassValidationError
from sdk.common.utils.validators import model_to_dict
from sdk.deployment.component import DeploymentComponent
from sdk.organization.component import OrganizationComponent
from sdk.storage.component import StorageComponentV1
from sdk.tests.extension_test_case import ExtensionTestCase

USER_ID = "5e8f0c74b50aa9656c34789b"
MANAGER_ID = "64e76adedf047d493ba356c3"
DEPLOYMENT_USER_CODE = "53924415"
VALID_PDF_FILE_SAMPLE = "sample_pdf.pdf"
INVALID_FILE_FORMAT_FILE = "sample_csv.csv"
INVALID_FILE_ID = "asdf123"
NOT_EXISTS_FILE_ID = "62274d2aabcc8fedbca3c7a7"
TEST_EMAIL = "<EMAIL>"
INVITATION_CODE = (
    "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2MTYwNzAwMjQsIm5iZiI6MTYxNjA3MDAyNCwia"
    "nRpIjoiYjliMDRiYzQtNmFiZi00MzkwLWI0MjUtYTM1YTc1NjgyNWQ4IiwiaWRlbnRpdHkiOiJ1c2VyMUBleGFtcGxlLm"
    "NvbSIsInR5cGUiOiJpbnZpdGF0aW9uIn0.6M22glMJAavCoeHGf8CEDOWyn9SBNyITxQot7PpaHZn"
)


class BillingUserProfileTestCase(ExtensionTestCase):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        StorageComponentV1(),
        DeploymentComponent(),
        OrganizationComponent(),
        ExtendedModuleResultComponent(additional_modules=[m() for m in enabled_modules]),
    ]
    fixtures = [Path(__file__).parent.joinpath("fixtures/users_dump.json")]

    def setUp(self):
        super().setUp()
        self.profiles_url = url_for("user_v1.retrieve_user_profiles_v1")
        self.upload_url = url_for("storage_v1.upload_file_v1")
        self.auth_url = "/api/auth/v1"
        self.headers = self.get_headers_for_token(MANAGER_ID)

    def upload_test_file(self, user_id, file_name):
        data = {
            "file": (io.BytesIO(self.content_test_file(file_name)), file_name),
        }
        rsp = self.flask_client.post(
            self.upload_url,
            data=data,
            headers=self.get_headers_for_token(identity=user_id),
            content_type="multipart/form-data",
        )
        return rsp.json["id"]

    @staticmethod
    def content_test_file(file_name):
        with open(Path(__file__).parent.joinpath(f"fixtures/{file_name}"), "rb") as file:
            return file.read()

    @patch.object(PyJWS, "_verify_signature")
    def test_create_user_with_billing_info(self, _):
        file_id = self.upload_test_file(USER_ID, VALID_PDF_FILE_SAMPLE)
        sample_name = "user1"
        billing_data = sample_user_billing()
        billing_data[UserBilling.FILE] = {
            UserBillingFile.FILE_ID: file_id,
            UserBillingFile.NAME: VALID_PDF_FILE_SAMPLE,
        }
        billing_data.pop(UserBilling.CREATE_DT)
        validation_data = {"invitationCode": INVITATION_CODE}
        user_data = {
            "method": 0,
            "email": TEST_EMAIL,
            "displayName": sample_name,
            "validationData": validation_data,
            "userAttributes": {
                UserDTO.FAMILY_NAME: sample_name,
                UserDTO.GIVEN_NAME: sample_name,
                UserDTO.COMPONENTS_DATA: {BILLING_FEATURES_KEY: billing_data},
            },
            "clientId": "ctest1",
            "projectId": "ptest1",
        }
        rsp = self.flask_client.post(f"{self.auth_url}/signup", json=user_data)
        user_id = rsp.json["uid"]

        db_user = model_to_dict(User.objects.filter(mongoId=user_id).first())
        db_billing_data = db_user[UserDTO.COMPONENTS_DATA][BILLING_FEATURES_KEY]
        self.assertEqual(db_billing_data, billing_data)

    def test_success_create_user_billing(self):
        file_id = self.upload_test_file(USER_ID, VALID_PDF_FILE_SAMPLE)
        data = sample_user_billing()
        data.pop(UserBilling.CREATE_DT)
        data[UserBilling.FILE] = {
            UserBillingFile.FILE_ID: file_id,
            UserBillingFile.NAME: VALID_PDF_FILE_SAMPLE,
        }
        try:
            UserBilling.from_dict(data)
        except ConvertibleClassValidationError:
            self.fail()

    def test_user_not_billing_enrolled_data(self):
        file_id = self.upload_test_file(USER_ID, VALID_PDF_FILE_SAMPLE)
        data = sample_user_billing_not_enrolled()
        data[UserBilling.FILE] = {
            UserBillingFile.FILE_ID: file_id,
            UserBillingFile.NAME: VALID_PDF_FILE_SAMPLE,
        }
        try:
            UserBilling.from_dict(data)
        except ConvertibleClassValidationError:
            self.fail()

    def test_failure_create_user_billing_with_non_pdf_format_file(self):
        file_id = self.upload_test_file(USER_ID, INVALID_FILE_FORMAT_FILE)
        data = sample_user_billing()
        data.pop(UserBilling.CREATE_DT)
        data[UserBilling.FILE] = {
            UserBillingFile.FILE_ID: file_id,
            UserBillingFile.NAME: INVALID_FILE_FORMAT_FILE,
        }
        with self.assertRaises(InvalidRequestException) as cm:
            UserBilling.from_dict(data)
        exception = cm.exception
        self.assertEqual(str(exception), "Only PDF file are acceptable for now")

    def test_failure_create_user_billing_with_invalid_file_id(self):
        data = sample_user_billing()
        data.pop(UserBilling.CREATE_DT)
        data[UserBilling.FILE] = {
            UserBillingFile.FILE_ID: INVALID_FILE_ID,
            UserBillingFile.NAME: VALID_PDF_FILE_SAMPLE,
        }
        with self.assertRaises(InvalidRequestException) as cm:
            UserBilling.from_dict(data)
        exception = cm.exception
        self.assertEqual(str(exception), "File Id is not valid")

    def test_failure_create_user_billing_with_valid_file_id_but_not_exists(self):
        data = sample_user_billing()
        data.pop(UserBilling.CREATE_DT)
        data[UserBilling.FILE] = {
            UserBillingFile.FILE_ID: NOT_EXISTS_FILE_ID,
            UserBillingFile.NAME: VALID_PDF_FILE_SAMPLE,
        }
        with self.assertRaises(FileNotFoundException) as cm:
            UserBilling.from_dict(data)
        exception = cm.exception
        self.assertEqual(str(exception), "File Not Found")

    def test_failure_create_user_billing_with_empty_file_name(self):
        file_id = self.upload_test_file(USER_ID, VALID_PDF_FILE_SAMPLE)
        data = sample_user_billing()
        data.pop(UserBilling.CREATE_DT)
        data[UserBilling.FILE] = {
            UserBillingFile.FILE_ID: file_id,
            UserBillingFile.NAME: "",
        }

        with self.assertRaises(InvalidRequestException) as cm:
            UserBilling.from_dict(data)
        exception = cm.exception
        self.assertEqual(str(exception), "File name should be at least 1 and at most 256 characters")

    def test_success_retrieve_profiles_by_billing_status(self):
        body = {"filters": {"componentsData": {"billing.status": 0}}}
        rsp = self._search_profiles(body)
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(1, len(rsp.json["users"]))

        body = {"filters": {"componentsData": {"billing.status": 1}}}
        rsp = self._search_profiles(body)
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(1, len(rsp.json["users"]))
        for res in rsp.json["users"]:
            self.assertIn(UserDTO.COMPONENTS_DATA, res)

    def test_failure_retrieve_profiles_with_non_eligible_character(
        self,
    ):
        body = {"filters": {"componentsData": {"billing.status": {"$eq": 1}}}}
        rsp = self._search_profiles(body)
        self.assertEqual(400, rsp.status_code)

    def _search_profiles(self, body):
        return self.flask_client.post(self.profiles_url, json=body, headers=self.headers)
