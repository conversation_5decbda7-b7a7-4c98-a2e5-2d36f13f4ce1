import io
import unittest
from unittest.mock import MagicMock, patch

from billing.components.core.callbacks.deployment_callbacks import (
    process_billing_calculation_type_change,
)
from billing.components.core.dtos.deployment_billing import (
    Color,
    ColorHex,
    ColorNames,
    DeploymentBillingConfig,
    DeploymentBillingFileType,
    DeploymentBillingFiles,
)
from billing.components.core.repository.billing_repository import (
    BillingAlertsRepository,
)
from billing.components.core.validators import validate_deployment_billing_file
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.utils import inject
from sdk.common.utils.convertible import ConvertibleClassValidationError
from sdk.organization.dtos.organization import ViewType

VALIDATORS_PATH = "billing.components.core.validators"
SAMPLE_FILE_ID = "5d386cc6ff885918d96edb2c"


class DeploymentBillingTestCase(unittest.TestCase):
    def _sample_deployment_billing(self) -> dict:
        return {
            DeploymentBillingConfig.ENABLED: True,
            DeploymentBillingConfig.PRODUCT_TYPE: ViewType.RPM.value,
            DeploymentBillingConfig.FILES: [self._sample_billing_file(f"FileName{f}") for f in range(3)],
            DeploymentBillingConfig.USE_CALENDAR_CALCULATION: False,
        }

    def _sample_deployment_billing_with_3_colors_instead_of_5(self):
        return {
            DeploymentBillingConfig.ENABLED: True,
            DeploymentBillingConfig.PRODUCT_TYPE: ViewType.RPM.value,
            DeploymentBillingConfig.FILES: [self._sample_billing_file(f"FileName{f}") for f in range(3)],
            DeploymentBillingConfig.COLORS: [
                Color.to_dict(Color(color=ColorHex.WHITE, name=ColorNames.WHITE)),
                Color.to_dict(Color(color=ColorHex.GREY, name=ColorNames.GREY)),
                Color.to_dict(Color(color=ColorHex.RED, name=ColorNames.RED)),
            ],
        }

    @staticmethod
    def _sample_billing_file(filename: str = "FileName") -> dict:
        return {
            DeploymentBillingFiles.TYPE: DeploymentBillingFileType.INSURANCE_CARRIERS.value,
            DeploymentBillingFiles.NAME: filename,
            DeploymentBillingFiles.FILE_ID: SAMPLE_FILE_ID,
            DeploymentBillingFiles.UPLOAD_DATETIME: "2020-08-19T10:00:00.000Z",
        }


class DeploymentBillingModelTestCase(DeploymentBillingTestCase):
    def test_success_create_deployment_billing_obj(self):
        billing_dict = {
            **self._sample_deployment_billing(),
            DeploymentBillingConfig.MANUAL_APPOINTMENT: True,
        }
        try:
            res = DeploymentBillingConfig.from_dict(billing_dict)
        except ConvertibleClassValidationError:
            self.fail()

        self.assertEqual(True, res.manualAppointment)

    def test_manual_appointment_is_defaulted(self):
        billing_dict = self._sample_deployment_billing()
        res = DeploymentBillingConfig.from_dict(billing_dict)
        self.assertEqual(False, res.manualAppointment)

    def test_colors_is_defaulted(self):
        billing_dict = self._sample_deployment_billing()
        res = DeploymentBillingConfig.from_dict(billing_dict)
        self.assertEqual(len(res.colors), 5)

    def test_colors_is_defaulted_with_3_colors_instead_of_5(self):
        billing_dict = self._sample_deployment_billing_with_3_colors_instead_of_5()
        res = DeploymentBillingConfig.from_dict(billing_dict)
        self.assertEqual(len(res.colors), 5)

    def test_failure_missing_required_keys(self):
        keys_to_pop = [
            DeploymentBillingConfig.PRODUCT_TYPE,
            DeploymentBillingConfig.FILES,
        ]
        for key in keys_to_pop:
            billing_dict = self._sample_deployment_billing()
            billing_dict.pop(key)
            with self.assertRaises(ConvertibleClassValidationError):
                DeploymentBillingConfig.from_dict(billing_dict)

    def test_success_color_creation(self):
        Color.from_dict({Color.COLOR: ColorHex.WHITE, Color.NAME: ColorNames.WHITE})
        Color.from_dict({Color.COLOR: ColorHex.RED, Color.NAME: ColorNames.RED})

    def test_failure_wrong_colors(self):
        with self.assertRaises(ConvertibleClassValidationError):
            Color.from_dict({Color.COLOR: "wrongColor", Color.NAME: ColorNames.WHITE})

    def test_failure_wrong_color_name(self):
        with self.assertRaises(ConvertibleClassValidationError):
            Color.from_dict({Color.COLOR: ColorHex.WHITE, Color.NAME: "wrongname"})

    def test_failure_pass_less_3_files(self):
        billing_dict = {
            **self._sample_deployment_billing(),
            DeploymentBillingConfig.FILES: [self._sample_billing_file()],
        }
        with self.assertRaises(ConvertibleClassValidationError):
            DeploymentBillingConfig.from_dict(billing_dict)

    def test_failure_wrong_product_type(self):
        billing_dict = {
            **self._sample_deployment_billing(),
            DeploymentBillingConfig.PRODUCT_TYPE: "something wrong",
        }
        with self.assertRaises(ConvertibleClassValidationError):
            DeploymentBillingConfig.from_dict(billing_dict)


class DeploymentBillingFilesModelTestCase(DeploymentBillingTestCase):
    def test_success_create_billing_file(self):
        file_dict = self._sample_billing_file()
        try:
            DeploymentBillingFiles.from_dict(file_dict)
        except ConvertibleClassValidationError:
            self.fail()

    def test_failure_required_field_is_missing(self):
        required_fields = [
            DeploymentBillingFiles.TYPE,
            DeploymentBillingFiles.NAME,
            DeploymentBillingFiles.FILE_ID,
            DeploymentBillingFiles.UPLOAD_DATETIME,
        ]
        for field in required_fields:
            file_dict = self._sample_billing_file()
            file_dict.pop(field)
            with self.assertRaises(ConvertibleClassValidationError):
                DeploymentBillingFiles.from_dict(file_dict)


class DeploymentBillingValidatorsTestCase(DeploymentBillingTestCase):
    @patch(f"{VALIDATORS_PATH}.FileDownload")
    def test_success_validate_billing_file(self, mock_file_download):
        file = mock_file_download.return_value
        file.name = "file.csv"
        file.size = 1024
        file.content = io.BytesIO(b"Group name,Payer ID\nTest Group,123456")
        validate_deployment_billing_file(DeploymentBillingFileType.INSURANCE_CARRIERS, SAMPLE_FILE_ID)

    @patch(f"{VALIDATORS_PATH}.FileDownload")
    def test_fail_billing_big_file_size(self, mock_file_download):
        file = mock_file_download.return_value
        file.name = "file.csv"
        file.size = 1024 * 1024 * 100
        file.content = io.BytesIO(b"Group name,Payer ID\nTest Group,123456")
        with self.assertRaises(InvalidRequestException):
            validate_deployment_billing_file(DeploymentBillingFileType.INSURANCE_CARRIERS, SAMPLE_FILE_ID)

    @patch(f"{VALIDATORS_PATH}.FileDownload")
    def test_missing_billing_provider_id(self, mock_file_download):
        file = mock_file_download.return_value
        file.name = "file.csv"
        file.size = 1024
        file.content = io.BytesIO(b"Name\nTest Name")
        try:
            validate_deployment_billing_file(DeploymentBillingFileType.BILLING_PROVIDERS, SAMPLE_FILE_ID)
        except InvalidRequestException:
            self.fail()


class DeploymentBillingCalculationTypeTestCase(DeploymentBillingTestCase):
    def setUp(self):
        self.deployment_id = "5d386cc6ff885918d96edb2c"
        self.current_billing_dict = self._sample_deployment_billing()
        self.previous_billing_dict = self._sample_deployment_billing()
        self.alert_repo = MagicMock()

        def bind_and_configure(binder):
            binder.bind(BillingAlertsRepository, self.alert_repo)

        inject.clear_and_configure(bind_and_configure)

    def test_set_calendar_calculation_for_non_billing_deployment(self):
        self.current_billing_dict[DeploymentBillingConfig.USE_CALENDAR_CALCULATION] = True

        process_billing_calculation_type_change(self.deployment_id, None, self.current_billing_dict)

        self.alert_repo.fix_submission_dates_for_deployment_users.assert_called_once()

    def test_set_30_day_calculation_for_non_billing_deployment(self):
        self.current_billing_dict[DeploymentBillingConfig.USE_CALENDAR_CALCULATION] = False

        process_billing_calculation_type_change(self.deployment_id, None, self.current_billing_dict)

        self.alert_repo.fix_submission_dates_for_deployment_users.assert_not_called()

    def test_set_30_day_calculation_for_billing_deployment(self):
        self.current_billing_dict[DeploymentBillingConfig.USE_CALENDAR_CALCULATION] = False

        process_billing_calculation_type_change(
            self.deployment_id, self.previous_billing_dict, self.current_billing_dict
        )

        self.alert_repo.fix_submission_dates_for_deployment_users.assert_not_called()

    def test_change_30_day_to_calendar_calculation(self):
        self.current_billing_dict[DeploymentBillingConfig.USE_CALENDAR_CALCULATION] = True

        process_billing_calculation_type_change(
            self.deployment_id, self.previous_billing_dict, self.current_billing_dict
        )

        self.alert_repo.fix_submission_dates_for_deployment_users.assert_called_once()

    def test_change_calendar_30_day_calculation(self):
        self.previous_billing_dict[DeploymentBillingConfig.USE_CALENDAR_CALCULATION] = True

        with self.assertRaises(InvalidRequestException):
            process_billing_calculation_type_change(
                self.deployment_id,
                self.previous_billing_dict,
                self.current_billing_dict,
            )


if __name__ == "__main__":
    unittest.main()
