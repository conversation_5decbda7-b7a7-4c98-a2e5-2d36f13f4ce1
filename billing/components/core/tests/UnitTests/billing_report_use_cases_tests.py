from datetime import datetime
from unittest import mock, TestCase
from unittest.mock import patch

from freezegun import freeze_time
from parameterized import parameterized

from billing.components.core.helpers.calculate_cumulative_helpers import (
    get_status_for_cpt_mapping_4_2x,
    get_status_for_cpt_mapping_4_1x,
)
from billing.components.core.router.billing_report_requests import (
    CPTMappingNumber,
)
from billing.components.core.router.billing_response_objects import (
    CPTRecordStatusType,
    CPTCode,
    CPTReportResponseObject,
)
from billing.components.core.services.submission.submission_rule import SubmissionRule
from billing.components.core.use_case.billing_report_use_cases import (
    ReportBillingRecordsForCPTUseCase,
)


class ReportBillingRecordsForCPTUseCaseTest(TestCase):
    def setUp(self) -> None:
        self.use_case = ReportBillingRecordsForCPTUseCase(mock.Mock(), mock.Mock())

    @parameterized.expand(
        [
            (12, 3, True, CPTRecordStatusType.IN_PROGRESS),
            (12, 3, True, CPTRecordStatusType.IN_PROGRESS),
            (12, 0, True, CPTRecordStatusType.PENDING),
            (15, 0, True, CPTRecordStatusType.INCOMPLETE),
            (14, 0, True, CPTRecordStatusType.PENDING),
            (14, 1, True, CPTRecordStatusType.IN_PROGRESS),
            (16, 1, True, CPTRecordStatusType.INCOMPLETE),
            (29, 15, True, CPTRecordStatusType.IN_PROGRESS),
            (29, 20, True, CPTRecordStatusType.COMPLETED),
            (29, 20, False, CPTRecordStatusType.NON_BILLABLE),
            (14, 1, False, CPTRecordStatusType.NON_BILLABLE),
        ]
    )
    @mock.patch.object(SubmissionRule, "_is_period_billable")
    def test_find_out_billing_status_for_current_billing(
        self,
        days_passed,
        days_have_submission,
        is_billable,
        expected_result,
        mocked_billable_func,
    ):
        mocked_billable_func.return_value = is_billable

        self.assertEqual(
            expected_result,
            SubmissionRule().evaluate_rolling_period_compliance(days_have_submission, days_passed, is_billable),
        )

    @parameterized.expand(
        [
            (
                datetime(2022, 6, 12, 0, 0).date(),
                datetime(2022, 6, 12, 0, 0).date(),
                18,
            ),
            (
                datetime(2022, 6, 29, 0, 0).date(),
                datetime(2022, 6, 29, 0, 0).date(),
                1,
            ),
            (
                datetime(2022, 5, 31, 0, 0).date(),
                datetime(2022, 6, 30, 0, 0).date(),
                0,
            ),
            (
                datetime(2022, 6, 1, 0, 0).date(),
                datetime(2022, 6, 1, 0, 0).date(),
                29,
            ),
            (
                datetime(2022, 6, 30, 0, 0).date(),
                datetime(2022, 6, 30, 0, 0).date(),
                0,
            ),
        ]
    )
    def test_extract_current_billing_start_data_from_first_billing_start_date(
        self, start_date, expected_billing_start_date, expected_billing_days
    ):
        with freeze_time("2022-06-30T10:00:00.000Z"):
            self.assertEqual(
                expected_billing_days,
                self.use_case._total_days_passed_in_current_billing(start_date),
            )
            self.assertEqual(
                expected_billing_start_date,
                self.use_case._current_billing_start_date(expected_billing_days),
            )

    @patch.object(ReportBillingRecordsForCPTUseCase, "cpt_code_type_cls", CPTCode.RPM)
    def test__get_response_obj_without_calculation_FOR_cpt_1(self):
        result = self.use_case._get_response_obj_without_calculation(CPTMappingNumber.CPT_99453_98975)

        self.assertEqual(CPTRecordStatusType.PENDING, result.get(CPTReportResponseObject.STATUS))
        self.assertEqual(
            CPTCode.RPM.CPT_MAPPING_1.value,
            result.get(CPTReportResponseObject.CPT_CODE),
        )

    @patch.object(ReportBillingRecordsForCPTUseCase, "cpt_code_type_cls", CPTCode.RTM)
    def test__get_response_obj_without_calculation_FOR_cpt_2(self):
        result = self.use_case._get_response_obj_without_calculation(CPTMappingNumber.CPT_99454_98976)

        self.assertEqual(CPTRecordStatusType.PENDING, result.get(CPTReportResponseObject.STATUS))
        self.assertEqual(
            CPTCode.RTM.CPT_MAPPING_2.value,
            result.get(CPTReportResponseObject.CPT_CODE),
        )

    @parameterized.expand(
        [
            (0, CPTRecordStatusType.PENDING, CPTRecordStatusType.PENDING),
            (500, CPTRecordStatusType.PENDING, CPTRecordStatusType.PENDING),
            (1000, CPTRecordStatusType.IN_PROGRESS, CPTRecordStatusType.PENDING),
            (1200, CPTRecordStatusType.COMPLETED, CPTRecordStatusType.IN_PROGRESS),
            (2400, CPTRecordStatusType.COMPLETED, CPTRecordStatusType.COMPLETED),
            (8000, CPTRecordStatusType.PENDING, CPTRecordStatusType.PENDING),
        ]
    )
    def test_find_out_cpt_status_for_1x(self, time_spent, status_for_cpt_mapping_3, expected_status):
        self.assertEqual(
            expected_status,
            get_status_for_cpt_mapping_4_1x(time_spent, status_for_cpt_mapping_3),
        )

    @parameterized.expand(
        [
            (0, CPTRecordStatusType.PENDING, CPTRecordStatusType.PENDING),
            (1000, CPTRecordStatusType.IN_PROGRESS, CPTRecordStatusType.PENDING),
            (1200, CPTRecordStatusType.COMPLETED, CPTRecordStatusType.PENDING),
            (2400, CPTRecordStatusType.COMPLETED, CPTRecordStatusType.IN_PROGRESS),
            (3600, CPTRecordStatusType.COMPLETED, CPTRecordStatusType.COMPLETED),
            (8000, CPTRecordStatusType.PENDING, CPTRecordStatusType.PENDING),
        ]
    )
    def test_find_out_cpt_status_for_2x(self, time_spent, status_for_cpt_mapping_3, expected_status):
        self.assertEqual(
            expected_status,
            get_status_for_cpt_mapping_4_2x(time_spent, status_for_cpt_mapping_3),
        )
