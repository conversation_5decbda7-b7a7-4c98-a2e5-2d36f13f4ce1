from enum import Enum


class CPTTimeThresholds:
    """Time thresholds for CPT codes in seconds"""
    
    TWENTY_MINUTES = 20 * 60  # 1200 seconds
    FORTY_MINUTES = 40 * 60   # 2400 seconds
    SIXTY_MINUTES = 60 * 60   # 3600 seconds


class CPTSubmissionThresholds:
    """Submission thresholds for CPT codes"""
    
    REQUIRED_SUBMISSION_DAYS = 16
    MINIMUM_COMPLIANCE_DAYS = 15


class CPTCodeTypes(Enum):
    """CPT code types and their mapping"""
    
    # RPM codes
    CPT_99453 = "99453"
    CPT_99454 = "99454" 
    CPT_99457 = "99457"
    CPT_99458 = "99458"
    
    # RTM codes
    CPT_98975 = "98975"
    CPT_98976 = "98976"
    CPT_98980 = "98980"
    CPT_98981 = "98981"


class CPTRuleTypes(Enum):
    """Types of CPT rules"""
    
    SUBMISSION_BASED = "SUBMISSION_BASED"
    TIME_BASED = "TIME_BASED"
    VIDEO_CALL_BASED = "VIDEO_CALL_BASED"
    DEPENDENCY_BASED = "DEPENDENCY_BASED"


class BillingPeriodTypes(Enum):
    """Types of billing periods"""
    
    STANDARD = "STANDARD"      # 30-day periods
    CALENDAR = "CALENDAR"      # Calendar month periods
    WINTER = "WINTER"          # Special winter period handling


class CPTCalculationModes(Enum):
    """CPT calculation modes"""
    
    CLASSIC = "CLASSIC"        # Original 30-day calculation
    CALENDAR = "CALENDAR"      # Calendar-based calculation