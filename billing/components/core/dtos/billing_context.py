from datetime import date, datetime
from typing import Optional

from billing.components.core.dtos.billing_period import BillingPeriod
from billing.components.core.dtos.deployment_billing import DeploymentBillingConfig
from billing.components.core.router.billing_report_requests import CPTMappingNumber
from sdk import convertibleclass
from sdk.authorization.dtos.user import UserDTO


@convertibleclass
class BillingCalculationContext:
    """Context object containing all data needed for CPT calculations"""

    USER = "user"
    DEPLOYMENT_CONFIG = "deploymentConfig"
    BILLING_PERIOD = "billingPeriod"
    CALCULATION_DATE = "calculationDate"
    CPT_MAPPING = "cptMapping"
    IS_FIRST_PERIOD = "isFirstPeriod"

    # Core data
    user: UserDTO
    deploymentConfig: DeploymentBillingConfig
    billingPeriod: BillingPeriod
    calculationDate: datetime
    cptMapping: CPTMappingNumber
    isFirstPeriod: bool = False

    # Cached data (populated by data services)
    _submission_days_count: Optional[int] = None
    _submission_count: Optional[int] = None
    _time_spent_seconds: Optional[float] = None
    _has_answered_calls: Optional[bool] = None
    _first_submission_date: Optional[date] = None
    _first_call_date: Optional[datetime] = None

    @property
    def user_id(self) -> str:
        return self.user.id

    @property
    def deployment_id(self) -> str:
        return self.user.deployment

    @property
    def period_start_date(self) -> date:
        return self.billingPeriod.startDate

    @property
    def period_end_date(self) -> date:
        return self.billingPeriod.endDate

    @property
    def period_start_datetime(self) -> datetime:
        return datetime.combine(self.period_start_date, datetime.min.time())

    @property
    def period_end_datetime(self) -> datetime:
        return datetime.combine(self.period_end_date, datetime.max.time())

    @property
    def days_in_period(self) -> int:
        return self.billingPeriod.daysInPeriod

    @property
    def days_elapsed(self) -> int:
        return self.billingPeriod.get_elapsed_days(self.calculationDate.date())

    @property
    def days_remaining(self) -> int:
        return self.billingPeriod.get_remaining_days(self.calculationDate.date())

    @property
    def use_calendar_calculation(self) -> bool:
        return self.deploymentConfig.useCalendarCalculation

    @property
    def product_type(self) -> str:
        return self.deploymentConfig.productType.value

    # Cached data accessors
    def get_submission_days_count(self) -> Optional[int]:
        return self._submission_days_count

    def set_submission_days_count(self, count: int) -> None:
        self._submission_days_count = count

    def get_submission_count(self) -> Optional[int]:
        return self._submission_count

    def set_submission_count(self, count: int) -> None:
        self._submission_count = count

    def get_time_spent_seconds(self) -> Optional[float]:
        return self._time_spent_seconds

    def set_time_spent_seconds(self, seconds: float) -> None:
        self._time_spent_seconds = seconds

    def get_has_answered_calls(self) -> Optional[bool]:
        return self._has_answered_calls

    def set_has_answered_calls(self, has_calls: bool) -> None:
        self._has_answered_calls = has_calls

    def get_first_submission_date(self) -> Optional[date]:
        return self._first_submission_date

    def set_first_submission_date(self, date: date) -> None:
        self._first_submission_date = date

    def get_first_call_date(self) -> Optional[datetime]:
        return self._first_call_date

    def set_first_call_date(self, date: datetime) -> None:
        self._first_call_date = date
