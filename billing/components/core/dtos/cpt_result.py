from datetime import date
from typing import Optional

from billing.components.core.router.billing_report_requests import CPTMappingNumber
from billing.components.core.router.billing_response_objects import (
    CPTRecordStatusType,
    CPTCodeIterationNumber,
)
from sdk import convertibleclass


@convertibleclass
class CPTResult:
    """Result object for CPT calculations"""

    CPT_CODE = "cptCode"
    CPT_MAPPING = "cptMapping"
    STATUS = "status"
    START_DATE = "startDate"
    END_DATE = "endDate"
    EARLIEST_BILLING_DATE = "earliestBillingDate"
    DAYS_HAVE_SUBMISSION = "daysHaveSubmission"
    TIME_SPENT = "timeSpent"
    CPT_CODE_ITERATION = "cptCodeIteration"
    ORDER = "order"

    cptCode: str
    cptMapping: CPTMappingNumber
    status: CPTRecordStatusType
    startDate: date
    endDate: date
    earliestBillingDate: Optional[date] = None
    daysHaveSubmission: Optional[int] = None
    timeSpent: Optional[float] = None
    cptCodeIteration: Optional[CPTCodeIterationNumber] = None
    order: Optional[int] = None

    def is_billable(self) -> bool:
        """Check if this CPT result is billable"""
        return self.status == CPTRecordStatusType.COMPLETED

    def is_in_progress(self) -> bool:
        """Check if this CPT result is in progress"""
        return self.status == CPTRecordStatusType.IN_PROGRESS

    def is_pending(self) -> bool:
        """Check if this CPT result is pending"""
        return self.status == CPTRecordStatusType.PENDING

    def is_incomplete(self) -> bool:
        """Check if this CPT result is incomplete"""
        return self.status == CPTRecordStatusType.INCOMPLETE

    def is_non_billable(self) -> bool:
        """Check if this CPT result is non-billable"""
        return self.status == CPTRecordStatusType.NON_BILLABLE

    def can_be_billed_on_date(self, target_date: date) -> bool:
        """Check if this CPT can be billed on the target date"""
        if not self.is_billable():
            return False
        if self.earliestBillingDate is None:
            return True
        return target_date >= self.earliestBillingDate

    def get_billing_period_days(self) -> int:
        """Get number of days in billing period"""
        return (self.endDate - self.startDate).days + 1

    def to_response_dict(self) -> dict:
        """Convert to response dictionary format"""
        result = {
            self.CPT_CODE: self.cptCode,
            self.STATUS: self.status,
            self.START_DATE: self.startDate,
            self.END_DATE: self.endDate,
        }

        if self.earliestBillingDate is not None:
            result[self.EARLIEST_BILLING_DATE] = self.earliestBillingDate

        if self.daysHaveSubmission is not None:
            result[self.DAYS_HAVE_SUBMISSION] = self.daysHaveSubmission

        if self.timeSpent is not None:
            result[self.TIME_SPENT] = self.timeSpent

        if self.cptCodeIteration is not None:
            result[self.CPT_CODE_ITERATION] = self.cptCodeIteration

        if self.order is not None:
            result[self.ORDER] = self.order

        return result

    def __str__(self) -> str:
        return f"CPTResult({self.cptCode}, {self.status}, {self.startDate}-{self.endDate})"
