from datetime import date
from typing import Optional

from sdk import convertibleclass


@convertibleclass
# @dataclass(frozen=True)
class BillingPeriod:
    """Value object representing a billing period"""

    START_DATE = "startDate"
    END_DATE = "endDate"
    PERIOD_TYPE = "periodType"
    IS_LEAP_YEAR = "isLeapYear"
    DAYS_IN_PERIOD = "daysInPeriod"
    IS_FIRST_PERIOD = "isFirstPeriod"
    DAYS_ELAPSED = "daysElapsed"

    startDate: date
    endDate: date
    periodType: str = "STANDARD"  # STANDARD, CALENDAR, WINTER
    isLeapYear: Optional[bool] = None
    daysInPeriod: Optional[int] = None
    daysElapsed: Optional[int] = None
    isFirstPeriod: bool = False

    def __post_init__(self):
        if self.daysInPeriod is None:
            object.__setattr__(self, "daysInPeriod", (self.endDate - self.startDate).days + 1)

    def contains_date(self, target_date: date) -> bool:
        """Check if target date is within this period"""
        return self.startDate <= target_date <= self.endDate

    def get_elapsed_days(self, reference_date: date) -> int:
        """Get number of days elapsed from start to reference date"""
        if reference_date < self.startDate:
            return 0
        if reference_date > self.endDate:
            return self.daysInPeriod
        return (reference_date - self.startDate).days

    def get_remaining_days(self, reference_date: date) -> int:
        """Get number of days remaining from reference date to end"""
        if reference_date < self.startDate:
            return self.daysInPeriod
        if reference_date > self.endDate:
            return 0
        return (self.endDate - reference_date).days

    def is_winter_period(self) -> bool:
        """Check if this is a winter period"""
        return self.periodType == "WINTER"

    def is_calendar_period(self) -> bool:
        """Check if this is a calendar period"""
        return self.periodType == "CALENDAR"

    def __str__(self) -> str:
        return f"BillingPeriod({self.startDate} to {self.endDate}, {self.daysInPeriod} days)"
