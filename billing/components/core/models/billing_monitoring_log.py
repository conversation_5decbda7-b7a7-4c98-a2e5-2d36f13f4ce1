from django.db import models


class BillingMonitoringLog(models.Model):
    class Meta:
        db_table = "billing_monitoring_log"
        indexes = [
            models.Index(fields=["userId", "createDateTime"]),
            models.Index(fields=["deploymentId"]),
            models.Index(fields=["createdById"]),
            models.Index(fields=["timeTrackingId"]),
            models.Index(fields=["startDateTime"]),
        ]
        app_label = "billing"

    MONGO_ID = "mongoId"
    ORIGINAL_LOG_ID = "originalLogId"

    id: str = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True)
    originalLogId = models.CharField(max_length=24, null=True, blank=True)
    deploymentId = models.CharField(max_length=24)
    createdById = models.CharField(max_length=24)
    lastModifiedById = models.CharField(max_length=24, null=True, blank=True)
    userId = models.CharField(max_length=24)
    timeTrackingId = models.CharField(max_length=24)
    status = models.IntegerField(null=True, blank=True)
    action = models.CharField(max_length=255)
    addendum = models.CharField(max_length=500, null=True, blank=True)
    timeSpent = models.FloatField(null=True, blank=True)
    startDateTime = models.DateTimeField()
    endDateTime = models.DateTimeField()
    initialCreateDateTime = models.DateTimeField()
    createDateTime = models.DateTimeField()
    updateDateTime = models.DateTimeField()
