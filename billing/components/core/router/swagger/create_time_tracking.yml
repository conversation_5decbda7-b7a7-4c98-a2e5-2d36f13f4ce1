Create time tracking
---
tags:
  - billing

security:
  - Bearer: []

parameters:
  - in: path
    name: user_id
    required: true
    type: string
  - name: body
    in: body
    description: body
    required: true
    schema:
      $ref: "#/definitions/CreateBillingRequest"
responses:
  201:
    description: created billing response
    schema:
      $ref: "#/definitions/CalculateCumulativeTimeTracking"

definitions:
  BaseBillingRequest:
    type: object
    properties:
      startDateTime:
        type: string
        format: date-time
      endDateTime:
        type: string
        format: date-time
  CreateBillingRequest:
    allOf:
      - $ref: "#/definitions/BaseBillingRequest"
      - required:
          - startDateTime
          - endDateTime
  CalculateCumulativeTimeTracking:
    type: object
    properties:
      cptCode:
        type: string
      cptCodeIteration:
        type: integer
      monitoringTimeSeconds:
        type: number
      status:
        type: string
      userId:
        type: string
