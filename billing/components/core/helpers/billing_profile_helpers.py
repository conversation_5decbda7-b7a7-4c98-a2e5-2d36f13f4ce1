from datetime import datetime
from typing import Optional

from billing.components.core.dtos.billing_models import (
    BillingDiagnosisHistoryLogDTO,
    BillingInsuranceCarrierProfileHistoryDTO,
    BillingProfileHistoryDTO,
    BillingProviderHistoryLogDTO,
)
from billing.components.core.dtos.deployment_billing import BILLING_FEATURES_KEY
from billing.components.core.dtos.user_billing import (
    Diagnosis,
    InsuranceCarrier,
    UserBilling,
)
from billing.components.core.repository.billing_repository import (
    BillingProfileHistoryLogRepository,
)
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.dtos.user import UserDTO
from sdk.authorization.services.authorization import AuthorizationService
from sdk.common.utils.inject import autoparams

BILLING_KEY = BILLING_FEATURES_KEY
DT_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"


def set_update_and_create_date_time_to_user_profile_data(
    user: UserD<PERSON>, previous_user_state: UserDTO, update_date_time: datetime
):
    if not user.componentsData.get(BILLING_KEY):
        return

    str_update_dt = _dt_2_str(update_date_time)
    billing_data = user.componentsData.get(BILLING_KEY)
    previous_billing_data = (
        previous_user_state.componentsData.get(BILLING_KEY, {}) if previous_user_state.componentsData else {}
    )
    _update_diagnosis_data(billing_data, previous_billing_data, str_update_dt)
    _update_carriers_data(billing_data, previous_billing_data, str_update_dt)
    _update_billing_provider(billing_data, previous_billing_data, str_update_dt)


def _dt_2_str(dt: datetime):
    return datetime.strftime(dt, DT_FORMAT)


def _update_diagnosis_data(current_billing_data: dict, previous_billing_data: dict, update_dt: str):
    if not _check_equal(
        Diagnosis,
        current_billing_data.get(UserBilling.DIAGNOSIS),
        previous_billing_data.get(UserBilling.DIAGNOSIS),
    ):
        new_diagnosis_data = {
            **(current_billing_data.get(UserBilling.DIAGNOSIS, {})),
            UserBilling.UPDATE_DT: update_dt,
        }

        if not previous_billing_data.get(UserBilling.DIAGNOSIS):
            new_diagnosis_data[UserBilling.CREATE_DT] = update_dt

        current_billing_data[UserBilling.DIAGNOSIS] = new_diagnosis_data

    elif previous_billing_data.get(UserBilling.DIAGNOSIS):
        current_billing_data[UserBilling.DIAGNOSIS] = previous_billing_data.get(UserBilling.DIAGNOSIS)


def _update_carriers_data(current_billing_data: dict, previous_billing_data: dict, update_dt: str):
    previous_carriers_data = previous_billing_data.get(UserBilling.INSURANCE_CARRIERS, [])
    for num, carrier_data in enumerate(current_billing_data.get(UserBilling.INSURANCE_CARRIERS, [])):
        previous_carrier_data = previous_carriers_data[num] if len(previous_carriers_data) > num else None
        if not _check_equal(InsuranceCarrier, carrier_data, previous_carrier_data):
            new_carrier_data = {**carrier_data, UserBilling.UPDATE_DT: update_dt}

            if not previous_carrier_data:
                new_carrier_data[UserBilling.CREATE_DT] = update_dt

            current_billing_data[UserBilling.INSURANCE_CARRIERS][num] = new_carrier_data
        elif previous_carrier_data:
            current_billing_data[UserBilling.INSURANCE_CARRIERS][num] = previous_carriers_data[num]


def _update_billing_provider(current_billing_data: dict, previous_billing_data: dict, update_dt: str):
    if current_billing_data.get(UserBilling.BILLING_PROVIDER_NAME) != previous_billing_data.get(
        UserBilling.BILLING_PROVIDER_NAME
    ):
        current_billing_data[UserBilling.UPDATE_DT] = update_dt

    elif pre_update_date := previous_billing_data.get(UserBilling.UPDATE_DT):
        current_billing_data[UserBilling.UPDATE_DT] = pre_update_date


def generate_user_billing_profile_history_logs(user: UserDTO, previous_user_state: UserDTO, update_date_time: datetime):
    if not user.componentsData.get(BILLING_KEY):
        return

    billing_data = user.componentsData.get(BILLING_KEY)
    previous_billing_data = (
        previous_user_state.componentsData.get(BILLING_KEY) if previous_user_state.componentsData else {}
    )

    _handle_user_billing_profile_diagnosis_logs(
        user,
        billing_data.get(UserBilling.DIAGNOSIS),
        previous_billing_data.get(UserBilling.DIAGNOSIS),
    )

    previous_carriers = previous_billing_data.get(UserBilling.INSURANCE_CARRIERS, [])

    for num, carrier in enumerate(billing_data.get(UserBilling.INSURANCE_CARRIERS, [])):
        handle_user_billing_profile_insurance_carrier_logs(
            user,
            carrier,
            previous_carriers[num] if len(previous_carriers) > num else {},
        )

    _handle_user_billing_profile_provider_logs(
        user,
        billing_data,
        previous_billing_data,
        update_date_time,
    )


@autoparams("billing_profile_repo")
def _handle_user_billing_profile_diagnosis_logs(
    user: UserDTO,
    current_data: dict,
    previous_data: dict,
    billing_profile_repo: BillingProfileHistoryLogRepository,
):
    if not _check_equal(Diagnosis, current_data, previous_data):
        billing_profile_repo.create_diagnosis_log(
            BillingDiagnosisHistoryLogDTO.from_dict(_clear_data_for_log(current_data, user))
        )


@autoparams("billing_profile_repo")
def handle_user_billing_profile_insurance_carrier_logs(
    user: UserDTO,
    current_data: dict,
    previous_data: dict,
    billing_profile_repo: BillingProfileHistoryLogRepository,
):
    if not _check_equal(InsuranceCarrier, current_data, previous_data):
        billing_profile_repo.create_insurance_log(
            BillingInsuranceCarrierProfileHistoryDTO.from_dict(_clear_data_for_log(current_data, user))
        )


@autoparams("billing_profile_repo")
def get_user_billing_provider_before_end_date(
    billing_profile_repo: BillingProfileHistoryLogRepository,
    user_id: str,
    to_date: datetime.date,
) -> list:
    billing_provider_logs = billing_profile_repo.get_billing_provider_logs(to_date=to_date, user_id=user_id)
    return billing_provider_logs


@autoparams("billing_profile_repo")
def get_diagnosis(
    billing_profile_repo: BillingProfileHistoryLogRepository,
    user_id: str,
    deployment_id: str,
):
    return billing_profile_repo.get_diagnosis(user_id=user_id, deployment_id=deployment_id)


@autoparams("billing_profile_repo")
def _handle_user_billing_profile_provider_logs(
    user: UserDTO,
    current_data: dict,
    previous_data: dict,
    update_date_time: datetime,
    billing_profile_repo: BillingProfileHistoryLogRepository,
):
    current_provider_name = current_data.get(UserBilling.BILLING_PROVIDER_NAME)
    previous_provider_name = previous_data.get(UserBilling.BILLING_PROVIDER_NAME)

    current_provider_id = current_data.get(UserBilling.BILLING_PROVIDER_ID)
    previous_provider_id = previous_data.get(UserBilling.BILLING_PROVIDER_ID)

    if current_provider_id != previous_provider_id or current_provider_name != previous_provider_name:
        billing_profile_repo.create_provider_log(
            BillingProviderHistoryLogDTO.from_dict(
                _clear_data_for_log(
                    {
                        BillingProviderHistoryLogDTO.BILLING_PROVIDER_ID: current_provider_id,
                        BillingProviderHistoryLogDTO.BILLING_PROVIDER_NAME: current_provider_name,
                        UserBilling.UPDATE_DT: update_date_time,
                    },
                    user,
                )
            )
        )


def _check_equal(checker_class, pre_data: dict, new_data: Optional[dict]):
    if (new_data or pre_data) is None:
        return True

    if (not new_data and pre_data) or (new_data and not pre_data) or not (new_data or pre_data):
        return False

    return checker_class.from_dict(pre_data).to_dict() == checker_class.from_dict(new_data).to_dict()


def _clear_data_for_log(profile_data: dict, user: UserDTO):
    authz_user = AuthorizedUser(AuthorizationService().retrieve_simple_user_profile(user.id))
    deployment_id = authz_user.deployment.id
    create_dt = profile_data.get(UserBilling.UPDATE_DT)

    if create_dt and isinstance(create_dt, str):
        create_dt = datetime.strptime(create_dt, DT_FORMAT)

    return {
        **profile_data,
        BillingProfileHistoryDTO.CREATE_DATETIME: create_dt,
        BillingProfileHistoryDTO.DEPLOYMENT_ID: deployment_id,
        BillingProfileHistoryDTO.USER_ID: user.id,
    }
