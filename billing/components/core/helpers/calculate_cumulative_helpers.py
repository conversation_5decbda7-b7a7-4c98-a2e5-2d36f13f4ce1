from datetime import datetime

from billing.components.core.router.billing_response_objects import (
    CPTRecordStatusType,
    MinutesInSeconds,
)
from huma_plugins.components.online_offline_call.dtos.video_models import (
    OfflineVideoCallDTO,
)
from huma_plugins.components.online_offline_call.repository.video_repository import (
    OnlineOfflineCallRepository,
)
from sdk.common.utils.inject import autoparams


def get_status_for_cpt_mapping_3(
    user_id: str, time_spent: float, start_date_time: datetime, end_date_time: datetime
) -> str:
    has_user_billable_calls = has_billable_calls(
        user_id=user_id,
        video_call_statuses=[OfflineVideoCallDTO.CallStatus.ANSWERED.value],
        from_date_time=start_date_time,
        to_date_time=end_date_time,
    )
    if not has_user_billable_calls:
        return CPTRecordStatusType.PENDING

    if time_spent < MinutesInSeconds.TWENTY_MINUTES_IN_SECONDS.value:
        return CPTRecordStatusType.IN_PROGRESS

    return CPTRecordStatusType.COMPLETED


@autoparams("video_repo")
def has_billable_calls(
    user_id: str,
    video_call_statuses: list[str],
    from_date_time: datetime,
    to_date_time: datetime,
    video_repo: OnlineOfflineCallRepository,
) -> bool:
    calls_count = video_repo.retrieve_user_calls_with_statuses(
        user_id=user_id,
        statuses=video_call_statuses,
        from_date_time=from_date_time,
        to_date_time=to_date_time,
        return_count=True,
    )
    return calls_count > 0


def get_status_for_cpt_mapping_4_1x(time_spent: float, status_cpt_mapping_3: str) -> str:
    if status_cpt_mapping_3 != CPTRecordStatusType.COMPLETED:
        return CPTRecordStatusType.PENDING

    if time_spent < MinutesInSeconds.FORTY_MINUTES_IN_SECONDS.value:
        return CPTRecordStatusType.IN_PROGRESS

    if time_spent >= MinutesInSeconds.FORTY_MINUTES_IN_SECONDS.value:
        return CPTRecordStatusType.COMPLETED


def get_status_for_cpt_mapping_4_2x(time_spent: float, status_cpt_mapping_3: str) -> str:
    if status_cpt_mapping_3 != CPTRecordStatusType.COMPLETED:
        return CPTRecordStatusType.PENDING

    if time_spent < MinutesInSeconds.FORTY_MINUTES_IN_SECONDS.value:
        return CPTRecordStatusType.PENDING
    if MinutesInSeconds.FORTY_MINUTES_IN_SECONDS.value <= time_spent < MinutesInSeconds.SIXTY_MINUTES_IN_SECONDS.value:
        return CPTRecordStatusType.IN_PROGRESS

    if time_spent >= MinutesInSeconds.SIXTY_MINUTES_IN_SECONDS.value:
        return CPTRecordStatusType.COMPLETED
