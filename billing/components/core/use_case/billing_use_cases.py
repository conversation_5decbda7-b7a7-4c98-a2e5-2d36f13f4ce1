import datetime
import logging

from dateutil import rrule

from billing.components.core.dtos.billing_models import BillingSubmissionDTO
from billing.components.core.dtos.billing_submission_schedule_event import (
    BillingSubmissionScheduleEvent,
)
from billing.components.core.dtos.deployment_billing import (
    BillingSubmissionReminderSchedule,
    DeploymentBillingConfig,
    ProductType,
)
from billing.components.core.repository.billing_repository import (
    BillingAlertsRepository,
    BillingRemoteTimeTrackingRepository,
    BillingSubmissionRepository,
)
from billing.components.core.router.billing_requests import (
    CreateBillingRequestObject,
    DeleteUserBillingRequestObject,
)
from sdk.authorization.repository import AuthorizationRepository
from sdk.calendar.dtos.calendar_event import CalendarEventDTO
from sdk.calendar.repo.calendar_repository import CalendarRepository
from sdk.calendar.service.calendar_service import CalendarService
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils import inject
from sdk.common.utils.inject import autoparams
from sdk.common.utils.validators import utc_str_field_to_val

logger = logging.getLogger(__name__)


class CreateBillingRemoteTimeTrackingUseCase(UseCase):
    @autoparams()
    def __init__(self, repo: BillingRemoteTimeTrackingRepository):
        self.repo = repo

    def process_request(self, request_object: CreateBillingRequestObject):
        return self.repo.create_billing_remote_time_tracking(
            deployment_id=request_object.user.deployment_id(),
            clinician_id=request_object.clinician.id,
            start_datetime=request_object.startDateTime,
            end_datetime=request_object.endDateTime,
            user_id=request_object.user.id,
        )


class DeleteBillingUseCase(UseCase):
    @autoparams()
    def __init__(
        self,
        billing_alert_repo: BillingAlertsRepository,
    ):
        self.billing_alert_repo = billing_alert_repo

    def process_request(self, request_object: DeleteUserBillingRequestObject):
        return self.billing_alert_repo.delete_user_data(user_id=request_object.userId)


class BillingSubmissionReminderProcessor:
    def __init__(self, submission: BillingSubmissionDTO, billing_config: DeploymentBillingConfig):
        self.submission = submission
        self.billing_config = billing_config
        self.calendar_service = CalendarService()

    def process(self):
        if not self.billing_config.submissionReminder or self.billing_config.productType is not ProductType.RPM:
            return

        user_id, start_dt = self.submission.userId, self.submission.startDateTimeUTC
        if self.user_has_submission_after_date(user_id, start_dt):
            logger.info(f"Skipping calendar event creation for user {user_id}")
            return

        self.delete_existing_submission_calendar_events(user_id)
        self.create_submission_calendar_events(user_id, start_dt)

    @staticmethod
    def user_has_submission_after_date(user_id: str, start_dt: datetime.datetime):
        submission_repo = inject.instance(BillingSubmissionRepository)
        return submission_repo.user_has_submission_after_date(user_id=user_id, reference_dt=start_dt)

    @autoparams("calendar_repo")
    def delete_existing_submission_calendar_events(self, user_id: str, calendar_repo: CalendarRepository):
        query = {
            CalendarEventDTO.USER_ID: user_id,
            CalendarEventDTO.MODEL: BillingSubmissionScheduleEvent.__name__,
        }
        deleted_events = calendar_repo.batch_delete_next_day_event_raw(query)
        logger.info(f"Deleted {deleted_events} billing submission reminder events for user {user_id}")

    def create_submission_calendar_events(self, user_id: str, start_dt: datetime.datetime):
        schedule = self.billing_config.submissionReminder.schedule
        event = self._build_submission_calendar_event(user_id, schedule, start_dt)
        repo = inject.instance(AuthorizationRepository)
        user_tz = repo.retrieve_simple_user_profile(user_id=user_id).timezone
        event_id = self.calendar_service.create_or_update_event(event, user_tz)
        logger.info(f"Created calendar event {event_id} for user {user_id}")

    @staticmethod
    def _build_submission_calendar_event(
        user_id: str,
        schedule: BillingSubmissionReminderSchedule,
        start_dt: datetime.datetime,
    ) -> CalendarEventDTO:
        hour, minute, offset = (
            schedule.byHour,
            schedule.byMinute,
            schedule.syncThreshold,
        )
        rrule_start = start_dt.replace(hour=hour, minute=minute) + datetime.timedelta(days=offset)
        rule = rrule.rrule(
            rrule.DAILY,
            dtstart=rrule_start,
            interval=schedule.interval,
            count=schedule.count,
            byhour=hour,
            byminute=minute,
        )
        start_dt = utc_str_field_to_val(start_dt)
        return BillingSubmissionScheduleEvent.from_dict(
            {
                CalendarEventDTO.MODEL: BillingSubmissionScheduleEvent.__name__,
                CalendarEventDTO.USER_ID: user_id,
                CalendarEventDTO.START_DATE_TIME: start_dt,
                CalendarEventDTO.IS_RECURRING: True,
                CalendarEventDTO.RECURRENCE_PATTERN: rule,
            }
        )
