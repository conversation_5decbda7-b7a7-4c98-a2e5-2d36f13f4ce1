# Project Name

A brief description of your project.

## Table of Contents

- [About](#about)
- [Installation](#installation)
- [Usage](#usage)
- [Plugins](#plugins)
  - [Components](#components)
  - [Widgets](#widgets)

## About

This project is a library of plugins that can be used in any Huma Server Platform (HSP)
-based project.
Plugins are extensions, that are based on
the [HumaSDK](https://github.com/huma-engineering/huma-server-sdk) and can be used to
add new functionalities to your project.

## Installation

To set up your environment and run the tests, follow these steps:

- Clone the
  repository: `git clone https://github.com/huma-engineering/huma-server-plugins`
- Setup virtualenv: `make setup`
- Run tests: `make plugins/tests/run`
- Run tests in docker: `plugins/tests/run/docker-compose`

## Usage

To use Huma Plugins you can add them to your HSP-based project in the `hsp-config.yaml`
file.

```yaml
repos:
  repo: https://github.com/huma-engineering/huma-server-plugins.git
  version: master
  components:
    - AutocompleteComponent
    - ExportComponent
  widgets:
    - FeedbackWidget
    - MedicationWidget
```

Make sure to include all plugin dependencies as well.
Run `python manage.py sync` every time you make changes to you `hsp-config.yaml` file.

### Development

For the new plugin, you can connect it to your HSP project to manually test/debug it
while it's not yet merged to master.
For this, you need to point your project to your new branch in the `hsp-config.yaml`

```yaml
repos:
  - repo: https://github.com/huma-engineering/huma-server-plugins.git
    version: feature/my-new-component
    components:
      - ReallyImportantComponent
```
Run `python manage.py sync` every time you make changes to you `hsp-config.yaml` file.

## Plugins

This repository contains a collection of plugins and widgets for enhancing your project.
Each plugin/widget is stored in a separate directory along with its documentation and
usage instructions.

### Components

Components are a way to add unlimited functionality to your project. They can be used to
add new features, integrate with third-party services, or enhance existing
functionalities. Here is a list of available components, but you can always add your
own:

- [Autocomplete](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/autocomplete):
  Adds input autocomplete functionality to your project. Can autocomplete medication
  names, symptoms and many more.
- [Export](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/export):
  Exporting user data to a CSV/JSON file.
- [Integration](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/integration):
  Integration with third-party services. Sends health data to supported services.
- [Kardia](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/kardia):
  Integration with [Kardia](https://kardia.com/) ECG devices.
- [Location](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/location):
  Location search by text or coordinates.
- [Medication](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/medication):
  Add and track your medications.
- [Natural Conditions](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/natural_conditions):
  Track Air Quality and Pollen levels in your area.
- [Phone Lookup](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/phone_lookup):
- Lookup phone numbers to identify their type (mobile/landline).
- [Privacy Assets](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/privacy_assets):
  Manage service privacy documents.
- [Reminder](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/reminder):
  Create and receive reminders for your daily tasks.
- [Revere](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/revere):
  Integration with [Revere](https://www.revere.ai/) Houndify service. Speach to text.
- [Template Bank](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/template_bank):
  Manage and use deployment templates for your project.
- [Third-party App](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/third_party_app):
  Integration with [Fitbit](https://www.fitbit.com/).
- [Twilio Video](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/components/twilio_video):
  Integration with [Twilio](https://www.twilio.com/) video service. Allows to make video
  calls.

### Widgets

Widget is a way to represent data in a mobile application. They can be used to display
health data, reminders, feedback, and more. Here is a list of available widgets:

- [External Connection](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/widgets/external_connection):
  Display reminder card to connect external services.
- [Feedback](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/widgets/feedback):
  Receive automated feedback for submitted health data.
- [Header](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/widgets/header):
  Add a header to application tab.
- [Health Coach](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/widgets/health_coach):
  In-app chat with clinician.
- [Learn](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/widgets/learn):
  Display educational content.
- [Medication](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/widgets/medication):
  Display medication information.
- [Reminder](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/widgets/reminder):
  Quick access to reminders.
- [Todo](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/widgets/todo):
  Display user's todo list.
- [Vitals](https://github.com/huma-engineering/huma-server-plugins/tree/master/huma_plugins/widgets/vitals):
  Display user's trends for all vitals modules.

Feel free to explore the plugins and widgets available and integrate them into your
project as needed.
