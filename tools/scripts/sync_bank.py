import os
import logging
import fire
import requests
import mimetypes
from pprint import pprint

logging.basicConfig(level=logging.DEBUG)

logger = logging.getLogger(__name__)


class SyncBankTemplates(object):
    def __init__(self, domain: str, email: str, password: str, path: str):
        self.email = email
        self.password = password
        self.path = path

        if not domain.startswith("http"):
            domain = "https://" + domain
        if domain.endswith("/"):
            domain = domain[:-1]
        self.domain = domain

    def _login(self):
        url = self.domain + "/api/auth/v1/signin"

        headers = {
            "Content-Type": "application/json",
        }

        response = requests.post(
            url,
            headers=headers,
            json={
                "method": 3,
                "email": self.email,
                "password": self.password,
                "clientId": "c3",
                "projectId": "p1",
            },
        )
        if response.status_code != 200:
            print(f"Request error {response.status_code}")
            print(response.text)
            exit(1)
        self.access_token = response.json()["authToken"]

    def _upload_file(self, file):
        url = self.domain + "/api/storage/v1/upload"

        headers = {
            "Authorization": f"Bearer {self.access_token}",
        }
        with open(
            file,
            "rb",
        ) as openned_file:
            response = requests.post(
                url,
                headers=headers,
                files=[
                    (
                        "file",
                        (
                            file.split("/")[-1],
                            openned_file,
                            "application/zip",
                        ),
                    )
                ],
            )
        if response.status_code != 201:
            print("upload failed")
            print(response.text)
            exit(1)
        return response.json()["id"]

    def _create_template(self, file_id, file_index, file_name):
        url = self.domain + "/api/extensions/template-bank/create"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.access_token}",
        }
        title = " ".join(file_name.split("/")[-1].split("_")[-1].split(".")[:-1])
        title = input(f"Enter title for file_name ({title}): ").strip() or title
        response = requests.post(
            url,
            headers=headers,
            json={
                "fileId": file_id,
                "sortingIndex": file_index,
                "title": title,
            },
        )

        if response.status_code != 200:
            print("upload _create_template failed with status code", response.status_code)
            print(response.text)
            exit(1)
        return response.json()

    def sync(self):
        self._login()
        file_ids = {}
        if os.path.isfile(self.path):
            if mimetypes.guess_type(self.path)[0] != "application/zip":
                print(f"{self.path} was not a zip")
                exit(1)
            file_ids[self.path] = self._upload_file(self.path)
        else:
            for file in os.listdir(self.path):
                file = os.path.join(self.path, file)
                if mimetypes.guess_type(file)[0] != "application/zip":
                    print(f"{file} was not a zip and was ignored")
                    continue
                file_ids[file] = self._upload_file(file)

        print("files uploaded", file_ids)
        for file_name, file_id in file_ids.items():
            file_index = file_name.split("/")[-1].split("_")[0]
            if file_index.isdigit():
                file_index = int(file_index)
            else:
                file_index = input(f"Enter index for {file_name} as a number!: ").strip() or 100

            if isinstance(file_index, str):
                if file_index.isdigit():
                    file_index = int(file_index)
                else:
                    print("Invalid number")
                    exit(1)
            pprint(self._create_template(file_id=file_id, file_index=file_index, file_name=file_name))


if __name__ == "__main__":
    fire.Fire(SyncBankTemplates)
