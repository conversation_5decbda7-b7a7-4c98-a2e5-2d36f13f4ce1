from datetime import datetime
from unittest.mock import patch

from dateutil.relativedelta import relativedelta
from freezegun import freeze_time
from jwt import PyJ<PERSON>

from sdk.authorization.dtos.stats_calculator import UserStatsCalculator
from sdk.authorization.dtos.user import PeriodProgress
from sdk.authorization.tasks import calculate_stats_per_user
from sdk.common.utils import inject
from sdk.key_action.models.config import KeyActionConfig
from sdk.key_action.models.key_action_log import KeyAction
from sdk.key_action.tests.IntegrationTests.key_actions_router_tests import (
    BaseKeyActionTestCase,
)

VALID_USER_ID = "5e8f0c74b50aa9656c34789b"
VALID_MANAGER_ID = "5e8f0c74b50aa9656c34789c"
FILLED_BY_MANAGER_CONFIG_ID = "5f1824ba504787d8d89ebeb0"
FILLED_BY_USER_CONFIG_ID = "5f1824ba504787d8d89ebeaf"
LAST_PERIOD_START = "2022-08-30T18:30:00.000000Z"
FILLED_BY_MANAGER = "Manager"
FILLED_BY_PATIENT = "User"
TEST_EMAIL = "<EMAIL>"
INVITATION_CODE = (
    "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2MTYwNzAwMjQsIm5iZiI6MTYxNjA3MDAyNCwia"
    "nRpIjoiYjliMDRiYzQtNmFiZi00MzkwLWI0MjUtYTM1YTc1NjgyNWQ4IiwiaWRlbnRpdHkiOiJ1c2VyMUBleGFtcGxlLm"
    "NvbSIsInR5cGUiOiJpbnZpdGF0aW9uIn0.6M22glMJAavCoeHGf8CEDOWyn9SBNyITxQot7PpaHZn"
)


class GroupedKeyActionRouterTestCase(BaseKeyActionTestCase):
    base_url = f"/api/extensions/v1/user/{VALID_USER_ID}/key-action"
    grouped_stats_url = f"{base_url}/grouped-forms-stats"
    grouped_forms_timeline_url = f"{base_url}/grouped-forms-stats"

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        inject.get_injector().rebind(lambda b: b.bind(UserStatsCalculator, UserStatsCalculator()))

    @freeze_time("2022-08-31T00:00")
    @patch.object(PyJWS, "_verify_signature")
    def setUp(self, _):
        super().setUp()
        self.now = datetime.utcnow()
        self.week_ago = self.now - relativedelta(weeks=1)
        self.yesterday = self.now - relativedelta(days=1)
        with freeze_time(self.now - relativedelta(months=1, days=2)):
            key_action_extra = {
                KeyActionConfig.INSTANCE_EXPIRES_IN: "P1W",
                KeyActionConfig.MODULE_ID: "Questionnaire",
                KeyActionConfig.MODULE_CONFIG_ID: FILLED_BY_MANAGER_CONFIG_ID,
                KeyActionConfig.DURATION_FROM_TRIGGER: "P1Y",
                KeyActionConfig.DURATION_ISO: "P1DT0H0M",
            }
            self.create_key_action(extra_data=key_action_extra)
            key_action_extra = {
                KeyActionConfig.INSTANCE_EXPIRES_IN: "P3D",
                KeyActionConfig.MODULE_ID: "Questionnaire",
                KeyActionConfig.MODULE_CONFIG_ID: FILLED_BY_USER_CONFIG_ID,
                KeyActionConfig.DURATION_FROM_TRIGGER: "P1Y",
                KeyActionConfig.DURATION_ISO: "P1DT0H0M",
            }
            self.create_key_action(extra_data=key_action_extra)
        with freeze_time(self.now - relativedelta(months=1, days=16)):
            self.user_id = self._sign_up_user(INVITATION_CODE, TEST_EMAIL, timezone="Asia/Kolkata")
        self.base_url = f"/api/extensions/v1/user/{self.user_id}/key-action"
        self.grouped_stats_url = f"{self.base_url}/grouped-forms-stats"
        self.grouped_forms_timeline_url = f"{self.base_url}/forms-timeline"

    def _submit_questionnaire_as_user(self, user_id: str, module_config_id):
        sample = self._sample_questionnaire()
        headers = self.get_headers_for_token(user_id)
        res = self.flask_client.post(
            f"/api/extensions/v1/user/{self.user_id}/module-result/Questionnaire",
            json=[sample],
            query_string={"moduleConfigId": module_config_id},
            headers=headers,
        )
        self.assertEqual(201, res.status_code)

    def _retrieve_grouped(self):
        headers = self.get_headers_for_token(VALID_MANAGER_ID)
        return self.flask_client.get(self.grouped_stats_url, headers=headers)

    def _retrieve_timeline(self, filled_by: str):
        headers = self.get_headers_for_token(VALID_MANAGER_ID)
        query_params = {"filledBy": filled_by, "startDateTime": LAST_PERIOD_START}
        return self.flask_client.get(
            self.grouped_forms_timeline_url,
            query_string=query_params,
            headers=headers,
        )

    def test_retrieve_grouped_key_actions_stats(self):
        with freeze_time(self.now):
            calculate_stats_per_user()
            rsp = self._retrieve_grouped()
            self.assertEqual(200, rsp.status_code)
            self.assertEqual(48, len(rsp.json))
            all_passed_period = rsp.json[40]
            one_passed_period = rsp.json[41]
            latest_period = rsp.json[47]
            self.assertEqual(
                2,
                all_passed_period[PeriodProgress.PASSED_DUE_DATE_FORMS_AMOUNT],
            )
            self.assertEqual(
                "2022-08-23T18:30:00.000000Z",
                all_passed_period[PeriodProgress.START_DATE_TIME],
            )
            self.assertEqual(
                1,
                one_passed_period[PeriodProgress.PASSED_DUE_DATE_FORMS_AMOUNT],
            )
            self.assertEqual(
                "2022-08-24T18:30:00.000000Z",
                one_passed_period[PeriodProgress.START_DATE_TIME],
            )
            self.assertEqual(LAST_PERIOD_START, latest_period[PeriodProgress.START_DATE_TIME])

    def test_retrieve_grouped_key_actions_timeline_submitted_by_manager(self):
        with freeze_time(self.now):
            self._submit_questionnaire_as_user(VALID_MANAGER_ID, FILLED_BY_MANAGER_CONFIG_ID)
            grouped_rsp = self._retrieve_grouped()
            self.assertEqual(50, grouped_rsp.json[-1][PeriodProgress.TASK_COMPLIANCE])

            timeline_rsp = self._retrieve_timeline(FILLED_BY_MANAGER)
            self.assertEqual(200, timeline_rsp.status_code)
            self.assertEqual(1, len(timeline_rsp.json))
            form_data = timeline_rsp.json[0]
            self.assertFalse(form_data[KeyAction.ENABLED])
            self.assertEqual(FILLED_BY_MANAGER_CONFIG_ID, form_data[KeyAction.MODULE_CONFIG_ID])

            rsp = self._retrieve_timeline(FILLED_BY_PATIENT)
            self.assertEqual(200, rsp.status_code)
            self.assertEqual(1, len(rsp.json))
            form_data = rsp.json[0]
            self.assertTrue(form_data[KeyAction.ENABLED])
            self.assertEqual(FILLED_BY_USER_CONFIG_ID, form_data[KeyAction.MODULE_CONFIG_ID])

    def test_retrieve_grouped_key_actions_timeline_filled_by_user(self):
        with freeze_time(self.now + relativedelta(weeks=3)):
            rsp = self._retrieve_timeline(FILLED_BY_PATIENT)
            self.assertEqual(200, rsp.status_code)
            form_data = rsp.json[0]
            self.assertTrue(form_data[KeyAction.ENABLED])
            self.assertEqual(FILLED_BY_USER_CONFIG_ID, form_data[KeyAction.MODULE_CONFIG_ID])
