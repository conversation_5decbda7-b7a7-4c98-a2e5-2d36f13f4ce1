from datetime import datetime
from pathlib import Path
from unittest.mock import patch

import freezegun
import pytz
from dateutil.relativedelta import relativedelta
from flask import url_for
from jwt import PyJWS

from sdk.appointment.component import AppointmentComponent
from sdk.auth.component import AuthComponent
from sdk.auth.tests.IntegrationTests.utils import sample_user
from sdk.authorization.component import AuthorizationComponent
from sdk.authorization.events.post_user_onboard_event import UserOnboardedEvent
from sdk.authorization.exceptions import UserErrorCodes
from sdk.calendar.component import CalendarComponent
from sdk.calendar.dtos.calendar_event_log import CalendarEventLogDTO
from sdk.calendar.models.calendar_event import CalendarEvent, CalendarEventLog
from sdk.calendar.router.calendar_request import ExportCalendarRequestObject
from sdk.calendar.utils import get_dt_from_str
from sdk.common.adapter.event_bus_adapter import EventBusAdapter
from sdk.common.localization.utils import Language
from sdk.common.utils import inject
from sdk.common.utils.common_functions_utils import find
from sdk.common.utils.date_utils import localize_from_utc
from sdk.common.utils.validators import utc_str_field_to_val, model_to_dict
from sdk.deployment.component import DeploymentComponent
from sdk.deployment.dtos.deployment import DeploymentDTO
from sdk.deployment.dtos.status import Status
from sdk.deployment.models import Deployment
from sdk.deployment.router.deployment_requests import (
    UpdateLocalizationsRequestObject,
)
from sdk.deployment.service.deployment_service import DeploymentService
from sdk.deployment.use_case.localizations_use_case import (
    UpdateLocalizationsUseCase,
)
from sdk.key_action.component import KeyActionComponent
from sdk.key_action.models.config import KeyActionConfig
from sdk.key_action.models.key_action_log import KeyAction
from sdk.key_action.use_case.key_action_requests import (
    RetrieveExpiringKeyActionsRequestObject,
)
from sdk.module_result.component import ModuleResultComponent
from sdk.module_result.modules import QuestionnaireModule, WeightModule
from sdk.organization.component import OrganizationComponent
from sdk.tests.extension_test_case import ExtensionTestCase
from sdk.versioning.component import VersionComponent
from ._mixin import KeyActionTestMixin, SUPER_ADMIN

VALID_USER_ID = "5e8f0c74b50aa9656c34789b"
VALID_MANAGER_ID = "5e8f0c74b50aa9656c34789c"
PROXY_ID = "5e8f0c74b50aa9656c34744a"
VALID_USER_ID_2 = "5e8f0c74b50aa9656c34789a"
VALID_DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
DRAFT_DEPLOYMENT_ID = "5d386cc6ff885918d96edb2d"
KEY_ACTION_CONFIG_ID = "60d9b623b07e15e833eae4a6"
VALID_KEY_ACTION_ID = "5f0f0373d656ed903e5a969e"
TEST_EVENT_ID = "5f0f0373d656ed903e5a969e"
TEST_EMAIL = "<EMAIL>"
INVITATION_CODE = (
    "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2MTYwNzAwMjQsIm5iZiI6MTYxNjA3MDAyNCwia"
    "nRpIjoiYjliMDRiYzQtNmFiZi00MzkwLWI0MjUtYTM1YTc1NjgyNWQ4IiwiaWRlbnRpdHkiOiJ1c2VyMUBleGFtcGxlLm"
    "NvbSIsInR5cGUiOiJpbnZpdGF0aW9uIn0.6M22glMJAavCoeHGf8CEDOWyn9SBNyITxQot7PpaHZn"
)


def datetime_to_str(val) -> str:
    return val.strftime("%Y-%m-%dT%H:%M:%S.%fZ")


class BaseKeyActionTestCase(ExtensionTestCase):
    SERVER_VERSION = "1.3.1"
    API_VERSION = "v1"
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        AppointmentComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
        CalendarComponent(),
        ModuleResultComponent(
            additional_modules=[
                WeightModule(),
                QuestionnaireModule(),
            ]
        ),
        KeyActionComponent(),
        VersionComponent(server_version=SERVER_VERSION, api_version=API_VERSION),
    ]
    fixtures = [Path(__file__).parent.joinpath("fixtures/deployments_dump.json")]

    def setUp(self):
        super().setUp()

        self.now = datetime.utcnow()
        self.relative_day = self.now.replace(hour=0, minute=0) + relativedelta(days=7)
        self.headers = self.get_headers_for_token(VALID_USER_ID)
        self.base_url = f"/api/extensions/v1/user/{VALID_USER_ID}/key-action"
        Deployment.objects.update(status=Status.DRAFT.value)

    def _sign_up_user(self, invitation_code: str, email: str = None, timezone: str = None):
        validation_data = {"invitationCode": invitation_code}
        rsp = self.flask_client.post(
            "/api/auth/v1/signup",
            json=sample_user(
                validation_data,
                phone_number="+************",
                timezone=timezone,
                email=email,
            ),
        )
        user_id = rsp.json["uid"]

        inject.instance(EventBusAdapter).emit(UserOnboardedEvent(user_id=user_id))
        return user_id

    def _get_query_date_range(self):
        return {
            "start": utc_str_field_to_val(self.relative_day),
            "end": utc_str_field_to_val(self.relative_day + relativedelta(days=7)),
        }

    def create_key_action(self, delta_from_trigger="PT0M", extra_data: dict = None):
        service = DeploymentService()
        duration = f"P1WT{self.now.hour}H{self.now.minute}M"
        config_data = {
            "title": "hu_ka_groupInfo_title",
            "description": "hu_ka_groupInfo_description",
            "deltaFromTriggerTime": delta_from_trigger,
            "durationFromTrigger": "P1M",
            "type": "MODULE",
            "trigger": "SIGN_UP",
            "durationIso": duration,
            "numberOfNotifications": 0,
            "moduleId": "BloodPressure",
            "moduleConfigId": "5f1824ba504787d8d89ebeaf",
            "instanceExpiresIn": "P1W",
        }
        if extra_data:
            config_data.update(extra_data)
        key_action_cfg = KeyActionConfig.from_dict(config_data)
        service.create_key_action(deployment_id=VALID_DEPLOYMENT_ID, key_action=key_action_cfg)

    def update_localizations(self):
        old_localization = model_to_dict(Deployment.objects.get(mongoId=VALID_DEPLOYMENT_ID)).get(
            DeploymentDTO.LOCALIZATIONS, {}
        )
        body = {
            "en": {
                "hu_ka_groupInfo_title": "We need some more information from you",
                "hu_ka_groupInfo_description": "You have a new task for the AZ:CONNECT EU study. Please complete as soon as you can.",
            },
            "de": {
                "hu_ka_groupInfo_title": "Wir benötigen noch einige Informationen über Sie",
                "hu_ka_groupInfo_description": "Sie haben eine neue Aufgabe für die AZ-COnnect-EU-Studie. Bitte erledigen Sie diese so schnell wie möglich.",
            },
        }
        for key, value in body.items():
            value.update(old_localization.get(key, {}))

        request_object = UpdateLocalizationsRequestObject.from_dict(
            {
                UpdateLocalizationsRequestObject.DEPLOYMENT_ID: VALID_DEPLOYMENT_ID,
                UpdateLocalizationsRequestObject.SUBMITTER_ID: VALID_DEPLOYMENT_ID,
                UpdateLocalizationsRequestObject.LOCALIZATIONS: body,
            }
        )

        UpdateLocalizationsUseCase().execute(request_object)

    def _sample_questionnaire(self, questionnaire_id="Test"):
        return {
            "type": "Questionnaire",
            "deviceName": "iOS",
            "deploymentId": VALID_DEPLOYMENT_ID,
            "startDateTime": datetime_to_str(datetime.utcnow()),
            "questionnaireId": questionnaire_id,
            "questionnaireName": "PAM Questionnaire",
            "value": 250.5,
            "answers": [
                {
                    "answerText": "Answer",
                    "answerScore": 55,
                    "id": "5ee0d29a58e7994d8633037c",
                    "questionId": "5ee0d29a58e7994d8633037c",
                    "question": "Simple Question 1",
                },
                {
                    "answerText": "Answer",
                    "answerScore": 55,
                    "id": "5ee0d29a58e7994d8633037c",
                    "questionId": "5ee0d29a58e7994d8633037d",
                    "question": "Simple Question 2",
                },
            ],
        }


class KeyActionRouterTestCase(BaseKeyActionTestCase, KeyActionTestMixin):
    def test_create_key_action__todo(self):
        """Todo key actions should not appear in the list."""
        self.assertEventsCount(5, VALID_USER_ID)
        sign_up_date = "2022-02-17T06:29:55.363Z"
        key_action = {
            "title": "Book parking",
            "description": "Make sure you book your parking before you arrive",
            "deltaFromTriggerTime": "P1D",
            "durationFromTrigger": "P1M",
            "durationIso": "P1MT0H0M",
            "type": KeyActionConfig.Type.TODO.value,
            "trigger": KeyActionConfig.Trigger.SIGN_UP.value,
            "numberOfNotifications": 0,
        }
        response = self._create_key_action(key_action, in_=VALID_DEPLOYMENT_ID)
        config_id = response["id"]
        actions = self._retrieve_timeline(
            for_=VALID_USER_ID,
            query={"start": sign_up_date, "end": "2022-02-24T10:00:00.000Z"},
        )
        actions = list(filter(lambda e: e.get(KeyAction.KEY_ACTION_CONFIG_ID) == config_id, actions))
        self.assertEqual(0, len(actions))
        self.assertEventsCount(6, VALID_USER_ID)

    def test_success_retrieve_key_actions(self):
        timezone = pytz.timezone("Europe/Kiev")
        expected_trigger_time = (0, 0)  # hours, minutes

        rsp = self.flask_client.get(self.base_url, headers=self.headers)
        self.assertEqual(200, rsp.status_code)
        self.assertGreater(len(rsp.json), 0)
        key_action = find(lambda x: x["id"] == TEST_EVENT_ID, rsp.json)
        self.assertNotIn(KeyAction.EXTRA_FIELDS, key_action)

        # check if timezone was injected correctly
        date = get_dt_from_str(key_action[KeyAction.START_DATE_TIME])
        local_date = localize_from_utc(date, timezone)
        self.assertEqual(expected_trigger_time, (local_date.hour, local_date.minute))

    def test_success_retrieve_key_actions_timeframe_simple(self):
        timezone = pytz.timezone("Europe/Kiev")
        expected_trigger_time = (0, 0)  # hours, minutes

        actions = self._retrieve_timeline(for_=VALID_USER_ID, query=self._get_query_date_range())
        self.assertGreater(len(actions), 0)
        key_action = find(lambda x: x["id"] == TEST_EVENT_ID, actions)

        # check if timezone was injected correctly
        date = get_dt_from_str(key_action[KeyAction.START_DATE_TIME])
        local_date = localize_from_utc(date, timezone)
        self.assertEqual(expected_trigger_time, (local_date.hour, local_date.minute))

    @patch.object(PyJWS, "_verify_signature")
    def test_success_retrieve_key_actions_timeframe(self, _):
        self.create_key_action()
        self.update_localizations()
        user_id = self._sign_up_user(INVITATION_CODE, TEST_EMAIL)

        actions = self._retrieve_timeline(for_=user_id, query=self._get_query_date_range())
        self.assertEqual(2, len(actions))

        self.assertEqual(
            "We need some more information from you",
            actions[0][KeyAction.TITLE],
        )

        start = utc_str_field_to_val(self.relative_day - relativedelta(months=1))
        end = utc_str_field_to_val(self.relative_day - relativedelta(minutes=1))
        query_params = {"start": start, "end": end}

        actions = self._retrieve_timeline(for_=user_id, query=query_params)
        self.assertEqual(1, len(actions))

        start = utc_str_field_to_val(self.relative_day)
        end = utc_str_field_to_val(self.relative_day + relativedelta(months=1))
        query_params = {"start": start, "end": end}

        self._retrieve_timeline(for_=user_id, query=query_params)

    def test_success_retrieve_key_actions_timeframe_filtered_by_filled_by(self):
        actions = self._retrieve_timeline(for_=VALID_USER_ID, as_=VALID_USER_ID, query=self._get_query_date_range())
        self.assertEqual(30, len(actions))

        actions = self._retrieve_timeline(for_=VALID_USER_ID, as_=VALID_MANAGER_ID, query=self._get_query_date_range())
        self.assertEqual(30, len(actions))

    @patch.object(PyJWS, "_verify_signature")
    def test_success_retrieve_key_actions_only_future_events(self, _):
        self.create_key_action()
        self.create_key_action(delta_from_trigger="P1D")
        user_id = self._sign_up_user(INVITATION_CODE, TEST_EMAIL)

        relative_day = self.now
        query_params = {
            "start": utc_str_field_to_val(relative_day),
            "end": utc_str_field_to_val(relative_day + relativedelta(days=2)),
            "allowPastEvents": False,
        }
        events = self._retrieve_timeline(for_=user_id, code=200, query=query_params)
        self.assertEqual(1, len(events))

    @patch.object(PyJWS, "_verify_signature")
    def test_success_retrieve_key_actions_only_expiring_events(self, _):
        self.create_key_action()
        self.create_key_action(delta_from_trigger="-P6D")
        user_id = self._sign_up_user(INVITATION_CODE, TEST_EMAIL)
        headers = self.get_headers_for_token(user_id)
        base_url = self.base_url.replace(VALID_USER_ID, user_id)

        expiring_url = base_url + "/expiring"

        rsp = self.flask_client.get(expiring_url, headers=headers)
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(1, len(rsp.json))

        # complete key action
        key_action = rsp.json[0]
        complete_event_url = base_url + f"/{key_action['id']}"
        complete_event_body = {
            KeyAction.START_DATE_TIME: key_action["startDateTime"],
            KeyAction.END_DATE_TIME: key_action["endDateTime"],
            KeyAction.MODEL: KeyAction.__name__,
        }
        rsp = self.flask_client.post(complete_event_url, json=complete_event_body, headers=headers)
        self.assertEqual(201, rsp.status_code, rsp.json.get("error"))

        rsp = self.flask_client.get(expiring_url, headers=headers)
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(0, len(rsp.json))

        rsp = self.flask_client.get(
            expiring_url,
            query_string={RetrieveExpiringKeyActionsRequestObject.ONLY_ENABLED: "false"},
            headers=headers,
        )
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(1, len(rsp.json))

    @patch.object(PyJWS, "_verify_signature")
    def test_success_retrieve_key_actions_timeframe_with_valid_language(self, _):
        self.create_key_action()
        self.update_localizations()
        user_id = self._sign_up_user(INVITATION_CODE, TEST_EMAIL)

        events = self._retrieve_timeline(for_=user_id, query=self._get_query_date_range())
        self.assertEqual(events[0][KeyAction.TITLE], "We need some more information from you")

        headers = {"x-hu-locale": Language.DE}
        events = self._retrieve_timeline(for_=user_id, query=self._get_query_date_range(), headers=headers)
        self.assertEqual(
            events[0][KeyAction.TITLE],
            "Wir benötigen noch einige Informationen über Sie",
        )

    @patch.object(PyJWS, "_verify_signature")
    def test_success_retrieve_key_actions_with_valid_language(self, _):
        self.create_key_action()
        self.update_localizations()
        user_id = self._sign_up_user(INVITATION_CODE, TEST_EMAIL)

        headers = self.get_headers_for_token(user_id)

        url = self.base_url.replace(VALID_USER_ID, user_id)
        rsp = self.flask_client.get(url, headers=headers)
        self.assertEqual(rsp.json[0][KeyAction.TITLE], "We need some more information from you")

        headers.update({"x-hu-locale": Language.DE})
        rsp = self.flask_client.get(url, headers=headers)
        self.assertEqual(rsp.json[0]["title"], "Wir benötigen noch einige Informationen über Sie")

    def test_success_create_delete_key_action_log(self):
        rsp = self.flask_client.get(self.base_url, headers=self.headers)
        self.assertEqual(200, rsp.status_code)
        self.assertGreater(len(rsp.json), 0)

        key_action = find(lambda ka: ka["id"] == VALID_KEY_ACTION_ID, rsp.json)
        self.assertTrue(key_action["enabled"])

        body = {
            "startDateTime": key_action["startDateTime"],
            "endDateTime": key_action["endDateTime"],
            "model": "KeyAction",
        }
        log_id = self._create_key_action_log(VALID_KEY_ACTION_ID, body, for_=VALID_USER_ID)["id"]
        self.assertIsNotNone(log_id)

        rsp = self.flask_client.get(self.base_url, headers=self.headers)
        self.assertEqual(200, rsp.status_code)
        self.assertGreater(len(rsp.json), 0)

        event = find(lambda ka: ka["id"] == log_id, rsp.json)
        self.assertFalse(event["enabled"])
        self.assertIn(KeyAction.COMPLETE_DATE_TIME, event)

        self._delete_key_action_log(log_id, for_=VALID_USER_ID)

        rsp = self.flask_client.get(self.base_url, headers=self.headers)
        self.assertEqual(200, rsp.status_code)

        event = find(lambda ka: ka["id"] == log_id, rsp.json)
        self.assertIsNone(event)

    def test_success_create_key_action_log_by_proxy(self):
        headers = self.get_headers_for_token(PROXY_ID)
        rsp = self.flask_client.get(self.base_url, headers=headers)
        self.assertEqual(200, rsp.status_code)
        self.assertTrue(rsp.json[0]["enabled"])

        key_action = rsp.json[0]
        body = {
            "startDateTime": key_action["startDateTime"],
            "endDateTime": key_action["endDateTime"],
            "model": "KeyAction",
        }
        rsp = self.flask_client.post(f"{self.base_url}/{VALID_KEY_ACTION_ID}", json=body, headers=headers)
        self.assertEqual(201, rsp.status_code)

    def test_create_log_on_module_result_submit(self):
        res = self.flask_client.get(self.base_url, headers=self.headers)
        self.assertGreater(len(res.json), 0)

        module_result = [
            {
                "type": "Weight",
                "deviceName": "iOS",
                "deploymentId": VALID_DEPLOYMENT_ID,
                "startDateTime": utc_str_field_to_val(datetime.utcnow()),
                "value": 80.0,
            }
        ]
        res = self.flask_client.post(
            f"/api/extensions/v1/user/{VALID_USER_ID}/module-result/Weight",
            json=module_result,
            headers=self.headers,
        )
        self.assertEqual(201, res.status_code)

        res = self.flask_client.get(self.base_url, headers=self.headers)
        self.assertGreater(len(res.json), 0)
        key_actions = [item for item in res.json if item.get("moduleId") == "Weight"]
        disabled = [item for item in res.json if not item["enabled"]]
        self.assertEqual(len(key_actions), len(disabled))

    def test_create_log_on_questionnaire_submit(self):
        res = self.flask_client.get(self.base_url, headers=self.headers)
        self.assertGreater(len(res.json), 0)

        sample = self._sample_questionnaire("4318697b-b546-49ea-a2f9-9e077a0079b77")

        res = self.flask_client.post(
            f"/api/extensions/v1/user/{VALID_USER_ID}/module-result/Questionnaire",
            json=[sample],
            headers=self.headers,
        )
        self.assertEqual(201, res.status_code)

        res = self.flask_client.get(self.base_url, headers=self.headers)
        self.assertGreater(len(res.json), 0)
        key_actions = [item for item in res.json if item.get("moduleConfigId") == "5f1824ba504787d8d89ebeaf"]
        disabled = [item for item in res.json if not item["enabled"]]
        self.assertEqual(len(key_actions), len(disabled))

    def test_create_log_on_questionnaire_submit_with_module_config_id(self):
        res = self.flask_client.get(self.base_url, headers=self.headers)
        self.assertGreater(len(res.json), 0)

        sample = self._sample_questionnaire()

        res = self.flask_client.post(
            f"/api/extensions/v1/user/{VALID_USER_ID}/module-result/Questionnaire",
            json=[sample],
            query_string={"moduleConfigId": "5f1824ba504787d8d89ebeaf"},
            headers=self.headers,
        )
        self.assertEqual(201, res.status_code)

        res = self.flask_client.get(self.base_url, headers=self.headers)
        self.assertGreater(len(res.json), 0)
        key_actions = [item for item in res.json if item.get("moduleConfigId") == "5f1824ba504787d8d89ebeaf"]
        disabled = [item for item in res.json if not item["enabled"]]
        self.assertEqual(len(key_actions), len(disabled))

    def test_failure_export_not_valid_user_id(self):
        self.test_server.config.server.debugRouter = True
        rsp = self.flask_client.get("/api/calendar/v1/user/not_valid_id/export")
        self.assertEqual(403, rsp.status_code)

    def test_failure_export_calendar_with_key_actions(self):
        rsp = self.flask_client.get(f"/api/calendar/v1/user/{VALID_USER_ID}/export")
        self.assertEqual(401, rsp.status_code)

    def test_success_export_calendar_with_key_actions_debug_true(self):
        self.test_server.config.server.debugRouter = True
        now = datetime.utcnow()
        data = {
            ExportCalendarRequestObject.START: utc_str_field_to_val(now - relativedelta(months=1)),
            ExportCalendarRequestObject.END: utc_str_field_to_val(now),
        }
        rsp = self.flask_client.get(f"/api/calendar/v1/user/{VALID_USER_ID}/export", query_string=data)
        self.assertEqual(200, rsp.status_code)
        self.assertTrue("BEGIN:VEVENT" in rsp.data.decode())

    @freezegun.freeze_time("2020-07-14T14:24:00")
    def test_delete_key_action_in_deployment(self):
        url = url_for(
            "deployment_route.delete_key_action_config",
            deployment_id=DRAFT_DEPLOYMENT_ID,
            key_action_id=KEY_ACTION_CONFIG_ID,
        )
        rsp = self.flask_client.delete(url, headers=self.get_headers_for_token(SUPER_ADMIN))
        self.assertEqual(204, rsp.status_code)

        events = CalendarEvent.objects.filter(
            userId=VALID_USER_ID, extraFields__keyActionConfigId=KEY_ACTION_CONFIG_ID
        ).all()
        self.assertEqual(1, len(events))  # User from Published deployment should not be affected

    def assertEventsCount(self, count: int, user: str):
        db_count = CalendarEvent.objects.filter(userId=user).count()
        self.assertEqual(count, db_count)


class DeleteUserWithKeyActions(BaseKeyActionTestCase):
    base_route = "api/auth/v1/private/user"

    def test_success_delete_user_with_key_actions(self):
        res = self.flask_client.get(self.base_url, headers=self.headers)
        self.assertGreater(len(res.json), 0)

        super_admin_headers = self.get_headers_for_token(SUPER_ADMIN)
        del_user_path = f"{self.base_route}/{VALID_USER_ID}/delete-user"
        rsp = self.flask_client.delete(del_user_path, headers=super_admin_headers)
        self.assertEqual(rsp.status_code, 200)

        rsp = self.flask_client.get(self.base_url, headers=super_admin_headers)
        self.assertEqual(UserErrorCodes.INVALID_USER_ID, rsp.json["code"])

    def test_success_bulk_delete_user_event_logs(self):
        data = {
            "endDateTime": "2023-03-06T10:48:00.810Z",
            "model": "string",
            "startDateTime": "2023-03-06T10:48:00.811Z",
        }
        url = f"{self.base_url}/{VALID_KEY_ACTION_ID}"
        rsp = self.flask_client.post(url, json=data, headers=self.headers)
        self.assertEqual(rsp.status_code, 201)

        super_admin_headers = self.get_headers_for_token(SUPER_ADMIN)
        del_user_path = f"{self.base_route}/{VALID_USER_ID}/delete-user"
        rsp = self.flask_client.delete(del_user_path, headers=super_admin_headers)
        self.assertEqual(rsp.status_code, 200)
        self.assertIn(CalendarEventLogDTO.MODEL_NAME, rsp.json)

        # Make sure that event logs are deleted for current user
        count_user_event_log = CalendarEventLog.objects.filter(userId=VALID_USER_ID).count()
        self.assertEqual(0, count_user_event_log)
