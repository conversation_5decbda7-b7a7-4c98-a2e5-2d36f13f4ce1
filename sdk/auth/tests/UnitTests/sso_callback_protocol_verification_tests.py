import unittest
from unittest.mock import Mock, patch

from sdk.auth.use_case.sso._callback_protocol_verification import (
    OIDCCallbackValidation,
    SAMLCallbackValidation,
)


class OIDCCallbackValidationTestCase(unittest.TestCase):
    @patch("sdk.auth.use_case.sso._callback_protocol_verification.get_sso_configs_associated_with_user_or_invitee")
    @patch("sdk.auth.use_case.sso._callback_protocol_verification.get_provider_cfg")
    @patch("requests.post")
    def test_get_tokens(self, mock_post, mock_get_provider_cfg, mock_get_sso_configs):
        mock_get_sso_configs.return_value.info = Mock(
            client_id="client_id",
            discovery_url="https://example.com",
            redirect_uri="https://redirect.example.com",
            client_secret="secret",
        )
        mock_get_provider_cfg.return_value = {"token_endpoint": "https://api.example.com/token"}
        mock_response = Mock()
        mock_response.json.return_value = {
            "access_token": "access_token",
            "id_token": "id_token",
        }
        mock_post.return_value = mock_response
        use_case = OIDCCallbackValidation()
        request_obj = Mock(
            email="<EMAIL>",
            callbackData={
                "code": "code",
                "url": "https://example.com/callback?code=code",
            },
        )
        tokens = use_case.get_tokens(request_obj)
        self.assertEqual(tokens["access_token"], "access_token")
        self.assertEqual(tokens["id_token"], "id_token")

    @patch("sdk.auth.use_case.sso._callback_protocol_verification.get_sso_configs_associated_with_user_or_invitee")
    @patch("sdk.auth.use_case.sso._callback_protocol_verification.get_provider_cfg")
    @patch("requests.post")
    def test_get_tokens_failure(self, mock_post, mock_get_provider_cfg, mock_get_sso_configs):
        mock_get_sso_configs.return_value.info = Mock(
            client_id="client_id",
            discovery_url="https://example.com",
            redirect_uri="https://redirect.example.com",
            client_secret="secret",
        )
        mock_get_provider_cfg.return_value = {"token_endpoint": "https://api.example.com/token"}
        mock_post.return_value = Mock(status_code=400, json=lambda: {"error": "invalid_request"})
        use_case = OIDCCallbackValidation()
        request_obj = Mock(
            email="<EMAIL>",
            callbackData={
                "code": "invalid_code",
                "url": "https://example.com/callback?code=invalid_code",
            },
        )
        with self.assertRaises(Exception) as context:
            use_case.get_tokens(request_obj)
        self.assertIn("invalid_request", str(context.exception))

    @patch("sdk.auth.use_case.sso._callback_protocol_verification.OIDCCallbackValidation.authenticate_tokens")
    @patch("sdk.auth.use_case.sso._callback_protocol_verification.OIDCCallbackValidation.get_tokens")
    def test_process_request_invalid(self, mock_get_tokens, mock_authenticate_tokens):
        mock_get_tokens.side_effect = Exception("Failed to get tokens")
        use_case = OIDCCallbackValidation()
        request_obj = Mock(email="<EMAIL>")

        with self.assertRaises(Exception) as context:
            use_case.validate(request_obj)

        self.assertIn("Failed to get tokens", str(context.exception))

    @patch("requests.get")
    @patch("jwt.get_unverified_header")
    @patch("jwt.decode")
    @patch("sdk.auth.use_case.sso._callback_protocol_verification.get_sso_configs_associated_with_user_or_invitee")
    @patch("sdk.auth.use_case.sso._callback_protocol_verification.get_provider_cfg")
    @patch("sdk.auth.use_case.sso._utils.assign_saml_keyfiles")
    def test_authenticate_tokens(
        self,
        mock_assign_saml_keyfiles,
        mock_get_provider_cfg,
        mock_get_sso_configs,
        mock_decode,
        mock_get_unverified_header,
        mock_get,
    ):
        mock_assign_saml_keyfiles.return_value = Mock()
        mock_get_provider_cfg.return_value = {
            "issuer": "issuer",
            "jwks_uri": "jwks_uri",
        }
        mock_get_sso_configs.return_value.info = Mock(client_id="client_id")
        mock_get_unverified_header.return_value = {"kid": "kid"}
        mock_get.return_value.json.return_value = {"keys": [{"kid": "kid", "n": "dGVzdA==", "e": "AQAB"}]}
        use_case = OIDCCallbackValidation()
        tokens = {"id_token": "id_token"}
        request_obj = Mock(email="<EMAIL>")
        payload = use_case.authenticate_tokens(tokens, request_obj)
        self.assertIsNotNone(payload)

    @patch("requests.get")
    @patch("jwt.get_unverified_header")
    @patch("jwt.decode")
    @patch("sdk.auth.use_case.sso._callback_protocol_verification.get_sso_configs_associated_with_user_or_invitee")
    @patch("sdk.auth.use_case.sso._callback_protocol_verification.get_provider_cfg")
    @patch("sdk.auth.use_case.sso._utils.assign_saml_keyfiles")
    def test_authenticate_tokens_invalid_token(
        self,
        mock_assign_saml_keyfiles,
        mock_get_provider_cfg,
        mock_get_sso_configs,
        mock_decode,
        mock_get_unverified_header,
        mock_get,
    ):
        mock_assign_saml_keyfiles.return_value = Mock()
        mock_get_provider_cfg.return_value = {
            "issuer": "issuer",
            "jwks_uri": "jwks_uri",
        }
        mock_get_sso_configs.return_value.info = Mock(client_id="client_id")
        mock_get_unverified_header.return_value = {"kid": "invalid_kid"}
        mock_get.return_value.json.return_value = {"keys": [{"kid": "different_kid", "n": "dGVzdA==", "e": "AQAB"}]}
        mock_decode.side_effect = Exception("Signature verification failed")

        use_case = OIDCCallbackValidation()
        tokens = {"id_token": "invalid_id_token"}
        request_obj = Mock(email="<EMAIL>")

        with self.assertRaises(Exception) as context:
            use_case.authenticate_tokens(tokens, request_obj)

        self.assertIn("Your OIDC token was not found", str(context.exception))

    @patch("sdk.auth.use_case.sso._callback_protocol_verification.OIDCCallbackValidation.authenticate_tokens")
    @patch("sdk.auth.use_case.sso._callback_protocol_verification.OIDCCallbackValidation.get_tokens")
    def test_process_request(self, mock_get_tokens, mock_authenticate_tokens):
        mock_get_tokens.return_value = Mock()
        mock_authenticate_tokens.return_value = {"email": "<EMAIL>"}
        use_case = OIDCCallbackValidation()
        request_obj = Mock(email="<EMAIL>")
        payload = use_case.validate(request_obj)
        self.assertEqual(payload, {"email": "<EMAIL>"})


class SAMLCallbackValidationTestCase(unittest.TestCase):
    @patch("sdk.auth.use_case.sso._callback_protocol_verification.get_sso_configs_associated_with_user_or_invitee")
    @patch("sdk.auth.use_case.sso._callback_protocol_verification.assign_saml_keyfiles")
    @patch("saml2.client.Saml2Client.parse_authn_request_response")
    @patch("sdk.auth.use_case.sso._saml_parsers.SAMLParser.Huma.parser")
    def test_process_request(
        self,
        mock_saml_parser,
        mock_parse_authn_request_response,
        mock_assign_saml_keyfiles,
        mock_get_sso_configs,
    ):
        mock_get_sso_configs.return_value = Mock(
            info__dict__={
                "virtual_organization": {},
                "extension_schemas": [],
            },
            org="Huma",
        )

        mock_assign_saml_keyfiles.return_value = Mock(info=Mock(), org="Huma")

        mock_parse_authn_request_response.return_value = {
            "givenName": "name1",
            "familyName": "name2",
        }

        mock_saml_parser.return_value = {
            "givenName": "name1",
            "familyName": "name2",
            "email": "<EMAIL>",
        }

        use_case = SAMLCallbackValidation()

        request_obj = Mock(
            email="<EMAIL>",
            callbackData={"SAML_response": "response"},
            org="Huma",
        )

        response = use_case.validate(request_obj)

        self.assertEqual(
            response,
            {"givenName": "name1", "familyName": "name2", "email": "<EMAIL>"},
        )


if __name__ == "__main__":
    unittest.main()
