from unittest import TestCase
from unittest.mock import MagicMock, patch

from sdk.auth.enums import VerificationType
from sdk.auth.repository import AuthRepository
from sdk.auth.tests.test_helpers import get_verify_credentials_request_object
from sdk.auth.use_case.auth_use_cases import VerifyCredentialsUseCase
from sdk.common.adapter.event_bus_adapter import EventBusAdapter
from sdk.common.adapter.token_adapter import TokenAdapter
from sdk.common.exceptions.exceptions import (
    InvalidPasswordException,
    InvalidPhoneNumberException,
    PhoneNumberNotSetException,
)
from sdk.common.utils import inject
from sdk.common.utils.token.jwt.jwt import IDENTITY_CLAIM_KEY
from sdk.phoenix.config.server_config import PhoenixServerConfig

USE_CASE = "sdk.auth.use_case"
CODE = "code"
PASSWORD = "Password123"
PHONE_NUMBER = "+48511290300"


class VerifyCredentialsUseCaseTestCase(TestCase):
    def setUp(self) -> None:
        self.repo = MagicMock()
        self.token_adapter = MagicMock()
        self.config = MagicMock()
        self.event_bus = MagicMock()

        def bind(binder):
            binder.bind(AuthRepository, self.repo)
            binder.bind(TokenAdapter, self.token_adapter)
            binder.bind(PhoenixServerConfig, self.config)
            binder.bind(EventBusAdapter, self.event_bus)

        inject.clear_and_configure(bind)

    @patch(f"{USE_CASE}.auth_use_cases.get_user")
    @patch(f"{USE_CASE}._requests.validate_project_and_client_id")
    def verify_credentials_basic_test(
        self,
        validate_project_and_client_id,
        get_user,
        verification_type=VerificationType.PASSWORD,
        password=None,
        confirmation_code=None,
        phone_number=None,
        match_phone=True,
        remove_mfa_phone=False,
    ):
        self.validate_project_and_client_id = validate_project_and_client_id
        self.get_user = get_user
        self.mock_user, self.mock_token = MagicMock(), MagicMock()
        if phone_number and match_phone:
            self.mock_user.get_mfa_identifier().value = phone_number
        if remove_mfa_phone:
            self.mock_user.get_mfa_identifier.return_value = None
        self.get_user.return_value = self.mock_user
        self.token_adapter.verify_token.return_value = self.mock_token

        self.req_obj = get_verify_credentials_request_object(
            verification_type, password, confirmation_code, phone_number
        )
        rsp = VerifyCredentialsUseCase().execute(self.req_obj)
        self.validate_project_and_client_id.assert_called_once_with(self.req_obj.clientId, self.req_obj.projectId)
        self.token_adapter.verify_token.assert_called_once_with(self.req_obj.authToken)
        self.get_user.assert_called_once_with(repo=self.repo, uid=self.mock_token[IDENTITY_CLAIM_KEY])
        self.event_bus.emit.assert_called_once()
        return rsp

    @patch(f"{USE_CASE}.auth_use_cases.is_correct_password")
    def test_verify_password_success(self, check_pw):
        rsp = self.verify_credentials_basic_test(password=PASSWORD)

        check_pw.assert_called_once_with(self.mock_user.hashedPassword, PASSWORD)
        self.assertTrue(rsp.to_dict()["ok"])

    @patch(f"{USE_CASE}.auth_use_cases.is_correct_password")
    def test_verify_incorrect_password(self, check_pw):
        check_pw.return_value = False
        with self.assertRaises(InvalidPasswordException):
            self.verify_credentials_basic_test(password=PASSWORD)
            check_pw.assert_called_once_with(self.mock_user.hashedPassword, PASSWORD)

    @patch(f"{USE_CASE}.auth_use_cases.validate_phone_number_code")
    def test_verify_code(self, validate_code):
        rsp = self.verify_credentials_basic_test(
            verification_type=VerificationType.PHONE_CONFIRMATION_CODE,
            confirmation_code=CODE,
            phone_number=PHONE_NUMBER,
        )
        validate_code.assert_called_once_with(self.config, PHONE_NUMBER, CODE)
        self.assertTrue(rsp.to_dict()["ok"])

    def test_verify_code_raises_error_invalid_phone_number(self):
        with self.assertRaises(InvalidPhoneNumberException):
            self.verify_credentials_basic_test(
                verification_type=VerificationType.PHONE_CONFIRMATION_CODE,
                confirmation_code=CODE,
                phone_number=PHONE_NUMBER,
                match_phone=False,
            )

    def test_verify_code_raises_error_phone_number_not_set(self):
        with self.assertRaises(PhoneNumberNotSetException):
            self.verify_credentials_basic_test(
                verification_type=VerificationType.PHONE_CONFIRMATION_CODE,
                confirmation_code=CODE,
                phone_number=PHONE_NUMBER,
                remove_mfa_phone=True,
            )
