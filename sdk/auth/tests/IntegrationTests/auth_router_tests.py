import unittest
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import patch

from flask import url_for
from freezegun import freeze_time
from jwt import PyJWS
from twilio.base.exceptions import TwilioRestException

from sdk.auth.component import AuthComponent
from sdk.auth.dtos.auth_user import AuthIdentifierType, AuthUserDTO
from sdk.auth.enums import (
    AuthStage,
    Method,
    SendVerificationTokenMethod,
    VerificationType,
)
from sdk.auth.model.auth_user import AuthUser
from sdk.auth.tests.mock_objects import MockSmsVerificationAdapter
from sdk.auth.tests.test_helpers import (
    CLIENT_ID_3,
    CLINICIAN_ID,
    NOT_EXISTING_EMAIL,
    PHONE_NUMBER,
    PROJECT_ID,
    TEST_CLIENT_ID,
    USER_EMAIL,
    USER_ID,
    USER_ID_2,
    USER_ID_3,
    USER_PASSWORD,
    USER_PHONE_NUMBER,
)
from sdk.auth.use_case import (
    ConfirmationRequestObject,
    RefreshTokenRequestObjectV1,
    SendVerificationTokenRequestObject,
    SetAuthAttributesRequestObject,
    SignInRequestObject,
    SignUpRequestObject,
)
from sdk.auth.use_case.auth_use_cases import mask_phone_number
from sdk.auth.use_case.utils import TEST_CONFIRMATION_CODE
from sdk.authorization.component import AuthorizationComponent
from sdk.authorization.dtos.role.role import RoleDTO, RoleName
from sdk.authorization.services.authorization import AuthorizationService
from sdk.common.adapter.token_adapter import TokenAdapter
from sdk.common.adapter.twilio.config import MultiSiteTwilioSmsVerificationConfig
from sdk.common.adapter.twilio.sms_verification_adapter import (
    TwilioSmsVerificationAdapter,
)
from sdk.common.exceptions.exceptions import ErrorCodes
from sdk.common.utils import inject
from sdk.common.utils.token.jwt.jwt import AUTH_STAGE_KEY, USER_CLAIMS_KEY
from sdk.common.utils.validators import model_to_dict
from sdk.deployment.component import DeploymentComponent
from sdk.organization.component import OrganizationComponent
from sdk.phoenix.config.project_config import Client
from ._mixins import AuthTestMixin
from .utils import BaseSignUpTestCase

INVALID_USER_ID = "5eafc4cf573e3f55ee088e1a"
EXISTING_INVITATION_PHONE_NUMBER = "+48511290309"
NON_EXISTING_PHONE_NUMBER = "+48511290000"
VALID_SHORT_CODE = "qzOjrnmv54_I3_Rq"
VALID_CLINICIAN_INVITATION_CODE = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YG259psbNge1pnkbDfjUEj0fDDga9SRMGZVduFR6a2w"
INV_EMAIL = "<EMAIL>"
INV_GIVEN_NAME = "John"
INV_FAMILY_NAME = "Doe"
WEB_MANAGER_CLIENT_ID = "c3"
TEST_EMAIL = "<EMAIL>"
INVITATION_CODE = (
    "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2MTYwNzAwMjQsIm5iZiI6MTYxNjA3MDAyNCwia"
    "nRpIjoiYjliMDRiYzQtNmFiZi00MzkwLWI0MjUtYTM1YTc1NjgyNWQ4IiwiaWRlbnRpdHkiOiJ1c2VyMUBleGFtcGxlLm"
    "NvbSIsInR5cGUiOiJpbnZpdGF0aW9uIn0.6M22glMJAavCoeHGf8CEDOWyn9SBNyITxQot7PpaHZn"
)


class SendPhoneVerificationTokenTestCase(BaseSignUpTestCase, AuthTestMixin):
    def test_send_verification(self):
        self._send_verification_token(method=SendVerificationTokenMethod.PHONE_NUMBER, phoneNumber=PHONE_NUMBER)

    @patch("sdk.common.adapter.twilio.sms_verification_adapter.Client")
    def test_failed_to_send_token_due_to_blocked_number(self, mock_client):
        config_dict = {
            "default": {
                "sourcePhoneNumber": "111111",
                "templateKey": "TestKey",
                "serviceName": "TestService",
                "useTwilioVerify": True,
                "twilioVerifyServiceSid": "TestSid",
            }
        }
        config = MultiSiteTwilioSmsVerificationConfig.from_dict(config_dict)
        self.sms_adapter = TwilioSmsVerificationAdapter(config=config)

        def bind_twilio_adapter(binder: inject.Binder):
            binder.bind("twilioSmsVerificationAdapter", self.sms_adapter)

        inject.get_injector_or_die().rebind(bind_twilio_adapter)

        client_instance = mock_client.return_value
        mock_verifications = client_instance.verify.v2.services.return_value.verifications
        twilio_error_code = 60410
        mock_verifications.create.side_effect = TwilioRestException(status=403, code=twilio_error_code, uri="")
        response = self._send_verification_token(
            method=SendVerificationTokenMethod.PHONE_NUMBER,
            phoneNumber=PHONE_NUMBER,
            code=403,
        )

        self.assertEqual(response["code"], ErrorCodes.BLOCKED_PHONE_NUMBER)
        self.assertEqual(
            response["message"],
            f"Phone number verification failed. Twilio error: {twilio_error_code}",
        )
        super().setUpClass()


class AuthProfileTestCase(BaseSignUpTestCase, AuthTestMixin):
    def test_a_valid_auth_token(self):
        user_id = "5e8f0c74b50aa9656c34789c"
        valid_body = {"authToken": self.create_token(user_id)}
        response = self._auth_profile(valid_body, code=200)
        self.assertEqual(user_id, response["uid"])

    def test_an_invalid_auth_token(self):
        invalid_body = {"authToken": self.create_token(USER_ID) + "test"}
        self._auth_profile(invalid_body, code=401)


class RetrieveSessionsTestCase(BaseSignUpTestCase, AuthTestMixin):
    fixtures = [Path(__file__).parent.joinpath("fixtures/session_dump.json")]

    def test_retrieve_device_sessions_by_user_id(self):
        sessions = self._retrieve_sessions(USER_ID)
        self.assertEqual(1, len(sessions))

    def test_retrieve_device_sessions_by_invalid_user_id(self):
        sessions = self._retrieve_sessions(INVALID_USER_ID)
        self.assertEqual(0, len(sessions))


class TFATestCase(BaseSignUpTestCase, AuthTestMixin):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
    ]

    fixtures = [
        Path(__file__).parent.joinpath("fixtures/deployments_dump.json"),
        Path(__file__).parent.joinpath("fixtures/mfa_user_dump.json"),
    ]

    override_config = {
        "server.project.clients": [
            {
                Client.NAME: "MANAGER_WEB-client",
                Client.CLIENT_ID: "c3",
                Client.CLIENT_TYPE: "MANAGER_WEB",
                Client.ROLE_IDS: ["Manager", "User"],
            },
            {
                Client.NAME: "USER_IOS-client",
                Client.CLIENT_ID: "c2",
                Client.CLIENT_TYPE: "USER_IOS",
            },
            {
                Client.NAME: "USER_ANDROID-client",
                Client.CLIENT_ID: "ctest1",
                Client.ROLE_IDS: ["User"],
                Client.CLIENT_TYPE: "USER_ANDROID",
            },
        ],
    }

    @classmethod
    def setUpClass(cls) -> None:
        super(TFATestCase, cls).setUpClass()
        cls.token_adapter: TokenAdapter = inject.instance(TokenAdapter)

    def test_enable_disable_mfa_set_auth_att(self):
        auth_token = self.create_token(USER_ID)
        self._set_auth_attr(auth_token, mfaEnabled=True)
        response = self._set_auth_attr(auth_token, mfaEnabled=False, code=400)
        self.assertEqual("Access token is not valid for Multi-Factor Auth", response["message"])

    def test_user_mfa_NOT_required_WHEN_clinician_has_custom_config_in_organization(
        self,
    ):
        # Request with Clinician auth data
        auth_token = self.create_token(CLINICIAN_ID, user_claims={"authStage": 2})
        response = self._check_auth_attr(auth_token, clientId=CLIENT_ID_3)
        self.assertEqual(False, response["mfaEnabled"])

        # Request with Patient auth data
        auth_token = self.create_token(USER_ID_2, user_claims={"authStage": 2})
        response = self._check_auth_attr(auth_token)
        self.assertEqual(True, response["mfaEnabled"])

    def test_user_attempts_to_disable_mfa_WHEN_organization_config_is_optional_for_clinician(
        self,
    ):
        auth_token = self.create_token(USER_ID_2, user_claims={"authStage": 2})
        self._set_auth_attr(auth_token, mfaEnabled=False, code=403)

    def test_user_attempt_to_disable_mfa_WHEN_deployment_NOT_allows(self):
        auth_token = self.create_token(USER_ID_2, user_claims={"authStage": 2})
        response = self._set_auth_attr(auth_token, mfaEnabled=False, code=403)
        self.assertEqual("MFA is required", response["message"])

    def test_token_stage_while_setting_up_mfa_identifier(self):
        auth_token = self.create_token(USER_ID_3, user_claims={"authStage": 1})
        auth_2_token = self.create_token(USER_ID_3, user_claims={"authStage": 2})
        code, test_number = "010101", "+380510000450"
        self._set_auth_attr(auth_2_token, phoneNumber=test_number, confirmationCode=code)
        self._set_auth_attr(auth_token, phoneNumber=test_number, confirmationCode=code, code=400)

    def test_mfa_identifier_change_WHEN_token_is_NOT_stage_2(self):
        auth_token = self.create_token(USER_ID_2, user_claims={"authStage": 1})
        test_code, test_phone_number = "010101", "+380510000000"
        set_auth_attributes_data = {
            SetAuthAttributesRequestObject.AUTH_TOKEN: auth_token,
            SetAuthAttributesRequestObject.PHONE_NUMBER: test_phone_number,
            SetAuthAttributesRequestObject.CONFIRMATION_CODE: test_code,
            SetAuthAttributesRequestObject.CLIENT_ID: "ctest1",
            SetAuthAttributesRequestObject.PROJECT_ID: "ptest1",
        }
        self._set_auth_attr(auth_token, **set_auth_attributes_data)

    @patch.object(PyJWS, "_verify_signature")
    def test_new_user_mfa_signup_and_signin(self, _):
        # 1. Sign up
        new_user_email = TEST_EMAIL
        new_user_phone_number = "+380500000000"
        signup_data = {
            SignUpRequestObject.METHOD: Method.TWO_FACTOR_AUTH,
            SignUpRequestObject.EMAIL: new_user_email,
            SignUpRequestObject.PASSWORD: USER_PASSWORD,
            SignUpRequestObject.DISPLAY_NAME: "test",
            SignUpRequestObject.VALIDATION_DATA: {"invitationCode": INVITATION_CODE},
            SignUpRequestObject.USER_ATTRIBUTES: {
                "familyName": "hey",
                "givenName": "test",
                "dob": "1988-02-20",
                "gender": "MALE",
            },
            SignUpRequestObject.CLIENT_ID: "ctest1",
            SignUpRequestObject.PROJECT_ID: "ptest1",
        }

        self._sign_up(signup_data, code=200)

        sign_in_data = {
            SignInRequestObject.METHOD: Method.EMAIL_PASSWORD,
            SignInRequestObject.EMAIL: new_user_email,
            SignUpRequestObject.PASSWORD: USER_PASSWORD,
        }
        response = self._sign_in(**sign_in_data, code=200)
        refresh_token = response.get("refreshToken")
        auth_token = response.get("authToken")
        self.assertIsNotNone(refresh_token)
        self.assertIsNotNone(auth_token)
        self._check_tokens_valid_for_mfa(auth_token, refresh_token, valid=False)

        self._set_auth_attr(auth_token, phoneNumber=new_user_phone_number)

        send_phone_number_confirmation_data = {
            SendVerificationTokenRequestObject.METHOD: SendVerificationTokenMethod.PHONE_NUMBER,
            SendVerificationTokenRequestObject.PHONE_NUMBER: new_user_phone_number,
        }
        self._send_verification_token(**send_phone_number_confirmation_data, code=200)

        phone_number_confirmation_data = {
            ConfirmationRequestObject.PHONE_NUMBER: new_user_phone_number,
            ConfirmationRequestObject.EMAIL: new_user_email,
            ConfirmationRequestObject.CONFIRMATION_CODE: TEST_CONFIRMATION_CODE,
            ConfirmationRequestObject.CLIENT_ID: "ctest1",
            ConfirmationRequestObject.PROJECT_ID: "ptest1",
        }
        phone_number_confirmation_response = self._confirm_identity(phone_number_confirmation_data, code=200)
        refresh_token = phone_number_confirmation_response.get("refreshToken")
        auth_token = phone_number_confirmation_response.get("authToken")
        self.assertIsNotNone(refresh_token)
        self.assertIsNotNone(auth_token)
        self._check_tokens_valid_for_mfa(auth_token, refresh_token)

    @patch.object(PyJWS, "_verify_signature")
    def test_new_sms_user_signup_and_signin(self, _):
        # 1. Sign up
        phone_number = "+48511290311"
        data = self._sign_up_data(
            Method.PHONE_NUMBER, phone_number, validation_data={"shortenedCode": "qzOjrnmv54_I3_Rr"}
        )
        self._sign_up(data, code=200)

        # 2. Send verification token
        send_phone_number_confirmation_data = {
            SendVerificationTokenRequestObject.METHOD: SendVerificationTokenMethod.PHONE_NUMBER,
            SendVerificationTokenRequestObject.PHONE_NUMBER: phone_number,
        }
        self._send_verification_token(**send_phone_number_confirmation_data, code=200)

        # 3. Sign in
        sign_in_data = {
            SignInRequestObject.METHOD: Method.PHONE_NUMBER,
            SignInRequestObject.PHONE_NUMBER: phone_number,
            SignInRequestObject.CONFIRMATION_CODE: TEST_CONFIRMATION_CODE,
        }
        response = self._sign_in(**sign_in_data, code=200)
        refresh_token = response.get("refreshToken")
        auth_token = response.get("authToken")
        self.assertIsNotNone(refresh_token)
        self.assertIsNotNone(auth_token)

        # 4. Check auth profile
        rsp = self._auth_profile({"authToken": auth_token}, code=200)
        created_user_id = rsp.get("uid")

        # 5. Get user profile and configuration
        self._get_user_route("user_v1.retrieve_user_profile", auth_token, created_user_id)
        self._get_user_route("user_v1.retrieve_deployment_config", auth_token, created_user_id)

    def test_user_mfa_signin(self):
        # 1. Sign in with email + password as first factor
        sign_in_data = {
            SignInRequestObject.METHOD: Method.TWO_FACTOR_AUTH,
            SignInRequestObject.EMAIL: USER_EMAIL,
            SignInRequestObject.PASSWORD: USER_PASSWORD,
        }
        response = self._sign_in(**sign_in_data, code=200)
        refresh_token = response.get("refreshToken")
        self.assertIsNotNone(refresh_token)

        # 2. Send confirmation code to user's phone number with refresh token
        send_phone_number_confirmation_data = {
            SendVerificationTokenRequestObject.METHOD: SendVerificationTokenMethod.TWO_FACTOR_AUTH,
            SendVerificationTokenRequestObject.REFRESH_TOKEN: refresh_token,
        }
        response = self._send_verification_token(**send_phone_number_confirmation_data, code=200)
        masked_phone = mask_phone_number(USER_PHONE_NUMBER)
        self.assertIn("to", response)
        self.assertEqual(masked_phone, response["to"])

        # 3. Sign in with refresh token from step 1 and code from step 2
        second_factor_sign_in_data = {
            SignInRequestObject.METHOD: Method.TWO_FACTOR_AUTH,
            SignInRequestObject.REFRESH_TOKEN: refresh_token,
            SignInRequestObject.CONFIRMATION_CODE: "123",
            "type": "test",  # to check if ignores unknown fields
        }
        response = self._sign_in(**second_factor_sign_in_data, code=200)
        # Verify tokens are valid for MFA
        refresh_token = response.get("refreshToken")
        auth_token = response.get("authToken")
        self.assertIsNotNone(refresh_token)
        self.assertIsNotNone(auth_token)
        self._check_tokens_valid_for_mfa(auth_token, refresh_token)

    def test_signin_same_email_uppercase(self):
        # 1. Sign in with email uppercase
        sign_in_data = {
            SignInRequestObject.METHOD: Method.TWO_FACTOR_AUTH,
            SignInRequestObject.EMAIL: USER_EMAIL.upper(),
            SignInRequestObject.PASSWORD: USER_PASSWORD,
        }
        self._sign_in(**sign_in_data, code=200)

    def test_old_user_migrate_and_sign_in_email(self):
        existing_user_with_email_only = "<EMAIL>"
        new_user_phone_number = "+380500000000"

        # 1. Sign in with email + code
        sign_in_data = {
            SignInRequestObject.METHOD: Method.EMAIL,
            SignInRequestObject.EMAIL: existing_user_with_email_only,
            SignInRequestObject.CONFIRMATION_CODE: "123",
            SignInRequestObject.CLIENT_ID: "ctest1",
            SignInRequestObject.PROJECT_ID: "ptest1",
        }
        response = self._sign_in(sign_in_data, code=200)
        refresh_token = response.get("refreshToken")
        auth_token = response.get("authToken")
        self.assertIsNotNone(refresh_token)
        self.assertIsNotNone(auth_token)

        # 2. Check that user is not eligible for MFA
        self._check_user_eligible_for_mfa(email=existing_user_with_email_only, eligible=False)

        # 3. Set missing attributes
        set_auth_attributes_data = {
            SetAuthAttributesRequestObject.PHONE_NUMBER: new_user_phone_number,
            SetAuthAttributesRequestObject.PASSWORD: "Aa123456",
        }
        self._set_auth_attr(auth_token, code=200, **set_auth_attributes_data)

        # 4. Send phone number confirmation by flow
        # 5. Confirm phone number and get auth token from confirmation response.
        phone_number_confirmation_data = {
            ConfirmationRequestObject.PHONE_NUMBER: new_user_phone_number,
            ConfirmationRequestObject.EMAIL: existing_user_with_email_only,
            ConfirmationRequestObject.CONFIRMATION_CODE: "123",
            ConfirmationRequestObject.CLIENT_ID: "ctest1",
            ConfirmationRequestObject.PROJECT_ID: "ptest1",
        }
        response = self._confirm_identity(phone_number_confirmation_data, code=200)
        refresh_token = response.get("refreshToken")
        auth_token = response.get("authToken")
        self.assertIsNotNone(refresh_token)
        self.assertIsNotNone(auth_token)

        # Confirming tokens are valid for MFA
        self._check_tokens_valid_for_mfa(auth_token, refresh_token)

        # Confirming that user is also eligible for MFA at this moment
        self._check_user_eligible_for_mfa(email=existing_user_with_email_only)

    def test_old_user_migrate_and_sign_in_phone_number(self):
        existing_user_with_phone_number_only = "+************"
        new_user_email = "<EMAIL>"

        # 1. Sign in with phonenumber + code
        sign_in_data = {
            SignInRequestObject.METHOD: Method.PHONE_NUMBER,
            SignInRequestObject.PHONE_NUMBER: existing_user_with_phone_number_only,
            SignInRequestObject.CONFIRMATION_CODE: "123",
        }
        sign_in_response = self._sign_in(**sign_in_data, code=200)
        refresh_token = sign_in_response.get("refreshToken")
        auth_token = sign_in_response.get("authToken")
        self.assertIsNotNone(refresh_token)
        self.assertIsNotNone(auth_token)

        # 2. Check that user is not eligible for MFA
        self._check_user_eligible_for_mfa(phone_number=existing_user_with_phone_number_only, eligible=False)

        # 3. Set missing attributes
        set_auth_attributes_data = {
            SetAuthAttributesRequestObject.EMAIL: new_user_email,
            SetAuthAttributesRequestObject.PASSWORD: "Aa123456",
            SetAuthAttributesRequestObject.AUTH_TOKEN: auth_token,
            SetAuthAttributesRequestObject.CLIENT_ID: CLIENT_ID_3,
            SetAuthAttributesRequestObject.PROJECT_ID: PROJECT_ID,
        }
        self._set_auth_attr(auth_token, code=200, **set_auth_attributes_data)

        # 4. Send email confirmation (4) by flow
        send_email_confirmation_data = {
            SendVerificationTokenRequestObject.METHOD: SendVerificationTokenMethod.EXISTING_USER_EMAIL_CONFIRMATION,
            SendVerificationTokenRequestObject.EMAIL: new_user_email,
            SendVerificationTokenRequestObject.CLIENT_ID: "ctest1",
            SendVerificationTokenRequestObject.PROJECT_ID: "ptest1",
        }
        self._send_verification_token(send_email_confirmation_data, code=200)

        # 5. Confirm email and get auth token from confirmation response.
        token_adapter = inject.instance(TokenAdapter)
        confirmation_token = token_adapter.create_confirmation_token(new_user_email)
        email_confirmation_data = {
            ConfirmationRequestObject.EMAIL: new_user_email,
            ConfirmationRequestObject.CONFIRMATION_CODE: confirmation_token,
            ConfirmationRequestObject.CLIENT_ID: "ctest1",
            ConfirmationRequestObject.PROJECT_ID: "ptest1",
        }
        confirmation_response = self._confirm_identity(email_confirmation_data, code=200)
        refresh_token = confirmation_response.get("refreshToken")
        auth_token = confirmation_response.get("authToken")
        self.assertIsNotNone(refresh_token)
        self.assertIsNotNone(auth_token)

        # Confirming tokens are valid for MFA
        self._check_tokens_valid_for_mfa(auth_token, refresh_token)

        # Confirming that user is also eligible for MFA at this moment
        self._check_user_eligible_for_mfa(phone_number=existing_user_with_phone_number_only)

    def test_check_clinician_mfa_after_sign_up(self):
        sign_up_data = {
            SignUpRequestObject.EMAIL: NOT_EXISTING_EMAIL,
            SignUpRequestObject.CLIENT_ID: "c3",
            SignUpRequestObject.PASSWORD: "Test123456",
            SignUpRequestObject.PROJECT_ID: PROJECT_ID,
            SignUpRequestObject.METHOD: 3,
            SignUpRequestObject.USER_ATTRIBUTES: {
                "givenName": "Temp",
                "familyName": "Temp",
            },
            SignUpRequestObject.VALIDATION_DATA: {"invitationCode": VALID_CLINICIAN_INVITATION_CODE},
        }

        response = self._sign_up(sign_up_data, code=200)
        user_id = response["uid"]
        auth_user = AuthUser.objects.get(mongoId=user_id)
        self.assertFalse(auth_user.mfaEnabled)

        auth_token = self.create_token(user_id, user_claims={"authStage": 1})

        response = self._check_auth_attr(auth_token, clientId="c3", code=200)
        self.assertFalse(response["mfaEnabled"])

    def test_device_token_attribute(self):
        existing_user_with_phone_number_only = "+************"

        # 1. Sign in with phonenumber + code
        sign_in_data = {
            SignInRequestObject.METHOD: Method.PHONE_NUMBER,
            SignInRequestObject.PHONE_NUMBER: existing_user_with_phone_number_only,
            SignInRequestObject.CONFIRMATION_CODE: "123",
        }
        response = self._sign_in(**sign_in_data, code=200)
        refresh_token = response.get("refreshToken")
        auth_token = response.get("authToken")
        self.assertIsNotNone(refresh_token)
        self.assertIsNotNone(auth_token)

        # 2. Check that user is not eligible for MFA
        self._check_user_eligible_for_mfa(phone_number=existing_user_with_phone_number_only, eligible=False)

        device_token = "device token"
        # 3. Set missing attributes
        set_auth_attributes_data = {SetAuthAttributesRequestObject.DEVICE_TOKEN: device_token}
        response = self._set_auth_attr(auth_token, code=200, **set_auth_attributes_data)

        db_user = AuthUser.objects.get(mongoId=response["uid"])
        auth_user = AuthUserDTO.from_dict(model_to_dict(db_user), use_validator_field=False)
        self.assertIsNotNone(auth_user.get_mfa_identifier(AuthIdentifierType.DEVICE_TOKEN, device_token))

        response = self._set_auth_attr(auth_token, code=200, **set_auth_attributes_data)

        new_db_user = AuthUser.objects.get(mongoId=response["uid"])
        self.assertListEqual(db_user.mfaIdentifiers, new_db_user.mfaIdentifiers)

    def test_mfa_NOT_enabled_v1_remember_me(self):
        # 1. Step 1 sign in with token stage 1
        sign_in_data = {
            SignInRequestObject.METHOD: Method.TWO_FACTOR_AUTH,
            SignInRequestObject.EMAIL: "<EMAIL>",
            SignInRequestObject.PASSWORD: USER_PASSWORD,
            SignInRequestObject.CLIENT_ID: CLIENT_ID_3,
            SignInRequestObject.PROJECT_ID: PROJECT_ID,
        }
        response = self._sign_in(sign_in_data, code=200)
        refresh_token = response.get("refreshToken")
        self.assertIsNotNone(refresh_token)

        response = self._refresh_token(
            {
                RefreshTokenRequestObjectV1.REFRESH_TOKEN: refresh_token,
                RefreshTokenRequestObjectV1.PASSWORD: USER_PASSWORD,
            },
            code=200,
        )
        updated_refresh_token = response.get("refreshToken")

        # 2. Step 2 sign in using remember me with token stage 1
        second_factor_sign_in_data = {
            SignInRequestObject.METHOD: Method.TWO_FACTOR_AUTH,
            SignInRequestObject.REFRESH_TOKEN: updated_refresh_token,
            SignInRequestObject.EMAIL: "<EMAIL>",
            SignInRequestObject.PASSWORD: USER_PASSWORD,
            SignInRequestObject.CLIENT_ID: CLIENT_ID_3,
            SignInRequestObject.PROJECT_ID: PROJECT_ID,
        }
        response = self._sign_in(second_factor_sign_in_data, code=200)
        refresh_token = response.get("refreshToken")
        self.assertIsNotNone(refresh_token)

    def test_user_mfa_signin_v1_remember_me(self):
        # 1. Sign in with email + password as first factor
        sign_in_data = {
            SignInRequestObject.METHOD: Method.TWO_FACTOR_AUTH,
            SignInRequestObject.EMAIL: USER_EMAIL,
            SignInRequestObject.PASSWORD: USER_PASSWORD,
        }
        response = self._sign_in(**sign_in_data, code=200)
        refresh_token = response.get("refreshToken")
        self.assertIsNotNone(refresh_token)

        # 2. Send confirmation code to user's phone number with refresh token
        send_phone_number_confirmation_data = {
            SendVerificationTokenRequestObject.METHOD: SendVerificationTokenMethod.TWO_FACTOR_AUTH,
            SendVerificationTokenRequestObject.REFRESH_TOKEN: refresh_token,
        }
        response = self._send_verification_token(**send_phone_number_confirmation_data)
        masked_phone = mask_phone_number(USER_PHONE_NUMBER)
        self.assertEqual(masked_phone, response.get("to"))

        # 3. Sign in with refresh token from step 1 and code from step 2
        second_factor_sign_in_data = {
            SignInRequestObject.METHOD: Method.TWO_FACTOR_AUTH,
            SignInRequestObject.REFRESH_TOKEN: refresh_token,
            SignInRequestObject.CONFIRMATION_CODE: "123",
        }
        response = self._sign_in(**second_factor_sign_in_data, code=200)
        # Verify tokens are valid for MFA
        refresh_token = response.get("refreshToken")
        auth_token = response.get("authToken")
        self.assertIsNotNone(refresh_token)
        self.assertIsNotNone(auth_token)
        self._check_tokens_valid_for_mfa(auth_token, refresh_token)

        # check refreshtoken with device token
        device_token = "device token"
        self._set_auth_attr(auth_token, code=200, deviceToken=device_token)

        data = {
            RefreshTokenRequestObjectV1.REFRESH_TOKEN: refresh_token,
            RefreshTokenRequestObjectV1.DEVICE_TOKEN: device_token,
        }
        response = self._refresh_token(data, code=200)
        new_refresh_token = response.get("refreshToken")
        # Verify tokens are valid for MFA
        self._check_tokens_valid_for_mfa(response.get("authToken"), new_refresh_token)

        # remember me duration
        second_factor_sign_in_data = {
            SignInRequestObject.METHOD: Method.TWO_FACTOR_AUTH,
            SignInRequestObject.REFRESH_TOKEN: new_refresh_token,
            SignInRequestObject.EMAIL: USER_EMAIL,
            SignInRequestObject.PASSWORD: USER_PASSWORD,
        }
        in_30_minutes = datetime.utcnow() + timedelta(minutes=30)
        with freeze_time(in_30_minutes):
            remember_response = self._sign_in(**second_factor_sign_in_data, code=200)
            # Verify tokens are valid for MFA
            remember_refresh_token = remember_response.get("refreshToken")
            remember_auth_token = remember_response.get("authToken")
            self.assertIsNotNone(remember_refresh_token)
            self.assertIsNotNone(remember_auth_token)
            # Verify if refreshtoken expiresIn not renewed

            # difference is not more than 5 seconds
            self.assertAlmostEqual(response["expiresIn"] - 30 * 60, remember_response["expiresIn"], delta=5)
            self._check_tokens_valid_for_mfa(remember_auth_token, remember_refresh_token)

            # confirming you still can refresh token with updated token
            data = {
                RefreshTokenRequestObjectV1.REFRESH_TOKEN: remember_refresh_token,
                RefreshTokenRequestObjectV1.DEVICE_TOKEN: device_token,
            }
            self._refresh_token(data, code=200)

    def test_check_auth_attributes_returns_error_if_inv_exists_and_flags_disabled(self):
        self.test_server.config.server.improvedOnboarding = False

        response = self._check_user_eligible_for_mfa(
            phone_number=EXISTING_INVITATION_PHONE_NUMBER, eligible=None, code=401
        )
        self.assertEqual("Unauthorized User", response["message"])
        self.test_server.config.server.improvedOnboarding = True

    def test_check_auth_attributes_returns_error_no_user_no_inv(self):
        response = self._check_user_eligible_for_mfa(phone_number=NON_EXISTING_PHONE_NUMBER, eligible=None, code=401)
        self.assertEqual("Unauthorized User", response["message"])

    def test_signup_with_invitation_raises_error_when_flags_disabled(self):
        self.test_server.config.server.improvedOnboarding = False
        sign_up_data = {
            SignUpRequestObject.METHOD: 1,
            SignUpRequestObject.PHONE_NUMBER: EXISTING_INVITATION_PHONE_NUMBER,
            SignUpRequestObject.VALIDATION_DATA: {"shortenedCode": VALID_SHORT_CODE},
            SignUpRequestObject.CLIENT_ID: TEST_CLIENT_ID,
            SignUpRequestObject.PROJECT_ID: PROJECT_ID,
        }
        rsp = self.flask_client.post("/api/auth/v1/signup", json=sign_up_data)
        self.assertEqual(405, rsp.status_code)
        self.assertEqual("Improved onboarding flags not active", rsp.json["message"])
        self.assertEqual(300049, rsp.json["code"])
        self.test_server.config.server.improvedOnboarding = True

    def test_check_auth_attributes_returns_invitation_code_if_exists(self):
        deployment_id = "5d386cc6ff885918d96edb2c"

        # 1. Check phoneNumber and retrieve invitationCode for this phoneNumber
        response = self._check_user_eligible_for_mfa(phone_number=EXISTING_INVITATION_PHONE_NUMBER, eligible=False)
        self.assertFalse(response.get("mfaEnabled"))
        self.assertFalse(response.get("passwordSet"))
        self.assertEqual(VALID_SHORT_CODE, response.get("shortenedCode"))

        # 2. Sign Up user with invitationCode
        sign_up_data = {
            SignUpRequestObject.METHOD: 1,
            SignUpRequestObject.PHONE_NUMBER: EXISTING_INVITATION_PHONE_NUMBER,
            SignUpRequestObject.VALIDATION_DATA: {"shortenedCode": VALID_SHORT_CODE},
            SignUpRequestObject.CLIENT_ID: TEST_CLIENT_ID,
            SignUpRequestObject.PROJECT_ID: PROJECT_ID,
        }
        response = self._sign_up(sign_up_data, code=200)
        created_user_id = response.get("uid")
        self.assertIsNotNone(created_user_id)

        user = AuthorizationService().retrieve_simple_user_profile(created_user_id)
        user_role = user.roles[0]
        self.assertEqual(INV_EMAIL, user.notification_email)
        self.assertEqual(EXISTING_INVITATION_PHONE_NUMBER, user.phoneNumber)
        self.assertEqual(INV_GIVEN_NAME, user.givenName)
        self.assertEqual(INV_FAMILY_NAME, user.familyName)
        self.assertEqual(RoleName.USER, user_role.roleId)
        self.assertEqual(f"deployment/{deployment_id}", user_role.resource)
        self.assertEqual(RoleDTO.UserType.USER, user_role.userType)
        self.assertTrue(user_role.isActive)

    def test_check_auth_attributes_call_for_invitation_by_web_manager(self):
        response = self._check_user_eligible_for_mfa(
            phone_number=EXISTING_INVITATION_PHONE_NUMBER,
            eligible=False,
            clientId=WEB_MANAGER_CLIENT_ID,
        )
        self.assertFalse(response.get("mfaEnabled"))
        self.assertFalse(response.get("passwordSet"))
        self.assertEqual(VALID_SHORT_CODE, response.get("shortenedCode"))

    @patch.object(PyJWS, "_verify_signature")
    @patch("sdk.auth.tests.mock_objects.MockEmailVerificationAdapter.verify_code")
    def test_verify_with_static_code_on_test_server(self, verify_code, _):
        # 1. Sign up
        new_user_email = TEST_EMAIL
        new_user_phone_number = "+380500000000"
        signup_data = {
            SignUpRequestObject.METHOD: Method.TWO_FACTOR_AUTH,
            SignUpRequestObject.EMAIL: new_user_email,
            SignUpRequestObject.PASSWORD: USER_PASSWORD,
            SignUpRequestObject.DISPLAY_NAME: "test",
            SignUpRequestObject.VALIDATION_DATA: {"invitationCode": INVITATION_CODE},
            SignUpRequestObject.USER_ATTRIBUTES: {
                "familyName": "hey",
                "givenName": "test",
                "dob": "1988-02-20",
                "gender": "MALE",
            },
            SignUpRequestObject.CLIENT_ID: "ctest1",
            SignUpRequestObject.PROJECT_ID: "ptest1",
        }
        self._sign_up(signup_data, code=200)

        # 2. Sign in with email and password
        sign_in_data = {
            SignInRequestObject.METHOD: Method.EMAIL_PASSWORD,
            SignInRequestObject.EMAIL: new_user_email,
            SignUpRequestObject.PASSWORD: USER_PASSWORD,
            SignUpRequestObject.PROJECT_ID: PROJECT_ID,
            SignUpRequestObject.CLIENT_ID: TEST_CLIENT_ID,
        }
        sign_in_rsp = self._sign_in(sign_in_data, code=200)
        refresh_token = sign_in_rsp.get("refreshToken")
        auth_token = sign_in_rsp.get("authToken")
        self.assertIsNotNone(refresh_token)
        self.assertIsNotNone(auth_token)
        # Confirming tokens are not valid for MFA
        self._check_tokens_valid_for_mfa(auth_token, refresh_token, valid=False)

        # 3. Set auth attributes with auth token from sign in step
        self._set_auth_attr(token=auth_token, phoneNumber=new_user_phone_number, clientId=CLIENT_ID_3)

        # 4. Send phone number confirmation
        send_phone_number_confirmation_data = {
            SendVerificationTokenRequestObject.METHOD: SendVerificationTokenMethod.PHONE_NUMBER,
            SendVerificationTokenRequestObject.PHONE_NUMBER: new_user_phone_number,
        }
        self._send_verification_token(**send_phone_number_confirmation_data, code=200)

        # 5. Confirm phone number by static code.
        phone_number_confirmation_data = {
            ConfirmationRequestObject.PHONE_NUMBER: new_user_phone_number,
            ConfirmationRequestObject.EMAIL: new_user_email,
            ConfirmationRequestObject.CONFIRMATION_CODE: TEST_CONFIRMATION_CODE,
            ConfirmationRequestObject.CLIENT_ID: TEST_CLIENT_ID,
            ConfirmationRequestObject.PROJECT_ID: PROJECT_ID,
        }
        self._confirm_identity(phone_number_confirmation_data, code=200)
        verify_code.assert_not_called()

    def _check_tokens_valid_for_mfa(self, auth_token=None, refresh_token=None, valid=True):
        if auth_token:
            decoded_auth_token = self.token_adapter.verify_token(auth_token, "access")
            auth_stage = decoded_auth_token[USER_CLAIMS_KEY][AUTH_STAGE_KEY]
            auth_stage_equal = auth_stage == AuthStage.SECOND
            self.assertEqual(auth_stage_equal, valid)
        if refresh_token:
            decoded_ref_token = self.token_adapter.verify_token(refresh_token, "refresh")
            ref_auth_stage = decoded_ref_token[USER_CLAIMS_KEY][AUTH_STAGE_KEY]
            refresh_stage_equal = ref_auth_stage == AuthStage.SECOND
            self.assertEqual(refresh_stage_equal, valid)

    def _check_user_eligible_for_mfa(
        self,
        email=None,
        phone_number=None,
        eligible: bool | None = True,
        code=200,
        **kwargs,
    ):
        response = self._check_auth_attr(None, email=email, phoneNumber=phone_number, code=code, **kwargs)
        if eligible is not None:
            self.assertEqual(response["eligibleForMFA"], eligible)
        return response

    def _get_user_route(self, endpoint: str, auth_token: str, created_user_id: str) -> dict:
        url = url_for(endpoint, user_id=created_user_id)
        rsp = self.flask_client.get(url, headers={"Authorization": f"Bearer {auth_token}"})
        self.assertEqual(200, rsp.status_code)
        return rsp.json


class VerifyCredentialsTestCase(BaseSignUpTestCase, AuthTestMixin):
    components = [AuthComponent()]
    fixtures = [Path(__file__).parent.joinpath("fixtures/mfa_user_dump.json")]
    CORRECT_CODE = "123"

    def setUp(self):
        super().setUp()
        self.token = self.create_token(USER_ID)
        self.sms_adapter = MockSmsVerificationAdapter(code=self.CORRECT_CODE)

        def bind(binder):
            binder.bind("aliCloudSmsVerificationAdapter", self.sms_adapter)
            binder.bind("twilioSmsVerificationAdapter", self.sms_adapter)

        inject.get_injector_or_die().rebind(bind)

    def test_verify_password_success(self):
        self._verify_creds(password=USER_PASSWORD, authToken=self.token, code=200)

    def test_verify_password_error(self):
        wrong_password = f"{USER_PASSWORD}123"
        response = self._verify_creds(password=wrong_password, authToken=self.token, code=403)
        self.assertEqual("Invalid Password", response["message"])

    def test_verify_confirmation_code(self):
        self._verify_creds(
            confirmationCode="123",
            phoneNumber=USER_PHONE_NUMBER,
            type=VerificationType.PHONE_CONFIRMATION_CODE,
            authToken=self.token,
            code=200,
        )

    def test_verify_confirmation_code_wrong_code(self):
        response = self._verify_creds(
            confirmationCode="567",
            phoneNumber=USER_PHONE_NUMBER,
            type=VerificationType.PHONE_CONFIRMATION_CODE,
            authToken=self.token,
            code=400,
        )
        self.assertEqual("Invalid verification code", response["message"])


if __name__ == "__main__":
    unittest.main()
