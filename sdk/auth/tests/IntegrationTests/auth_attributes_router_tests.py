from datetime import datetime
from pathlib import Path
from unittest.mock import MagicMock

from dateutil.relativedelta import relativedelta
from freezegun import freeze_time

from sdk.auth.component import AuthComponent
from sdk.auth.model.auth_user import AuthUser
from sdk.auth.tests.IntegrationTests._mixins import AuthTestMixin
from sdk.auth.tests.mock_objects import (
    MockEmailVerificationAdapter,
    MockSmsVerificationAdapter,
)
from sdk.auth.tests.test_helpers import (
    CONFIRMATION_CODE,
    USER_EMAIL,
    USER_ID,
    USER_PASSWORD,
    USER_PHONE_NUMBER,
    phone_number_sign_in_request,
)
from sdk.auth.use_case import (
    CheckAuthAttributesResponseObject,
    ConfirmationRequestObject,
    RefreshTokenRequestObjectV1,
    SetAuthAttributesResponseObject,
)
from sdk.authorization.component import AuthorizationComponent
from sdk.common.adapter.email_confirmation_adapter import EmailConfirmationAdapter
from sdk.common.adapter.email_verification_adapter import EmailVerificationAdapter
from sdk.common.adapter.token_adapter import TokenType
from sdk.common.exceptions.exceptions import ErrorCodes
from sdk.common.utils import inject
from sdk.common.utils.inject import Binder
from sdk.deployment.component import DeploymentComponent
from sdk.notification.component import NotificationComponent
from sdk.organization.component import OrganizationComponent
from sdk.phoenix.config.project_config import Client
from sdk.tests.test_case import SdkTestCase

NEW_PHONE_NUMBER = "+447500000010"


def get_mock_email_password_adapter():
    adapter = MagicMock()
    adapter.send_confirmation_email.return_value = None
    adapter.verify_code.return_value = True
    return adapter


class AuthAttributesTestCase(SdkTestCase, AuthTestMixin):
    components = [
        AuthComponent(),
        NotificationComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        OrganizationComponent(),
    ]
    fixtures = [Path(__file__).parent.joinpath("fixtures/auth_users_dump.json")]

    @classmethod
    def setUpClass(cls) -> None:
        super().setUpClass()
        cls.email_confirmation_adapter = get_mock_email_password_adapter()
        cls.email_verification_adapter = MockEmailVerificationAdapter(CONFIRMATION_CODE)
        cls.sms_adapter = MockSmsVerificationAdapter()

        def configure_with_binder(binder: Binder):
            binder.bind(EmailVerificationAdapter, cls.email_verification_adapter)
            binder.bind(EmailConfirmationAdapter, cls.email_confirmation_adapter)
            binder.bind("aliCloudSmsVerificationAdapter", cls.sms_adapter)
            binder.bind("twilioSmsVerificationAdapter", cls.sms_adapter)

        inject.get_injector_or_die().rebind(configure_with_binder)

    def tearDown(self):
        self.email_confirmation_adapter.reset_mock()
        return super().tearDown()

    def sign_in(self):
        response = self._sign_in(email=USER_EMAIL, confirmationCode=CONFIRMATION_CODE)
        return response["authToken"], response["refreshToken"]

    def test_success_refresh_token_empty_http_user_agent(self):
        response = self._sign_in(email=USER_EMAIL, confirmationCode=CONFIRMATION_CODE)
        self._refresh_token({RefreshTokenRequestObjectV1.REFRESH_TOKEN: response["refreshToken"]})

    def test_check_auth_attributes(self):
        auth_token = self.create_token(USER_ID)

        response = self._check_auth_attr(auth_token)
        expected = {
            CheckAuthAttributesResponseObject.ELIGIBLE_FOR_MFA: False,
            CheckAuthAttributesResponseObject.EMAIL: USER_EMAIL,
            CheckAuthAttributesResponseObject.EMAIL_VERIFIED: False,
            CheckAuthAttributesResponseObject.MFA_ENABLED: False,
            CheckAuthAttributesResponseObject.PASSWORD_SET: False,
            CheckAuthAttributesResponseObject.PHONE_NUMBER: "+441700000000",
            CheckAuthAttributesResponseObject.PHONE_NUMBER_VERIFIED: False,
        }
        self.assertEqual(expected, response)

    def test_set_auth_attributes(self):
        test_phone_number = "+441347722096"

        auth_token = self.create_token(USER_ID)
        self._set_auth_attr(auth_token, phoneNumber=test_phone_number, password=USER_PASSWORD)
        response = self._check_auth_attr(auth_token)
        self.assertTrue(response["passwordSet"])
        self.assertFalse(response["emailVerified"])
        self.assertEqual(test_phone_number, response["phoneNumber"])
        self.assertEqual(USER_EMAIL, response["email"])

        # testing that still may re set while it's not verified
        self._set_auth_attr(auth_token, phoneNumber=NEW_PHONE_NUMBER)
        response = self._check_auth_attr(auth_token)
        self.assertEqual(NEW_PHONE_NUMBER, response["phoneNumber"])

    def test_success_set_auth_attributes_update_verified_phone_number(self):
        rsp = phone_number_sign_in_request(self.flask_client, USER_PHONE_NUMBER, CONFIRMATION_CODE)
        self.assertEqual(rsp.status_code, 200)
        auth_token = rsp.json["authToken"]

        # trying to update user attr that has verified phone number
        self._set_auth_attr(
            auth_token,
            phoneNumber=NEW_PHONE_NUMBER,
            confirmationCode=CONFIRMATION_CODE,
        )
        response = self._check_auth_attr(auth_token)
        self.assertEqual(response["phoneNumber"], NEW_PHONE_NUMBER)

    def test_failure_set_verified_phone_number_use_same_phone_number(self):
        rsp = phone_number_sign_in_request(self.flask_client, USER_PHONE_NUMBER, CONFIRMATION_CODE)
        self.assertEqual(rsp.status_code, 200)
        auth_token = rsp.json["authToken"]

        # trying to update with the same phone number
        response = self._set_auth_attr(
            auth_token,
            phoneNumber=USER_PHONE_NUMBER,
            confirmationCode=CONFIRMATION_CODE,
            code=400,
        )
        self.assertEqual(ErrorCodes.PHONE_NUMBER_ALREADY_SET, response["code"])

    def test_failure_set_verified_phone_number_no_confirmation_code_provided(self):
        rsp = phone_number_sign_in_request(self.flask_client, USER_PHONE_NUMBER, CONFIRMATION_CODE)
        self.assertEqual(rsp.status_code, 200)
        auth_token = rsp.json["authToken"]

        # trying to update user attr that has verified phone number
        response = self._set_auth_attr(auth_token, phoneNumber=NEW_PHONE_NUMBER, code=400)
        self.assertEqual(ErrorCodes.CONFIRMATION_CODE_IS_MISSING, response["code"])

    def test_set_verified_phone_number_from_unverified(self):
        auth_token = self.create_token(USER_ID)
        self._set_auth_attr(
            auth_token,
            phoneNumber=NEW_PHONE_NUMBER,
            confirmationCode=CONFIRMATION_CODE,
        )

        response = self._check_auth_attr(auth_token)
        self.assertEqual(response["phoneNumber"], NEW_PHONE_NUMBER)
        self.assertTrue(response["phoneNumberVerified"])

    def test_set_password_updates_password_update_datetime(self):
        auth_token, refresh_token = self.sign_in()

        # checking current update password datetime
        user = AuthUser.objects.get(mongoId=USER_ID)
        password_update_datetime = user.passwordUpdateDateTime
        password_create_datetime = user.passwordCreateDateTime
        self.assertIsNone(password_update_datetime)
        self.assertIsNone(password_create_datetime)

        self._set_auth_attr(auth_token, password=USER_PASSWORD)

        user = AuthUser.objects.get(mongoId=USER_ID)
        new_update_date_time = user.passwordUpdateDateTime
        new_create_date_time = user.passwordCreateDateTime
        self.assertIsNotNone(new_update_date_time)
        self.assertNotEqual(password_update_datetime, new_update_date_time)
        self.assertEqual(new_create_date_time, new_update_date_time)

        self._set_password(refresh_token, USER_PASSWORD, "someNewPass1")

        user = AuthUser.objects.get(mongoId=USER_ID)
        new_update_date_time = user.passwordUpdateDateTime
        new_create_date_time = user.passwordCreateDateTime
        self.assertNotEqual(new_create_date_time, new_update_date_time)

    def test_update_to_recent_password_raises_exception(self):
        response = self._sign_in(email=USER_EMAIL, confirmationCode=CONFIRMATION_CODE)
        auth_token, refresh_token = response["authToken"], response["refreshToken"]

        pass_1 = "Aa123456"
        pass_2 = "Aa223456"
        pass_3 = "Aa323456"
        pass_4 = "Aa423456"
        self._set_auth_attr(auth_token, password=pass_1)
        self._set_password(refresh_token, pass_1, pass_2, code=200)

        response = self._sign_in(email=USER_EMAIL, confirmationCode=CONFIRMATION_CODE)
        auth_token, refresh_token = response["authToken"], response["refreshToken"]
        self._set_password(refresh_token, pass_2, pass_3, code=200)

        response = self._sign_in(email=USER_EMAIL, confirmationCode=CONFIRMATION_CODE)
        auth_token, refresh_token = response["authToken"], response["refreshToken"]
        self._set_password(refresh_token, pass_3, pass_4, code=200)

        response = self._sign_in(email=USER_EMAIL, confirmationCode=CONFIRMATION_CODE)
        auth_token, refresh_token = response["authToken"], response["refreshToken"]

        response = self._set_password(refresh_token, pass_4, pass_2, code=400)
        self.assertEqual(ErrorCodes.ALREADY_USED_PASSWORD, response["code"])

        self._set_password(refresh_token, pass_4, pass_1, code=200)

    def test_change_password(self):
        self.test_server.config.server.project.clients[0].authType = Client.AuthType.SFA
        # signing in 30 seconds earlier
        with freeze_time(datetime.utcnow() - relativedelta(seconds=30)):
            response = self._sign_in(email=USER_EMAIL, confirmationCode=CONFIRMATION_CODE)
            auth_token, refresh_token = response["authToken"], response["refreshToken"]
        # initial set password
        response = self._set_auth_attr(auth_token, password=USER_PASSWORD)
        self.assertNotIn(SetAuthAttributesResponseObject.REFRESH_TOKEN, response)
        self.email_confirmation_adapter.send_password_changed_email.assert_not_called()

        # changing password
        new_password = "Some1234"
        response = self._set_password(refresh_token, USER_PASSWORD, new_password)
        self.email_confirmation_adapter.send_password_changed_email.assert_called_once()
        new_refresh_token = response.get(SetAuthAttributesResponseObject.REFRESH_TOKEN)
        self.assertIsNotNone(new_refresh_token)

        # testing that no access with old auth token
        response = self._retrieve_devices(USER_ID, code=401)
        self.assertEqual(ErrorCodes.TOKEN_EXPIRED, response["code"])

        # confirming cannot refresh old token
        response = self._refresh_token({RefreshTokenRequestObjectV1.REFRESH_TOKEN: refresh_token}, code=401)
        self.assertEqual(ErrorCodes.TOKEN_EXPIRED, response["code"])

        # confirming can refresh new token from change password response
        self._refresh_token({RefreshTokenRequestObjectV1.REFRESH_TOKEN: new_refresh_token})

        # confirming cannot update password again with old token
        self._set_password(refresh_token, USER_PASSWORD, new_password, code=401)

    def test_set_mfa_phone_number_not_sending_emails(self):
        _, refresh_token = self.sign_in()
        self._set_auth_attr(
            refresh_token,
            password=USER_PASSWORD,
            tokenType=TokenType.REFRESH.string_value,
            phoneNumber=NEW_PHONE_NUMBER,
        )
        self.email_confirmation_adapter.send_mfa_phone_number_updated_email.assert_not_called()

    def test_only_change_mfa_phone_number_sending_emails(self):
        _, refresh_token = self.sign_in()
        self._set_auth_attr(
            refresh_token,
            tokenType=TokenType.REFRESH.string_value,
            phoneNumber=USER_PHONE_NUMBER,
        )

        phone_confirmation_data = {
            ConfirmationRequestObject.EMAIL: USER_EMAIL,
            ConfirmationRequestObject.PHONE_NUMBER: USER_PHONE_NUMBER,
            ConfirmationRequestObject.CONFIRMATION_CODE: "11111",
            ConfirmationRequestObject.CLIENT_ID: "ctest1",
            ConfirmationRequestObject.PROJECT_ID: "ptest1",
        }
        self._confirm_identity(phone_confirmation_data)
        self.email_confirmation_adapter.send_mfa_phone_number_updated_email.assert_not_called()

        # setting new phone number
        new_phone_number = "+447500000000"
        _, refresh_token = self.sign_in()
        self._set_auth_attr(
            refresh_token,
            tokenType=TokenType.REFRESH.string_value,
            phoneNumber=new_phone_number,
            confirmationCode="123456",
        )

        self.email_confirmation_adapter.send_mfa_phone_number_updated_email.assert_called_once()

    def test_set_contact_email(self):
        auth_token = self.create_token(USER_ID)

        contact_email = "<EMAIL>"
        self._set_auth_attr(
            auth_token,
            contactEmail=contact_email,
            confirmationCode=CONFIRMATION_CODE,
        )
        response = self._check_auth_attr(auth_token)
        self.assertEqual(contact_email, response["contactEmail"])

    def test_set_contact_email_with_test_code(self):
        auth_token = self.create_token(USER_ID)
        contact_email, test_code, wrong_code = "<EMAIL>", "010101", "333"
        self._set_auth_attr(
            auth_token,
            contactEmail=contact_email,
            confirmationCode=test_code,
        )
        response = self._check_auth_attr(auth_token)
        self.assertEqual(contact_email, response["contactEmail"])

        response = self._set_auth_attr(
            auth_token,
            contactEmail=contact_email,
            confirmationCode=wrong_code,
            code=400,
        )
        self.assertEqual(ErrorCodes.INVALID_VERIFICATION_CODE, response["code"])
