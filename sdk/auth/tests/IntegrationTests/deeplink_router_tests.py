from apiflask import APIFlask
from flask import url_for

from sdk.auth.component import AuthComponent
from sdk.auth.router.deeplink_request_objects import RedirectPathOptions
from sdk.tests.test_case import SdkTestCase

USER_PROFILE_ROUTER_PATH = "sdk.auth.router.private_auth_router"
SAMPLE_ID = "600a8476a961574fb38157d5"

testapp = APIFlask(__name__)


class DeeplinkRouterTestCase(SdkTestCase):
    components = [AuthComponent()]

    def test_success_redirect_with_query_params(self):
        path = "/signup?shortenedCode=12313123"
        rsp = self.flask_client.get(path)
        self.assertEqual(rsp.status_code, 302)
        self.assertEqual(url_for("deeplink_route.install_app_html_page"), rsp.location)

    def test_success_redirect_without_query_params(self):
        for option in RedirectPathOptions:
            path = f"/{option.value}"
            rsp = self.flask_client.get(path)
            self.assertEqual(302, rsp.status_code)
            self.assertEqual(url_for("deeplink_route.install_app_html_page"), rsp.location)

    def test_404_not_found_url(self):
        path = "random-path-asd-asd"
        rsp = self.flask_client.get(path)
        self.assertEqual(404, rsp.status_code)
        self.assertEqual("Not Found", rsp.json["message"])
