server:
  host: 0.0.0.0
  hostUrl: localhost
  port: 3901
  debug: true
  debugRouter: true
  testEnvironment: true
  releaseDate: "2022-08"
  supportEmail: "<EMAIL>"
  androidAppUrl: !ENV ${MP_ANDROID_APP_URL}
  iosAppUrl: !ENV ${MP_IOS_APP_URL}
  improvedOnboarding: true

  auditLogger:
    enable: true

  project:
    id: ptest1
    clients:
      - name: USER_IOS-client
        clientId: ctest1
        clientType: USER_IOS
        roleIds: ["User"]
        appIds: ["TeamId.com.huma.iosappid"]
        deepLinkBaseUrl: https://ios-url.com
        minimumVersion: 1.17.1
      - name: USER_ANDROID-client non expirable
        clientId: c2
        clientType: USER_ANDROID
        roleIds: ["User"]
        appIds: ["TeamId.com.huma.androidappid"]
        fingerprints: ["10:E1:03:22:12:25"]
        refreshTokenExpiresAfterMinutes: null
        deepLinkBaseUrl: https://android-url.com
        minimumVersion: 1.17.1
      - name: MANAGER_WEB-client
        clientId: c3
        clientType: MANAGER_WEB
        deepLinkBaseUrl: https://web-url.com
      - name: ADMIN_WEB-client
        clientId: c4
        clientType: ADMIN_WEB
        deepLinkBaseUrl: https://web-url.com
    masterKey: !ENV ${MP_MASTER_KEY}
    hashSalt: !ENV ${MP_HASH_SALT}

  metrics:
    enabled: false

  auth:
    enable: true
    database: pp_integration_tests_sdk
    signedUrlSecret: !ENV ${MP_SIGNED_URL_SECRET}
    humaProjectSsoConfig: >
      ewogICAgICAgICJuYW1lIjogIlNBTUwiLAogICAgICAgICJvcmciOiAiSHVtYSIsCiAgICAgICAgImluZm8iOiB7CiAgICAgICAgICAieG1sc
      2VjX2JpbmFyeSI6ICIvdXNyL2Jpbi94bWxzZWMxIiwKICAgICAgICAgICJlbnRpdHlpZCI6ICJodHRwczovL2xvY2FsaG9zdDozOTAxL2FwaS9
      hdXRoL3YxYmV0YS9zc29zaWduaW4vaWRlbnRpZmllciIsCiAgICAgICAgICAic2VydmljZSI6IHsKICAgICAgICAgICAgInNwIjogewogICAgI
      CAgICAgICAgICJuYW1lIjogIkZsYXNrIFNQIiwKICAgICAgICAgICAgICAiZW5kcG9pbnRzIjogewogICAgICAgICAgICAgICAgImFzc2VydGl
      vbl9jb25zdW1lcl9zZXJ2aWNlIjogWwogICAgICAgICAgICAgICAgICAiaHR0cHM6Ly9sb2NhbGhvc3Q6MzkwMS9hcGkvYXV0aC92MWJldGEvc
      3NvL2NhbGxiYWNrIiwKICAgICAgICAgICAgICAgICAgInVybjpvYXNpczpuYW1lczp0YzpTQU1MOjIuMDpiaW5kaW5nczpIVFRQLVBPU1QiCiA
      gICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAiYWxsb3dfdW5zb2xpY2l0ZWQiOiAidHJ1ZSIsCiAgICAgI
      CAgICAgICAgImF1dGhuX3JlcXVlc3RzX3NpZ25lZCI6ICJmYWxzZSIsCiAgICAgICAgICAgICAgImxvZ291dF9yZXF1ZXN0c19zaWduZWQiOiA
      idHJ1ZSIsCiAgICAgICAgICAgICAgIndhbnRfYXNzZXJ0aW9uc19zaWduZWQiOiAiZmFsc2UiLAogICAgICAgICAgICAgICJ3YW50X3Jlc3Bvb
      nNlX3NpZ25lZCI6ICJmYWxzZSIKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgICJtZXRhZGF0YSI6IHsKICAgICAgICAgICA
      gInJlbW90ZSI6IFsKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAidXJsIjogImh0dHBzOi8vbG9naW4ubWljcm9zb2Z0b25saW5lL
      mNvbS84YjkyZWI5OS1kZmEyLTRiYmUtYWU4Zi1kNmU2NmRhNDRkNmEvZmVkZXJhdGlvbm1ldGFkYXRhLzIwMDctMDYvZmVkZXJhdGlvbm1ldGF
      kYXRhLnhtbD9hcHBpZD1iNDRjYWM1Yy1kMDg2LTQwZGUtOTM1YS1jMWE0ZWYwN2Q4MTMiCiAgICAgICAgICAgICAgfQogICAgICAgICAgICBdC
      iAgICAgICAgICB9LAogICAgICAgICAgImtleV9maWxlIjogImtleV9wYXRoIiwKICAgICAgICAgICJjZXJ0X2ZpbGUiOiAiY2VydF9wYXRoIiw
      KICAgICAgICAgICJlbmNyeXB0aW9uX2tleXBhaXJzIjogWwogICAgICAgICAgICB7CiAgICAgICAgICAgICAgImtleV9maWxlIjogInBhdGgvd
      G8vcHJpdmF0ZS5rZXkiLAogICAgICAgICAgICAgICJjZXJ0X2ZpbGUiOiAicGF0aC90by9jZXJ0aWZpY2F0ZS5jcnQiCiAgICAgICAgICAgIH0
      KICAgICAgICAgIF0sCiAgICAgICAgICAib3JnYW5pemF0aW9uIjogewogICAgICAgICAgICAibmFtZSI6IFsKICAgICAgICAgICAgICBbCiAgI
      CAgICAgICAgICAgICAiRXhhbXBsZSIsCiAgICAgICAgICAgICAgICAiZW4iCiAgICAgICAgICAgICAgXQogICAgICAgICAgICBdLAogICAgICA
      gICAgICAiZGlzcGxheV9uYW1lIjogWwogICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICJFeGFtcGxlIiwKICAgICAgICAgICAgICAgI
      CJlbiIKICAgICAgICAgICAgICBdCiAgICAgICAgICAgIF0sCiAgICAgICAgICAgICJ1cmwiOiBbCiAgICAgICAgICAgICAgWwogICAgICAgICA
      gICAgICAgImh0dHA6Ly93d3cuZXhhbXBsZS5jb20iLAogICAgICAgICAgICAgICAgImVuIgogICAgICAgICAgICAgIF0KICAgICAgICAgICAgX
      QogICAgICAgICAgfSwKICAgICAgICAgICJjb250YWN0X3BlcnNvbiI6IHsKICAgICAgICAgICAgImdpdmVuX25hbWUiOiAiU3VwcG9ydCIsCiA
      gICAgICAgICAgICJlbWFpbF9hZGRyZXNzIjogInN1cHBvcnRAZXhhbXBsZS5jb20iLAogICAgICAgICAgICAiY29udGFjdF90eXBlIjogInRlY
      2huaWNhbCIKICAgICAgICAgIH0sCiAgICAgICAgICAiY2VydGlmaWNhdGVfY3J0IjogIi0tLS0tQkVHSU4gQ0VSVElGSUNBVEUtLS0tLVxuTUl
      JRC9UQ0NBdVdnQXdJQkFnSVVOcWVkOTIvdXlGOTRla04zRU5uL28wTTczTkl3RFFZSktvWklodmNOQVFFTFxuQlFBd2dZMHhDekFKQmdOVkJBW
      VRBbFZMTVE4d0RRWURWUVFJREFaTWIyNWtiMjR4RHpBTkJnTlZCQWNNQmt4dlxuYm1SdmJqRU5NQXNHQTFVRUNnd0VTSFZ0WVRFUk1BOEdBMVV
      FQ3d3SVNIVnRZU0JUVTA4eEVUQVBCZ05WQkFNTVxuQ0ZSMWNteHZkV2RvTVNjd0pRWUpLb1pJaHZjTkFRa0JGaGgwZFhKc2IzVm5hQzV2YUdGb
      llXNUFhSFZ0WVM1alxuYjIwd0hoY05NalF3TkRNd01UTTFOek13V2hjTk1qVXdORE13TVRNMU56TXdXakNCalRFTE1Ba0dBMVVFQmhNQ1xuVlV
      zeER6QU5CZ05WQkFnTUJreHZibVJ2YmpFUE1BMEdBMVVFQnd3R1RHOXVaRzl1TVEwd0N3WURWUVFLREFSSVxuZFcxaE1SRXdEd1lEVlFRTERBa
      ElkVzFoSUZOVFR6RVJNQThHQTFVRUF3d0lWSFZ5Ykc5MVoyZ3hKekFsQmdrcVxuaGtpRzl3MEJDUUVXR0hSMWNteHZkV2RvTG05b1lXZGhia0J
      vZFcxaExtTnZiVENDQVNJd0RRWUpLb1pJaHZjTlxuQVFFQkJRQURnZ0VQQURDQ0FRb0NnZ0VCQUpZT1dGQ0hqOU9pbndCMXN3bkxsbzFuL2ZmO
      DJOZHZGcjlkVnUzWFxuSlZsY3hlZVZoVWp3TUY0R3piY1AvVG5EOW9CK3JxWC9JNnFZK1pwVEc0MWFtb2czRDdxSW10dUFhN2g4Z0FSL1xuN3J
      SRUZhcUUwMm5GMFB4ZFB6OGVlT0dYc0xSMWM0MEwrUWhRUkRIWkhYVXhpWSt5NEZ5K2xYT3BKZFF1MkVXNVxuQzJhc1NmWCtxNFU1NEp5V2pBN
      lByUmhMZlZ0RmxVR2VhRngyYVY0SGs3SE0xNUptdnpVYlhYSUM2UUY3elhzUFxubldkRUpWcHZ6YStuYi9oL2hUdXhyYUdkNmVYL0tmQktydUx
      2VDlJUHhrcjlHSXpvVnROZVM2SmZzQlFuMDVzS1xucEZTR3RvUXlESm1SMlJrVXJUa2oxVHhFVTBDSk0xd3dmL2VuVkRVWE5JUFBaTThDQXdFQ
      UFhTlRNRkV3SFFZRFxuVlIwT0JCWUVGSUJwdjlBL1VOZUxmbWZEek5sRDNleFptaWwyTUI4R0ExVWRJd1FZTUJhQUZJQnB2OUEvVU5lTFxuZm1
      mRHpObEQzZXhabWlsMk1BOEdBMVVkRXdFQi93UUZNQU1CQWY4d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQlxuQUVFL05FM3pQbjRxQkpLNytVT
      E5aNzE5MVBZNmlBckNOMlNjeXBJdFhUNWpKUFV2ZUV1VFdWSEptVDVaNGNXUFxuUmZVUE5ZSHlSMElTY1psNHFZbTRDN2lwWUdueEdkbkI4QTF
      JQmkyS1BiOWRwWnVVS2pQQUdkUnVETElwUy9CcFxuWHBpWWRacWpMRnNXNjFWT0tDbEhUakxTZXdKVTdCc21IMU9vM1FUSnNUTW5reERWUlRtY
      zlPK1kvazVackRoV1xuSW5DMHdBWVJLSW8yblRsVjhnK2Uyc0xBQ2RkeGdpcVNRaENOaFhmQVN4enZ6aFVWdmRsM1oyQUQ1NFpjS2hTV1xuZjd
      wdDFveEswYm1oVGJsVzVQc0Noa29od3kzaXVpSlRQRWhsTGVXMFpNMERXV2RYcHFkRXY3ZVQ4czVkV05qWFxuMkJGRHdZU0lnZThmbUxwUEFpb
      k53Z1U9XG4tLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tXG4iLAogICAgICAgICAgInNhbWxfbWV0YWRhdGFfeG1sIjogImV4YW1wbGVfbWV0YWR
      hdGEiLAogICAgICAgICAgInByaXZhdGVfa2V5IjogIi0tLS0tQkVHSU4gUFJJVkFURSBLRVktLS0tLVxuTUlJRXZBSUJBREFOQmdrcWhraUc5d
      zBCQVFFRkFBU0NCS1l3Z2dTaUFnRUFBb0lCQVFDV0RsaFFoNC9Ub3A4QVxuZGJNSnk1YU5aLzMzL05qWGJ4YS9YVmJ0MXlWWlhNWG5sWVZJOER
      CZUJzMjNELzA1dy9hQWZxNmwveU9xbVBtYVxuVXh1TldwcUlOdys2aUpyYmdHdTRmSUFFZis2MFJCV3FoTk5weGREOFhUOC9IbmpobDdDMGRYT
      05DL2tJVUVReFxuMlIxMU1ZbVBzdUJjdnBWenFTWFVMdGhGdVF0bXJFbjEvcXVGT2VDY2xvd09qNjBZUzMxYlJaVkJubWhjZG1sZVxuQjVPeHp
      OZVNacjgxRzExeUF1a0JlODE3RDUxblJDVmFiODJ2cDIvNGY0VTdzYTJobmVubC95bndTcTdpNzAvU1xuRDhaSy9SaU02RmJUWGt1aVg3QVVKO
      U9iQ3FSVWhyYUVNZ3laa2RrWkZLMDVJOVU4UkZOQWlUTmNNSC8zcDFRMVxuRnpTRHoyVFBBZ01CQUFFQ2dnRUFNWlFreHRxN2dUUHljQTd6YkZ
      HWmVUZzZWL1R4V21Eb2RhcnIzV0tnQXdJZFxubTNJeUd4TE5FRXdVTFI5NXpkdTY5dUtrWnRGQllnendMM2ExSEVPcTJrWERDeUUyZFQ3ZG1KT
      itJcjU0U3BSU1xuU2toZnA4dUZ6TlFzWVBqNi9LS0hibUIrSU1TcmIvMUJRdTBYQ0JFcGY5S2N3ZGZ3ZmtvTlp5bGhLTXhHZ1pINlxubFFkRlZ
      CSmNZS1I3aitKcE9VT3hOSStYaTRhZ2JkQVNnRGg2eEdob05nRzNhcUpld0trdXRyWW9EcytKd2paUFxuSW5BdVlBbzJuUExFcENGbEJmZHFyQ
      XB6Wm53VjlNZEgxRnBwOVNCODRsYzQ1TWhDSTdsK0krTmhIbFBTVEJjVFxuZTRpTHVyWFRHUUpHUnVJbEd1eTQybktkSEFKbWJ4bFp4L1djREx
      hQjRRS0JnUURQbE5rWnl3OFhMeURPTWtJRVxuWC9ybnprYmxRa281c09vdU5zdFFEV2lPemtpOUIxUXBBeENPaVd4eDlUYnZWbEVNOENlS1NDS
      St6Mk5pemVDdlxuNDlFbzhMWWszQzhuenArQzFwOUY3d2U0a0s5bERBMEtvWWdBQVZqQVFjdVA0a0dKZlB1MGtUVVptOEtQVm4yR1xuRGFzMnk
      5Tmc2OVRVd25ON05pNTR2MjliWVFLQmdRQzVEb2JHcnE5b04yb3FPTTJ2TTl5VFdVa00yYmFiRU9pWFxuVjVvTmhROVRhMU1kekZDK2Q2a2pRT
      C9vR3ZzblcxMnUvWXBjRFVRSHR2UGNIeERaVmQvTjNtL0paNmJoaDQ0cFxuMmxET3JXWWU2UEhKbklHT1VyWldnZEVSVG9rdXFIWGd5NElLYWh
      yUTJOYzl6R3NSQ1M2RC9YTlJ4RG91bVJPRFxucXlqSnZOQmVMd0tCZ1FDNEFhd0k3elVyU2YxOGlwUjJxRllUa1lqS3hONWpFVzRsRlRGWWZ1c
      EdYSm1oNXBNMVxuMzUrWG1qM2xMb2RKMVd2M1doTFN3MnFIM3ZLc3JPOFZPNDgzQ1ZtTWh4bk40WjBJTUJQenZCVk9DQU5TUVVKMlxuUUFYOGF
      6VXZCNlBZb2FtU3BKWEMxRTBMckVUbEpyZ1NVcCtJN1Zrc3V5Qm9rUTRwRkRoUmxQQ3pRUUovY1gxM1xuS21IM01QTExLUm10Q3NYRGdKcEJNc
      UJTWE1jQjNjN2JxMjd1OXpuYXYydU9HVUZKZVZVOFhCd3Y2dk1kZ1RQOFxucG5kOFFUN3lUd3krU0l0aHRkKzBycy9paUcyWndoRWhmWjdhQWh
      yYUxjck5xWHNRVi8xRk9sZnZ5YTJHblZZeFxuYWpHb1FHeTdsQ3JKZnFraTNjK1I2Wk51R3lYRjBmb21DWkxDTndLQmdRQ2hST2RySktZcEY5c
      UdPeTJUMWtnWFxucEFYbkJVZ3lCN0ZSakI5MGlTNTFQNUZkeGpHUDJBSlJMWnVVdmlEOEhtUWF2bWlLSDBCTktORk5FMmN0U2JleVxuVFpqY3J
      KWVdNd043NlA4V2p0RUsyMlJNdUYyNmI0ZTJzL2dnMGZheFpXOHE2UHFBTWFFNnB5RjhGKzFLaFJDMlxub0dneVNmSzd4aTFVVGpqcEdwcnNMU
      T09XG4tLS0tLUVORCBQUklWQVRFIEtFWS0tLS0tXG4iCiAgICAgICAgfQogICAgICB9

  builder:
    enable: true
    enableAuth: true
    enableAuthz: true

  storage:
    enable: true
    enableAuth: true
    allowedBuckets: ["ppdtestbucket", "allowedbucket"]
    defaultBucket: "ppdtestbucket"

  adapters:
    sentry:
      enable: false
      dsn: "https://<EMAIL>/5738132"
      requestBodies: "always"
      tracesSampleRate: 1.0
      extraTags:
        cluster: pp-localhost
        cloud: docker-compose
    oneTimePasswordRepo:
      rateLimit: 10
    jwtToken:
      secret: !ENV ${MP_JWT_SECRET}
      audience: "urn:mp"
      algorithm: "HS256"
    minio:
      url: !ENV ${MP_MINIO_URL}
      accessKey: !ENV ${MP_MINIO_ACCESS_KEY}
      secretKey: !ENV ${MP_MINIO_SECRET_KEY}
      secure: false
      baseUrl: !ENV ${MP_MINIO_BASE_URL}
    redisDatabase:
      url: !ENV ${MP_REDIS_URL}
    aliCloudPush:
      accessKeyId: !ENV ${MP_ALI_CLOUD_PUSH_ACCESS_KEY}
      accessKeySecret: !ENV ${MP_ALI_CLOUD_PUSH_ACCESS_KEY_SECRET}
      region: cn-beijing
      appKey: !ENV ${MP_ALI_CLOUD_APP_KEY}
    twilioSmsVerification:
      default:
        templateKey: "SMSVerificationTemplate"
        serviceName: "Medopad Dev"
    twilioSms:
      default:
        sourcePhoneNumber: "+************"
        accountSid: !ENV ${MP_TWILIO_ACCOUNT_SID}
        authToken: !ENV ${MP_TWILIO_AUTH_TOKEN}
    hawk:
      hashingAlgorithm: sha256
      localTimeOffset: 0
      timeStampSkew: 60
    pdfEngine:
      serverUrl: !ENV ${MP_PDF_ENGINE_SERVER_URL}
