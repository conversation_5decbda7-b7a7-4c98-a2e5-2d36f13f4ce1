from django.db import models

from sdk.common.utils.mongo_utils import generate_obj_id


class DeviceSession(models.Model):
    class Meta:
        db_table = "device_session"
        app_label = "sdk_auth"
        indexes = [
            models.Index(fields=["userId"]),
            models.Index(fields=["mongoId"]),
        ]

    mongoId = models.CharField(max_length=24, unique=True, default=generate_obj_id)
    userId = models.CharField(max_length=24)
    refreshToken = models.CharField(max_length=1024, null=True, blank=True)
    deviceAgent = models.CharField(max_length=1024, null=True, blank=True)
    isActive = models.BooleanField(default=True)
    updateDateTime = models.DateTimeField()
    createDateTime = models.DateTimeField()
