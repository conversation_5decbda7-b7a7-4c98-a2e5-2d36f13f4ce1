from sdk.auth.service.auth_service import AuthService

from sdk.auth.exceptions import EmailNeitherUserNorInvited, UnableToFindSsoConfig
from sdk.auth.use_case.sso._request_use_cases import (
    OIDCRequestUseCase,
    SAMLRequestUseCase,
)
from sdk.auth.use_case.sso._signup_and_signin_use_cases import (
    SsoSignInUseCase,
    SsoSignUpUseCase,
)
from sdk.auth.use_case.sso._utils import get_sso_configs_associated_with_user_or_invitee
from sdk.authorization.exceptions import InvitationDoesNotExistException
from sdk.authorization.services.authorization import AuthorizationService
from sdk.common.exceptions.exceptions import UnauthorizedException


def request_protocol_factory(request_obj):
    sso_config = get_sso_configs_associated_with_user_or_invitee(request_obj.email)
    if sso_config.name == "OIDC":
        return OIDCRequestUseCase()
    elif sso_config.name == "SAML":
        return SAMLRequestUseCase()
    else:
        raise UnableToFindSsoConfig


def callback_auth_factory(req_obj):
    try:
        AuthService().get_user(email=req_obj.email)
        return SsoSignInUseCase()
    except UnauthorizedException:
        pass

    try:
        AuthorizationService().retrieve_invitation(email=req_obj.email)
        return SsoSignUpUseCase()
    except InvitationDoesNotExistException:
        raise EmailNeitherUserNorInvited
