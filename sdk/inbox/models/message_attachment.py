from datetime import datetime

from django.db import models

from sdk.inbox.models import Message


class MessageAttachment(models.Model):
    class Meta:
        db_table = "inbox_attachments"
        app_label = "inbox"

    id = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True)
    message = models.ForeignKey(Message, related_name="attachments", on_delete=models.CASCADE)
    attachmentType = models.CharField(
        max_length=20,
        choices=[
            ("IMAGE", "IMAGE"),
            ("PDF", "PDF"),
            ("EXTERNAL_URL", "EXTERNAL_URL"),
            ("INTERNAL_ARTICLE", "INTERNAL_ARTICLE"),
            ("INTERNAL_MODULE", "INTERNAL_MODULE"),
        ],
    )
    title = models.CharField(max_length=500)
    resource = models.Char<PERSON><PERSON>(max_length=500, null=True, blank=True)
    isResourceDeleted = models.BooleanField(default=False)
    createDateTime = models.DateTimeField(default=datetime.utcnow)
