import unittest
from unittest.mock import patch

from sdk.auth.events.delete_user_event import DeleteUser<PERSON>vent
from sdk.inbox.callbacks.callbacks import delete_user_inbox_on_user_delete_event
from sdk.inbox.dtos.message_dto import MessageDTO

SAMPLE_ID = "600a8476a961574fb38157d5"
CALLBACK_PATH = "sdk.inbox.callbacks.callbacks"


class InboxCallbackTestCase(unittest.TestCase):
    @patch(f"{CALLBACK_PATH}.InboxService")
    def test_success_delete_user_inbox_on_user_delete_callback(self, inbox_service):
        event = DeleteUserEvent(user_id=SAMPLE_ID)
        result = delete_user_inbox_on_user_delete_event(event)
        inbox_service().delete_user_inbox.assert_called_once_with(user_id=SAMPLE_ID)
        self.assertIn(MessageDTO.MODEL_NAME, result)


if __name__ == "__main__":
    unittest.main()
