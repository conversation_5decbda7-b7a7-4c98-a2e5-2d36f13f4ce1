# Generated by Django 5.1.7 on 2025-03-12 15:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("inbox", "0002_remove_message_inbox_message_messagetype_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="messageattachment",
            name="isResourceDeleted",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="messageattachment",
            name="title",
            field=models.CharField(default="test", max_length=500),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="messageattachment",
            name="attachmentType",
            field=models.CharField(
                choices=[
                    ("IMAGE", "IMAGE"),
                    ("PDF", "PDF"),
                    ("EXTERNAL_URL", "EXTERNAL_URL"),
                    ("INTERNAL_ARTICLE", "INTERNAL_ARTICLE"),
                    ("INTERNAL_MODULE", "INTERNAL_MODULE"),
                ],
                max_length=20,
            ),
        ),
    ]
