from sdk.common.usecase.use_case import UseCase
from sdk.inbox.router.request_objects import ConfirmMessageRequestObject
from sdk.inbox.router.response_objects import ConfirmMessageResponseObject
from sdk.inbox.services.inbox_service import InboxService


class ConfirmMessagesUseCase(UseCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def process_request(self, request_obj: ConfirmMessageRequestObject):
        is_user = request_obj.authzUser.is_user()
        updated = InboxService().read_messages(request_obj.authzUser.id, request_obj.messageIds, is_user)
        result = ConfirmMessageResponseObject(updated)
        return result
