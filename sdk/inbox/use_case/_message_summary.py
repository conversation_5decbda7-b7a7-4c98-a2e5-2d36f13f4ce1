from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.json_utils import replace_values
from sdk.inbox.router.request_objects import MessagesSummaryRequestObject
from sdk.inbox.router.response_objects import MessageSummaryResponseObject
from sdk.inbox.services.inbox_service import InboxService


class MessageSummaryUseCase(UseCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def process_request(self, request_obj: MessagesSummaryRequestObject):
        response = InboxService().retrieve_submitters_first_messages(request_obj.userId)
        response["messages"] = replace_values(response["messages"], request_obj.authzUser.localization)

        result = MessageSummaryResponseObject().from_dict(response)
        return result
