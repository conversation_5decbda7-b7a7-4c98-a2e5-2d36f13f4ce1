import enum
from dataclasses import field
from datetime import datetime

from sdk.common.utils.convertible import (
    convertibleclass,
    default_field,
    meta,
    required_field,
)
from sdk.common.utils.fields import id_field
from sdk.common.utils.validators import default_datetime_meta, validate_not_too_long
from sdk.inbox.models.message import MessageStatusType


class MessageType(enum.Enum):
    TEXT = "TEXT"
    RICH_TEXT = "RICH_TEXT"
    ATTACHMENT = "ATTACHMENT"


class AttachmentType(enum.Enum):
    IMAGE = "IMAGE"
    PDF = "PDF"
    EXTERNAL_URL = "EXTERNAL_URL"
    INTERNAL_ARTICLE = "INTERNAL_ARTICLE"
    INTERNAL_MODULE = "INTERNAL_MODULE"


@convertibleclass
class MessageAttachmentDTO:
    ATTACHMENT_TYPE = "attachmentType"
    TITLE = "title"
    RESOURCE = "resource"

    id: str = id_field()
    attachmentType: AttachmentType = required_field()
    title: str = required_field(metadata=meta(validate_not_too_long))
    resource: str = id_field(required=True)
    resourceUrl: str = default_field()
    isResourceDeleted: bool = field(default=False)
    createDateTime: datetime = default_field(metadata=default_datetime_meta())


@convertibleclass
class MessageDTO:
    MODEL_NAME = "inbox"
    ID = "id"
    USER_ID = "userId"
    SUBMITTER_ID = "submitterId"
    SUBMITTER_NAME = "submitterName"
    TEXT = "text"
    STATUS = "status"
    CREATE_DATE_TIME = "createDateTime"
    UPDATE_DATE_TIME = "updateDateTime"
    CUSTOM = "custom"
    MESSAGE_TYPE = "messageType"
    ATTACHMENTS = "attachments"
    MAX_MESSAGE_LENGTH = 850
    MAX_ATTACHMENTS = 10

    id: str = id_field()
    userId: str = id_field()
    submitterId: str = id_field(required=True)
    submitterName: str = default_field()
    text: str = default_field()
    status: MessageStatusType = default_field()
    createDateTime: datetime = default_field(metadata=default_datetime_meta())
    updateDateTime: datetime = default_field(metadata=default_datetime_meta())
    custom: bool = field(default=False)
    locale: str = default_field()
    messageType: MessageType = default_field()
    attachments: list[MessageAttachmentDTO] = default_field()


@convertibleclass
class SubmitterMessageReport:
    LATEST_MESSAGE = "latestMessage"
    UNREAD_MESSAGE_COUNT = "unreadMessageCount"
    CUSTOM = "custom"

    latestMessage: MessageDTO = default_field()
    unreadMessageCount: int = default_field()
    custom: bool = field(default=False)

    def post_init(self):
        if self.latestMessage.custom:
            self.custom = True
