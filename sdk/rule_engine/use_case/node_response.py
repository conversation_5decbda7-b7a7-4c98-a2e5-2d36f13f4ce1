from dataclasses import field

from sdk import convertibleclass
from sdk.common.usecase.response_object import ResponseObject
from sdk.common.utils.convertible import required_field
from sdk.rule_engine.nodes.node_manager import Node


@convertibleclass
class NodeConvertible:
    name: str = required_field()
    parameters: list[str] = required_field()
    output: list[str] = required_field()
    isTrigger: bool = field(default=False)
    description: str = field(default="")

    def from_node(self, node: Node):
        self.name = node.name
        self.parameters = list(node.parameters or [])
        self.output = list(node.output or [])
        self.isTrigger = node.isTrigger
        self.description = node.description
        return self


@convertibleclass
class GetNodesResponseObject(ResponseObject):
    nodes: list[NodeConvertible] = required_field()

    def __init__(self, nodes: list[Node]):
        self.nodes = [NodeConvertible().from_node(node) for node in nodes]
