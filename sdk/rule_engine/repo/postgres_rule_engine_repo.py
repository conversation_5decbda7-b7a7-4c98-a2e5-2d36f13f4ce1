from datetime import datetime

from bson import ObjectId
from django.db.models import Q

from sdk.common.utils.validators import id_as_obj_id, remove_none_values, model_to_dict
from sdk.rule_engine.dtos import WorkflowDTO
from sdk.rule_engine.exceptions import WorkflowNotFoundException
from sdk.rule_engine.models import Workflow, WorkflowSchedule
from sdk.rule_engine.models.workflow_status_model import WorkflowStatusType
from sdk.rule_engine.repo.rule_engine_repo import (
    RuleEngineRepository,
    RuleEngineScheduleRepository,
)


class PostgresRuleEngineRepository(RuleEngineRepository):
    def delete_workflow_by_id(self, workflow_id: str, resource_id: str):
        Workflow.objects.filter(mongoId=workflow_id, resourceId__contains=resource_id).update(
            deleteDateTime=datetime.utcnow(), status=WorkflowStatusType.ARCHIVED.value
        )

    def create_workflow(self, workflow: WorkflowDTO) -> str:
        new_workflow = Workflow(**workflow.to_dict(include_none=False))
        new_workflow.isDraft = True
        new_workflow.mongoId = ObjectId()
        new_workflow.save()
        return str(new_workflow.mongoId)

    def update_workflow(self, workflow: WorkflowDTO) -> str:
        """
        If the workflow is not a draft, create a new draft workflow with the updated data.
        If the workflow is a draft, update the existing draft workflow.
        In case of creating a new draft workflow, the draft deployment id will be put in the resource id array of the
        draft workflow, and the original deployment id will be put in the resource id array of the published workflow.
        Before above change, the resourceId is [original_deployment_id, draft_deployment_id] (the order is important).
        """
        existing_workflow: Workflow = Workflow.objects.filter(mongoId=workflow.id).first()
        if not existing_workflow:
            raise WorkflowNotFoundException(f"workflow not found to update {workflow.id}")
        if existing_workflow.isDraft:
            changes = workflow.to_dict(include_none=False)
            changes.pop(WorkflowDTO.ID, None)
            changes.pop("mongoId", None)
            changes["updateDateTime"] = datetime.utcnow()
            changes["isDraft"] = True
            Workflow.objects.filter(mongoId=existing_workflow.mongoId).update(**changes)
            return existing_workflow.mongoId

        original_deployment_id, draft_deployment_id = existing_workflow.resourceId
        existing_workflow.resourceId = [original_deployment_id]
        existing_workflow.save()
        return self._create_draft_workflow(workflow, draft_deployment_id)

    @staticmethod
    def _create_draft_workflow(workflow: WorkflowDTO, draft_deployment_id: str) -> str:
        workflow_dict = workflow.to_dict(include_none=False)
        workflow_dict.pop(WorkflowDTO.ID, None)
        workflow_dict.pop(WorkflowDTO.UPDATE_DATE_TIME, None)
        doc = {
            **workflow_dict,
            Workflow.UPDATE_DATE_TIME: datetime.utcnow(),
            Workflow.PUBLISHED_WORKFLOW_ID: workflow.id,
            Workflow.IS_DRAFT: True,
            Workflow.RESOURCE_ID: [draft_deployment_id],
            "mongoId": ObjectId(),
        }
        new_workflow = Workflow(**doc).save()
        return str(new_workflow.mongoId)

    @staticmethod
    def bulk_update(ids: list[str], data: dict):
        Workflow.objects.filter(mongoId__in=ids).update(**data)

    def publish_workflow(self, workflow: WorkflowDTO):
        if not workflow.publishedWorkflowId:
            return workflow.id
        draft_workflow = Workflow.objects.filter(mongoId=workflow.id).first()
        published_workflow = Workflow.objects.filter(mongoId=draft_workflow.publishedWorkflowId).first()
        if not published_workflow:
            raise WorkflowNotFoundException(f"published workflow not found {draft_workflow.publishedWorkflowId}")

        draft_workflow.resourceId = published_workflow.resourceId
        draft_dict = draft_workflow.to_dict(include_none=True)
        draft_dict.pop("id")
        draft_dict.pop("mongoId")
        draft_dict.pop("isDraft")
        draft_dict.pop("publishedWorkflowId")

        fields_to_unset = {field: None for field, value in draft_dict.items() if value is None}

        published_workflow.update(**{**remove_none_values(draft_dict), **fields_to_unset})

        draft_workflow.delete()

    def get_workflows_by_trigger_node(self, node_name: str, resource_id: str) -> list[WorkflowDTO]:
        result = Workflow.objects.filter(
            startNode=node_name,
            resourceId__contains=resource_id,
            status=WorkflowStatusType.ACTIVE.value,
        )
        return [WorkflowDTO.from_dict(model_to_dict(workflow)) for workflow in result]

    def get_workflows_by_resource_id(self, resource_id: str, include_archived=False) -> list[WorkflowDTO]:
        query = Workflow.objects.filter(resourceId__contains=resource_id)
        if not include_archived:
            query = query.filter(~Q(status=WorkflowStatusType.ARCHIVED.value))

        return [
            WorkflowDTO.from_dict(self._replace_workflow_resource(resource_id, model_to_dict(workflow)))
            for workflow in query
        ]

    @staticmethod
    def _replace_workflow_resource(resource_id: str, workflow_dict: dict):
        workflow_dict[WorkflowDTO.RESOURCE_ID] = [resource_id]
        return workflow_dict

    def get_scheduled_workflows(self) -> list[WorkflowDTO]:
        list_with_duplicate_results = list(
            model_to_dict(workflow)
            for workflow in Workflow.objects.filter(
                schedule__exists=True,
                status=WorkflowStatusType.ACTIVE.value,
            )
        )

        result = list()
        for workflow in list_with_duplicate_results:
            if len(workflow[WorkflowDTO.RESOURCE_ID]) > 1:
                for resource_id in workflow[WorkflowDTO.RESOURCE_ID]:
                    result.append(self._replace_workflow_resource(resource_id, workflow))
        return [WorkflowDTO.from_dict(workflow) for workflow in result]

    def get_workflows_by_ids(self, workflow_ids: list[str]) -> list[WorkflowDTO]:
        return [
            WorkflowDTO.from_dict(model_to_dict(workflow))
            for workflow in Workflow.objects.filter(
                ~Q(status=WorkflowStatusType.ARCHIVED.value),
                mongoId__in=workflow_ids,
            )
        ]

    def get_workflow_by_id(
        self,
        workflow_id: str,
        resource_id=None,
        include_archived=False,
    ) -> WorkflowDTO:
        query = Workflow.objects.filter(mongoId=workflow_id)
        if resource_id:
            query = query.filter(resourceId__contains=resource_id)
        if not include_archived:
            query = query.filter(~Q(status=WorkflowStatusType.ARCHIVED.value))
        found_workflow = query.first()

        if not found_workflow:
            raise WorkflowNotFoundException(f"workflow not found {workflow_id}")

        return WorkflowDTO.from_dict(model_to_dict(found_workflow))


class PostgresRuleEngineScheduleRepository(RuleEngineScheduleRepository):
    @id_as_obj_id
    def create_schedule(self, workflow_id: str, date_time: datetime):
        new_schedule = WorkflowSchedule(
            mongoId=ObjectId(),
            workflowId=workflow_id,
            createDateTime=datetime.utcnow(),
            triggerDateTime=date_time,
        )
        new_schedule.save()

    def get_ready_schedules(self, date_time: datetime) -> list[str]:
        result = WorkflowSchedule.objects.filter(triggerDateTime__lte=date_time, endDateTime=None)
        return [str(schedule.workflowId) for schedule in result]

    def delete_schedule_by_workflow_id(self, workflow_id: str):
        WorkflowSchedule.objects.filter(workflowId=workflow_id).delete()

    def schedule_exists(self, workflow_id: str, from_date: datetime) -> bool:
        return WorkflowSchedule.objects.filter(workflowId=workflow_id, triggerDateTime=None).exists()

    @id_as_obj_id
    def log_workflow_run(
        self,
        workflow_id: str,
        start_date_time: datetime,
        end_date_time: datetime,
        error: str | None,
    ):
        WorkflowSchedule.objects.filter(workflowId=workflow_id).update(
            startDateTime=start_date_time,
            endDateTime=end_date_time,
            errorMessage=error,
        )
