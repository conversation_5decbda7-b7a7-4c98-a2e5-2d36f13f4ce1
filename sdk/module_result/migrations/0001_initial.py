# Generated by Django 5.1.5 on 2025-02-11 14:03

import django.contrib.postgres.fields
from django.db import migrations, models

import sdk.common.adapter.sql.sql_utils
import sdk.common.utils.mongo_utils
import sdk.module_result.models.custom_module_config_model
from sdk.common.utils.file_utils import move_mongo_collection_to_postgres_table


def transformer(mapping):
    primitive_data = {}
    for key in list(mapping.keys()):
        if key not in [
            "mongoId",
            "userId",
            "moduleId",
            "moduleConfigId",
            "deploymentId",
            "primitiveName",
            "extra",
            "flags",
            "createDateTime",
            "startDateTime",
            "source",
        ]:
            primitive_data[key] = mapping.pop(key, None)
    mapping["primitiveData"] = primitive_data
    mapping["startDateTime"] = mapping.get("startDateTime", None) or mapping.get("createDateTime", None)
    return mapping


def migrate_data(apps, schema_editor):
    move_mongo_collection_to_postgres_table(
        apps,
        "module_result",
        migrations=[
            {
                "model_name": "CustomModuleConfig",
                "mongo_model": "custommoduleconfig",
            },
            {
                "model_name": "CustomModuleConfigLog",
                "mongo_model": "custommoduleconfiglog",
            },
            {
                "model_name": "Goal",
                "mongo_model": "goal",
            },
            {
                "model_name": "UnseenResult",
                "mongo_model": "unseenrecentresult",
                "transformer": transformer,
                "partial_migrate_filter": ("createDateTime", 14),
            },
        ],
    )


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="CustomModuleConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "mongoId",
                    models.CharField(
                        max_length=24,
                        unique=True,
                        default=sdk.common.utils.mongo_utils.generate_obj_id,
                    ),
                ),
                ("updateDateTime", models.DateTimeField()),
                ("createDateTime", models.DateTimeField()),
                ("moduleId", models.CharField(max_length=32)),
                (
                    "moduleName",
                    models.CharField(blank=True, default=None, max_length=128, null=True),
                ),
                (
                    "shortModuleName",
                    models.CharField(blank=True, default=None, max_length=32, null=True),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[("ENABLED", "ENABLED"), ("DISABLED", "DISABLED")],
                        default=None,
                        null=True,
                    ),
                ),
                (
                    "order",
                    models.PositiveIntegerField(blank=True, default=None, null=True),
                ),
                ("configBody", models.JSONField(blank=True, default=None, null=True)),
                (
                    "about",
                    models.CharField(blank=True, default=None, max_length=10000, null=True),
                ),
                ("footnote", models.JSONField(blank=True, default=None, null=True)),
                (
                    "learnArticleIds",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(),
                        blank=True,
                        default=None,
                        null=True,
                        size=None,
                    ),
                ),
                (
                    "version",
                    models.PositiveIntegerField(blank=True, default=None, null=True),
                ),
                ("staticEvent", models.JSONField(blank=True, default=None, null=True)),
                (
                    "notificationData",
                    models.JSONField(blank=True, default=None, null=True),
                ),
                (
                    "localizationPrefix",
                    models.CharField(blank=True, default=None, max_length=128, null=True),
                ),
                (
                    "ragThresholds",
                    models.JSONField(blank=True, default=None, null=True),
                ),
                (
                    "reason",
                    models.CharField(blank=True, default=None, max_length=1000, null=True),
                ),
                ("schedule", models.JSONField(blank=True, default=None, null=True)),
                (
                    "userId",
                    models.CharField(blank=True, default=None, max_length=24, null=True),
                ),
                (
                    "moduleConfigId",
                    models.CharField(blank=True, default=None, max_length=24, null=True),
                ),
                (
                    "goal",
                    models.JSONField(
                        blank=True,
                        default=None,
                        encoder=sdk.module_result.models.custom_module_config_model.ObjectIdEncoder,
                        null=True,
                    ),
                ),
                (
                    "goalEnabled",
                    models.BooleanField(blank=True, default=None, null=True),
                ),
            ],
            options={
                "db_table": "custom_module_config",
            },
        ),
        migrations.CreateModel(
            name="CustomModuleConfigLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "mongoId",
                    models.CharField(
                        max_length=24,
                        unique=True,
                        default=sdk.common.utils.mongo_utils.generate_obj_id,
                    ),
                ),
                ("updateDateTime", models.DateTimeField()),
                ("createDateTime", models.DateTimeField()),
                ("moduleId", models.CharField(max_length=32)),
                (
                    "moduleName",
                    models.CharField(blank=True, default=None, max_length=128, null=True),
                ),
                (
                    "shortModuleName",
                    models.CharField(blank=True, default=None, max_length=32, null=True),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[("ENABLED", "ENABLED"), ("DISABLED", "DISABLED")],
                        default=None,
                        null=True,
                    ),
                ),
                (
                    "order",
                    models.PositiveIntegerField(blank=True, default=None, null=True),
                ),
                ("configBody", models.JSONField(blank=True, default=None, null=True)),
                (
                    "about",
                    models.CharField(blank=True, default=None, max_length=10000, null=True),
                ),
                ("footnote", models.JSONField(blank=True, default=None, null=True)),
                (
                    "learnArticleIds",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(),
                        blank=True,
                        default=None,
                        null=True,
                        size=None,
                    ),
                ),
                (
                    "version",
                    models.PositiveIntegerField(blank=True, default=None, null=True),
                ),
                ("staticEvent", models.JSONField(blank=True, default=None, null=True)),
                (
                    "notificationData",
                    models.JSONField(blank=True, default=None, null=True),
                ),
                (
                    "localizationPrefix",
                    models.CharField(blank=True, default=None, max_length=128, null=True),
                ),
                (
                    "ragThresholds",
                    models.JSONField(blank=True, default=None, null=True),
                ),
                (
                    "reason",
                    models.CharField(blank=True, default=None, max_length=1000, null=True),
                ),
                ("schedule", models.JSONField(blank=True, default=None, null=True)),
                (
                    "userId",
                    models.CharField(blank=True, default=None, max_length=24, null=True),
                ),
                (
                    "moduleConfigId",
                    models.CharField(blank=True, default=None, max_length=24, null=True),
                ),
                (
                    "goal",
                    models.JSONField(
                        blank=True,
                        default=None,
                        encoder=sdk.module_result.models.custom_module_config_model.ObjectIdEncoder,
                        null=True,
                    ),
                ),
                (
                    "goalEnabled",
                    models.BooleanField(blank=True, default=None, null=True),
                ),
                (
                    "clinicianId",
                    models.CharField(blank=True, default=None, max_length=24, null=True),
                ),
            ],
            options={
                "db_table": "custom_module_config_log",
            },
        ),
        migrations.CreateModel(
            name="Goal",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mongoId", models.CharField(max_length=24, unique=True)),
                ("userId", models.CharField(max_length=24)),
                ("moduleConfigId", models.CharField(max_length=24)),
                ("moduleId", models.CharField(max_length=32)),
                ("values", models.JSONField()),
                (
                    "notes",
                    models.CharField(blank=True, default=None, max_length=1000, null=True),
                ),
                ("targetDate", models.DateField(blank=True, null=True)),
                ("seenByApp", models.BooleanField(blank=True, default=None, null=True)),
                (
                    "seenByCPAfterCompletion",
                    models.BooleanField(blank=True, default=None, null=True),
                ),
                (
                    "achievedValues",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(),
                        blank=True,
                        default=list,
                        null=True,
                        size=None,
                    ),
                ),
                (
                    "createdBy",
                    models.JSONField(
                        blank=True,
                        default=None,
                        encoder=sdk.module_result.models.module_goal_model.ObjectIdEncoder,
                        null=True,
                    ),
                ),
                ("endDateTime", models.DateTimeField(blank=True, null=True, default=None)),
                ("createDateTime", models.DateTimeField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("CREATED", "CREATED"),
                            ("EDITED", "EDITED"),
                            ("COMPLETED", "COMPLETED"),
                            ("DELETED", "DELETED"),
                        ]
                    ),
                ),
                (
                    "parentId",
                    models.CharField(blank=True, default=None, max_length=24, null=True),
                ),
                (
                    "completedInfo",
                    models.JSONField(
                        blank=True,
                        default=None,
                        encoder=sdk.module_result.models.module_goal_model.ObjectIdEncoder,
                        null=True,
                    ),
                ),
            ],
            options={
                "db_table": "goal",
            },
        ),
        migrations.CreateModel(
            name="UnseenResult",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mongoId", models.CharField(max_length=24, unique=True)),
                ("userId", models.CharField(db_index=True, max_length=24)),
                ("moduleId", models.CharField(db_index=True, max_length=64)),
                ("moduleConfigId", models.CharField(db_index=True, max_length=24, blank=True, null=True, default=None)),
                ("deploymentId", models.CharField(db_index=True, max_length=24)),
                ("primitiveName", models.CharField(max_length=128)),
                ("extra", models.JSONField(blank=True, default=None, null=True)),
                ("flags", models.JSONField(blank=True, null=True, default=None)),
                ("createDateTime", models.DateTimeField()),
                ("startDateTime", models.DateTimeField()),
                ("source", models.TextField(blank=True, null=True)),
                (
                    "primitiveData",
                    models.JSONField(
                        blank=True,
                        default=None,
                        encoder=sdk.common.adapter.sql.sql_utils.BSONEncoder,
                        help_text="This will store the rest of the primitive data",
                        null=True,
                    ),
                ),
            ],
            options={
                "db_table": "unseen_recent_result",
            },
        ),
        migrations.RunPython(migrate_data, lambda *args, **kwargs: 0, atomic=True),
    ]
