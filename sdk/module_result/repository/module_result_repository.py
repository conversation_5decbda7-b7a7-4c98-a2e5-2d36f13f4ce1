from abc import ABC, abstractmethod
from datetime import datetime
from typing import Optional, TYPE_CHECKING, Union

from sdk.common.common_models.sort import SortField
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.module_result_utils import AggregateFunc, AggregateMode

if TYPE_CHECKING:
    from sdk.module_result.modules.questionnaire import QuestionnaireStatus


class ModuleResultRepository(ABC):
    @abstractmethod
    def create_primitive(self, primitive: PrimitiveDTO) -> str:
        raise NotImplementedError

    @abstractmethod
    def update_primitive(
        self,
        primitive: PrimitiveDTO,
        save_unseen: bool = False,
    ) -> str:
        raise NotImplementedError

    @abstractmethod
    def batch_assign_event_log_id(self, primitive_class: type[PrimitiveDTO], ids: list[str], event_log_id: str) -> int:
        raise NotImplementedError

    @abstractmethod
    def update_primitives_status(
        self,
        primitive_name: str,
        module_result_ids: list,
        module_id: str,
        user_id: str,
        status: "QuestionnaireStatus",
        last_status: "QuestionnaireStatus",
        deployment_id: str,
        module_config_ids: list[str],
    ):
        raise NotImplementedError

    @abstractmethod
    def get_primitive(self, primitive_name, _id: str):
        raise NotImplementedError

    @staticmethod
    @abstractmethod
    def get_primitive_dict(primitive: PrimitiveDTO) -> dict:
        raise NotImplementedError

    @abstractmethod
    def retrieve_primitives(
        self,
        user_id: str,
        module_id: str,
        primitive_name: str,
        skip: Optional[int],
        limit: Optional[int],
        direction: SortField.Direction | None = None,
        from_date_time: datetime | None = None,
        to_date_time: datetime | None = None,
        field_filter: dict | None = None,
        excluded_fields: list[str] | None = None,
        included_fields: list[str] | None = None,
        module_config_id: str | None = None,
        exclude_module_ids: list[str] | None = None,
        only_unseen_results: bool | None = None,
        status: str | None = None,
        use_create_date_time: bool = False,
        average: bool = False,
        time_zone: str | None = None,
        module_result_id: str | None = None,
        return_total_count: bool = False,
    ) -> Union[list[PrimitiveDTO], tuple[list[PrimitiveDTO], int], int]:
        raise NotImplementedError

    @abstractmethod
    def count_primitives(
        self,
        user_id: str,
        module_id: str,
        primitive_class: type[PrimitiveDTO],
        module_config_id: str,
        from_date_time: datetime = None,
        to_date_time: datetime = None,
        field_filter: dict = None,
        exclude_module_ids: list[str] = None,
        status: str = None,
        use_create_date_time: bool = False,
    ) -> int:
        raise NotImplementedError

    @abstractmethod
    def retrieve_aggregated_results(
        self,
        user_id: str,
        module_type: str,
        aggregation_function: AggregateFunc,
        mode: AggregateMode,
        start_date: datetime,
        end_date: datetime,
        skip: int = None,
        limit: int = None,
        module_config_id: str = None,
        deployment_id: str = None,
        timezone: str = None,
        requester_tz: str = None,
        include_max_record: bool = None,
        days_range: int = None,
    ):
        raise NotImplementedError

    @abstractmethod
    def retrieve_primitive(self, user_id: str, primitive_name: str, **filter_options) -> PrimitiveDTO:
        raise NotImplementedError

    @abstractmethod
    def link_note_to_primitives(
        self,
        user_id: str,
        primitive_name: str,
        primitive_id: str,
        note_id: str,
    ):
        raise NotImplementedError

    @abstractmethod
    def embed_feedback_to_primitive(
        self,
        user_id: str,
        primitive_name: str,
        primitive_id: str,
        feedback_id: str,
    ):
        raise NotImplementedError

    @abstractmethod
    def retrieve_latest_feedback(self, user_id, primitive_class: type[PrimitiveDTO]):
        raise NotImplementedError

    @abstractmethod
    def retrieve_latest_feedback_by_criteria(
        self,
        user_id: str,
        primitive_name: str,
        criteria: dict,
    ):
        raise NotImplementedError

    @abstractmethod
    def delete_user_primitive(self, user_id: str, primitive_classes: list[type[PrimitiveDTO]]):
        raise NotImplementedError

    @abstractmethod
    def create_indexes(self, collection_class: type[PrimitiveDTO]):
        raise NotImplementedError

    @abstractmethod
    def retrieve_date_range_aggregated_results(
        self,
        user_id: str,
        primitive_name: str,
        start_date: datetime,
        end_date: datetime,
        module_config_id: str | None = None,
        deployment_id: str | None = None,
    ):
        raise NotImplementedError
