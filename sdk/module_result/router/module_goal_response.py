from dataclasses import dataclass

from sdk.common.usecase.response_object import Response
from sdk.common.utils.convertible import default_field, convertibleclass
from sdk.module_result.dtos.module_config import ModuleConfig
from sdk.module_result.dtos.module_goal import GoalDTO


@dataclass
class ModuleGoalResponseObject(Response):
    goal: GoalDTO = default_field()
    module_config: ModuleConfig = default_field()

    def to_dict(self) -> dict:
        response = {}
        if self.module_config:
            response["moduleConfig"] = self.module_config.to_dict(include_none=False)
        if self.goal:
            response["goal"] = self.goal.to_dict(include_none=False)
        return response


@dataclass
class RetrieveAllLatestUserModuleGoalsResponseObject(Response):
    goals: list[ModuleGoalResponseObject] = default_field()

    def to_dict(self) -> dict:
        return {
            "goals": [goal.to_dict() for goal in self.goals if goal],
        }


class ModuleGoalsResponseObject(Response):
    @convertibleclass
    class ResponseCls:
        GOALS = "goals"
        MODULE_CONFIG = "moduleConfig"

        goals: list[GoalDTO] = default_field()
        moduleConfig: ModuleConfig = default_field()

    def __init__(self, goals: list[GoalDTO], module_config: ModuleConfig):
        value = self.ResponseCls.from_dict(
            {
                self.ResponseCls.GOALS: goals,
                self.ResponseCls.MODULE_CONFIG: module_config,
            }
        )
        super().__init__(value)
