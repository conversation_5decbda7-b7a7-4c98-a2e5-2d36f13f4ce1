from sdk.module_result.common.enums import YesNoDont, VaccineLocation
from sdk.module_result.modules.questionnaire import (
    QuestionnaireMetadata,
    QuestionnaireAnswer,
)
from sdk.module_result.modules.vaccination_details import VaccinationDetailsDTO
from ._utils import common_fields


def sample_vaccination_details() -> dict:
    return {
        **common_fields(),
        "type": VaccinationDetailsDTO.get_primitive_name(),
        VaccinationDetailsDTO.VACCINATED_PLACE: VaccineLocation.HOSPITAL,
        VaccinationDetailsDTO.VACCINATION_LOCATION: "NHS",
        VaccinationDetailsDTO.VACCINATION_CITY: "London",
        VaccinationDetailsDTO.BATCH_NUMBER: "ABV3922",
        VaccinationDetailsDTO.IS_SECOND_DOSE_VACC: YesNoDont.YES,
        VaccinationDetailsDTO.IS_SEASON_FLU_VAC: False,
        VaccinationDetailsDTO.IS_OTHER_SPECIFIED_VACC: True,
        VaccinationDetailsDTO.IS_ALLERGIC_REACTION_VACC: YesNoDont.DONT_KNOW,
        VaccinationDetailsDTO.METADATA: {
            QuestionnaireMetadata.ANSWERS: [
                {
                    QuestionnaireAnswer.QUESTION_ID: "032db29f-363c-418f-b626-2fa21adcc557",
                    QuestionnaireAnswer.QUESTION: "Where were you vaccinated?",
                    QuestionnaireAnswer.ANSWER_TEXT: "Hospital",
                },
                {
                    QuestionnaireAnswer.QUESTION_ID: "1d8efcfc-02c8-4153-95c3-f9bdb32499e0",
                    QuestionnaireAnswer.QUESTION: "Please specify where",
                    QuestionnaireAnswer.ANSWER_TEXT: "NHS",
                },
                {
                    QuestionnaireAnswer.QUESTION_ID: "3ce7cf65-f3d6-4bd0-8e67-fd9c98b636fd",
                    QuestionnaireAnswer.QUESTION: "Please specify the city/town.",
                    QuestionnaireAnswer.ANSWER_TEXT: "London",
                },
            ]
        },
    }
