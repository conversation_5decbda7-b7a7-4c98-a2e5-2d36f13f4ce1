from sdk.module_result.modules.questionnaire import QuestionnaireAnswer
from ._utils import common_fields


def sample_pam_questionnaire_result() -> dict:
    return {
        **common_fields(),
        "type": "Questionnaire",
        "answers": [
            {
                QuestionnaireAnswer.QUESTION: "PA1",
                QuestionnaireAnswer.ANSWER_TEXT: "Disagree",
            },
            {
                QuestionnaireAnswer.QUESTION: "PA2",
                QuestionnaireAnswer.ANSWER_TEXT: "Disagree",
            },
            {
                QuestionnaireAnswer.QUESTION: "PA3",
                QuestionnaireAnswer.ANSWER_TEXT: "Disagree",
            },
            {
                QuestionnaireAnswer.QUESTION: "PA4",
                QuestionnaireAnswer.ANSWER_TEXT: "Disagree",
            },
            {
                QuestionnaireAnswer.QUESTION: "PA5",
                QuestionnaireAnswer.ANSWER_TEXT: "Disagree",
            },
            {
                QuestionnaireAnswer.QUESTION: "PA6",
                QuestionnaireAnswer.ANSWER_TEXT: "Disagree",
            },
            {
                QuestionnaireAnswer.QUESTION: "PA7",
                QuestionnaireAnswer.ANSWER_TEXT: "Disagree",
            },
            {
                QuestionnaireAnswer.QUESTION: "PA8",
                QuestionnaireAnswer.ANSWER_TEXT: "Disagree",
            },
            {
                QuestionnaireAnswer.QUESTION: "PA9",
                QuestionnaireAnswer.ANSWER_TEXT: "Disagree",
            },
            {
                QuestionnaireAnswer.QUESTION: "PA10",
                QuestionnaireAnswer.ANSWER_TEXT: "Disagree",
            },
            {
                QuestionnaireAnswer.QUESTION: "PA11",
                QuestionnaireAnswer.ANSWER_TEXT: "Disagree",
            },
            {
                QuestionnaireAnswer.QUESTION: "PA12",
                QuestionnaireAnswer.ANSWER_TEXT: "Disagree",
            },
            {
                QuestionnaireAnswer.QUESTION: "PA13",
                QuestionnaireAnswer.ANSWER_TEXT: "Disagree",
            },
        ],
        "questionnaireId": "d7c92a9e-ca3b-4f73-824e-3b1ac3b5141d",
        "questionnaireName": "Questionnaire",
    }
