from sdk.module_result.modules import MGADLModule
from sdk.module_result.modules.questionnaire import QuestionnaireAnswer, QuestionnaireDTO
from ._utils import common_fields


def sample_mg_adl():
    return {
        **common_fields(),
        "type": QuestionnaireDTO.get_primitive_name(),
        QuestionnaireDTO.QUESTIONNAIRE_ID: "611dffd20d4e78155ae591e8",
        QuestionnaireDTO.ANSWERS: [
            {
                QuestionnaireAnswer.ANSWER_TEXT: "hu_mg_adl_q8_option1",
                QuestionnaireAnswer.QUESTION_ID: "mg_adl_eyelid_dropping",
                QuestionnaireAnswer.QUESTION: "give us a number",
            },
            {
                QuestionnaireAnswer.ANSWER_TEXT: "hu_mg_adl_q7_option2",
                QuestionnaireAnswer.QUESTION_ID: "mg_adl_double_vision",
                QuestionnaireAnswer.QUESTION: "give us a number",
            },
            {
                QuestionnaireAnswer.ANSWER_TEXT: "hu_mg_adl_q2_option4",
                QuestionnaireAnswer.QUESTION_ID: "mg_adl_symptoms_affecting_chewing",
                QuestionnaireAnswer.QUESTION: "give us a number",
            },
            {
                QuestionnaireAnswer.ANSWER_TEXT: "hu_mg_adl_q1_option3",
                QuestionnaireAnswer.QUESTION_ID: "mg_adl_symptoms_affecting_talking",
                QuestionnaireAnswer.QUESTION: "give us a number",
            },
            {
                QuestionnaireAnswer.ANSWER_TEXT: "hu_mg_adl_q3_option2",
                QuestionnaireAnswer.QUESTION_ID: "mg_adl_symptoms_affecting_swallowing",
                QuestionnaireAnswer.QUESTION: "give us a number",
            },
            {
                QuestionnaireAnswer.ANSWER_TEXT: "hu_mg_adl_q4_option4",
                QuestionnaireAnswer.QUESTION_ID: "mg_adl_symptoms_affecting_breathing",
                QuestionnaireAnswer.QUESTION: "give us a number",
            },
            {
                QuestionnaireAnswer.ANSWER_TEXT: "hu_mg_adl_q5_option2",
                QuestionnaireAnswer.QUESTION_ID: "mg_adl_impairment_brush_comb",
                QuestionnaireAnswer.QUESTION: "give us a number",
            },
            {
                QuestionnaireAnswer.ANSWER_TEXT: "hu_mg_adl_q6_option1",
                QuestionnaireAnswer.QUESTION_ID: "mg_adl_impairment_arise_chair",
                QuestionnaireAnswer.QUESTION: "give us a number",
            },
        ],
        QuestionnaireDTO.MODULE_ID: MGADLModule.moduleId,
        QuestionnaireDTO.QUESTIONNAIRE_NAME: MGADLModule.moduleId,
        QuestionnaireDTO.MODULE_CONFIG_ID: "61c444ca4618f7287a1c1bc7",
    }
