from sdk.module_result.common.enums import Regularly
from sdk.module_result.modules.questionnaire import (
    QuestionnaireAnswer,
    QuestionnaireMetadata,
)
from sdk.module_result.modules.questionnaire_qol import AdditionalQoLDTO
from ._utils import common_fields


def sample_additional_qol() -> dict:
    return {
        **common_fields(),
        "type": "AdditionalQoL",
        AdditionalQoLDTO.VIEW_FAMILY: Regularly.STRONGLY_DISAGREE,
        AdditionalQoLDTO.CONTACT_PROFESSIONALS: Regularly.AGREE,
        AdditionalQoLDTO.CONTRIBUTE_ACTIVITIES: Regularly.NEITHER_AGREE_DISAGREE,
        AdditionalQoLDTO.METADATA: {
            QuestionnaireMetadata.ANSWERS: [
                {
                    QuestionnaireAnswer.QUESTION_ID: "30cad609-cd6a-418d-a1de-6eacfa3a2d9d",
                    QuestionnaireAnswer.QUESTION: "I am able to see my friends and family more in person",
                    QuestionnaireAnswer.ANSWER_TEXT: "AGREE",
                }
            ]
        },
    }
