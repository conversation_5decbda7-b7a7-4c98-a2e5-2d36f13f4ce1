import unittest
from unittest.mock import MagicMock

from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.utils import inject
from sdk.deployment.router.deployment_requests import CreateModuleConfigRequestObject
from sdk.module_result.dtos.module_config import (
    CustomRagThreshold,
    ModuleConfig,
    RagThreshold,
    RagThresholdType,
    ThresholdRange,
    MealGroup,
)
from sdk.module_result.dtos.primitives import (
    ChangeDirection,
)
from sdk.module_result.dtos.primitives import MealType
from sdk.module_result.modules import (
    BloodPressureModule,
    OxfordKneeScoreQuestionnaireModule,
    HeartRateModule,
    BloodGlucoseModule,
)
from sdk.module_result.modules.blood_glucose import BloodGlucoseDTO
from sdk.module_result.modules.blood_pressure import BloodPressureDTO
from sdk.module_result.modules.cat import CAT
from sdk.module_result.modules.heart_rate.primitives import HeartRateDTO
from sdk.module_result.modules.oxford_knee_score import OxfordKneeScoreDTO, LegAffected
from sdk.module_result.modules.weight import WeightModule, WeightDTO
from sdk.phoenix.config.server_config import PhoenixServerConfig

COLOR = RagThreshold.COLOR
DIRECTION = RagThreshold.DIRECTION
VALUE = WeightDTO.VALUE


def sample_module_config() -> dict:
    return {
        "moduleId": "BMI",
        "moduleName": "BMI",
        "status": "ENABLED",
        "ragThresholds": [
            {
                "color": "green",
                "severity": 1,
                "fieldName": "value",
                "type": "VALUE",
                "thresholdRange": [{"minValue": 10, "maxValue": 19}],
                "enabled": True,
            }
        ],
    }


class BaseThresholdTestCase(unittest.TestCase):
    def setUp(self) -> None:
        self.repo = MagicMock()
        self.server_config = MagicMock()

        def configure_with_binder(binder: inject.Binder):
            binder.bind(PhoenixServerConfig, self.server_config)

        inject.clear_and_configure(config=configure_with_binder)


class ApplyThresholdsTestCase(BaseThresholdTestCase):
    @property
    def module(self):
        return WeightModule()

    def module_config(
        self,
        rag_type: RagThresholdType,
        threshold_range_low: list[ThresholdRange],
        threshold_range_high: list[ThresholdRange],
    ):
        return ModuleConfig(
            moduleId="Weight",
            moduleName="Weight",
            ragThresholds=[
                RagThreshold(
                    color="green",
                    severity=1,
                    fieldName="value",
                    type=rag_type,
                    thresholdRange=threshold_range_low,
                    enabled=True,
                ),
                RagThreshold(
                    color="amber",
                    severity=2,
                    fieldName="value",
                    type=rag_type,
                    thresholdRange=threshold_range_high,
                    enabled=True,
                ),
            ],
        )

    def test_apply_threshold_value_type(self):
        primitive = WeightDTO(value=112)

        module_config = self.module_config(
            RagThresholdType.VALUE,
            [ThresholdRange(minValue=110, maxValue=119)],
            [ThresholdRange(minValue=120, maxValue=130)],
        )
        threshold = self.module.calculate_threshold(primitive, module_config, [WeightDTO(value=100)])
        self.assertEqual(threshold[VALUE].color, "green")

        primitive = WeightDTO(value=125)

        threshold = self.module.calculate_threshold(primitive, module_config, [WeightDTO(value=100)])
        self.assertEqual(threshold[VALUE].color, "amber")

    def test_apply_rounding(self):
        """
        Value 85.444 is less than 85.4444445 so it doesn't get RAG.
        But RAG should be reduced to the same decimal as value and become 85.444
        and get amber colour.
        """
        primitive = WeightDTO(value=85.444)

        module_config = self.module_config(
            RagThresholdType.VALUE,
            [ThresholdRange(minValue=68.111111, maxValue=85.199999)],
            [ThresholdRange(minValue=85.4444445, maxValue=98.4444448)],
        )
        threshold = self.module.calculate_threshold(primitive, module_config, [])
        self.assertEqual(threshold[VALUE].color, "amber")

    def test_limit_values_rag_for_integer_based_primitives(self):
        primitive = HeartRateDTO(value=100)
        module_config = self.module_config(
            RagThresholdType.VALUE,
            [ThresholdRange(minValue=0, maxValue=100)],
            [ThresholdRange(minValue=100.1, maxValue=999)],
        )
        threshold = self.module.calculate_threshold(primitive, module_config, [])
        self.assertEqual(threshold[VALUE].color, "green")

    def test_apply_threshold_type_change_number(self):
        module_config = self.module_config(
            RagThresholdType.CHANGE_NUMBER,
            [ThresholdRange(minValue=10, maxValue=19)],
            [ThresholdRange(minValue=20, maxValue=30)],
        )

        primitive = WeightDTO(value=110)

        threshold = self.module.calculate_threshold(primitive, module_config, [WeightDTO(value=100)])
        self.assertEqual(threshold[VALUE].color, "green")

        primitive = WeightDTO(value=125)

        threshold = self.module.calculate_threshold(primitive, module_config, [WeightDTO(value=100)])
        self.assertEqual(threshold[VALUE].color, "amber")

    def test_apply_threshold_type_change_percent(self):
        module_config = self.module_config(
            RagThresholdType.CHANGE_NUMBER,
            [ThresholdRange(minValue=-5, maxValue=5)],
            [
                ThresholdRange(minValue=-20, maxValue=-6),
                ThresholdRange(minValue=6, maxValue=20),
            ],
        )

        primitive = WeightDTO(value=103)

        threshold = self.module.calculate_threshold(primitive, module_config, [WeightDTO(value=100)])
        self.assertEqual(threshold[VALUE].color, "green")

        primitive = WeightDTO(value=112)

        threshold = self.module.calculate_threshold(primitive, module_config, [WeightDTO(value=100)])
        self.assertEqual(threshold[VALUE].color, "amber")

        primitive = WeightDTO(value=95)

        threshold = self.module.calculate_threshold(primitive, module_config, [WeightDTO(value=100)])
        self.assertEqual(threshold[VALUE].color, "green")

        primitive = WeightDTO(value=84)

        threshold = self.module.calculate_threshold(primitive, module_config, [WeightDTO(value=100)])
        self.assertEqual(threshold[VALUE].color, "amber")

    def test_failure_rag_threshold_wrong_fieldname(self):
        with self.assertRaises(InvalidRequestException):
            module_config = sample_module_config()
            module_config["ragThresholds"][0]["fieldName"] = "wrong_filed_name"
            CreateModuleConfigRequestObject.from_dict(module_config)

    def test_failure_rag_threshold_wrong_moduleid(self):
        with self.assertRaises(InvalidRequestException):
            module_config = sample_module_config()
            module_config["moduleId"] = "wrong_module_name"
            CreateModuleConfigRequestObject.from_dict(module_config)


class MultipleFieldsThresholdTestCase(BaseThresholdTestCase):
    @property
    def module(self):
        return BloodPressureModule()

    def module_config(
        self,
        rag_type: RagThresholdType,
        threshold_range_low: list[ThresholdRange],
        threshold_range_high: list[ThresholdRange],
    ):
        return ModuleConfig(
            moduleId="BloodPressure",
            moduleName="BloodPressure",
            ragThresholds=[
                RagThreshold(
                    color="green",
                    severity=1,
                    fieldName="diastolicValue",
                    type=rag_type,
                    thresholdRange=threshold_range_low,
                    enabled=True,
                ),
                RagThreshold(
                    color="amber",
                    severity=2,
                    fieldName="diastolicValue",
                    type=rag_type,
                    thresholdRange=threshold_range_high,
                    enabled=True,
                ),
                RagThreshold(
                    color="yellow",
                    severity=3,
                    fieldName="diastolicValue",
                    type=rag_type,
                    thresholdRange=threshold_range_high,
                    enabled=True,
                ),
                RagThreshold(
                    color="pink",
                    severity=1,
                    fieldName="systolicValue",
                    type=rag_type,
                    thresholdRange=threshold_range_low,
                    enabled=True,
                ),
                RagThreshold(
                    color="red",
                    severity=2,
                    fieldName="systolicValue",
                    type=rag_type,
                    thresholdRange=threshold_range_high,
                    enabled=True,
                ),
            ],
        )

    def test_multiple_field_threshold(self):
        primitive = BloodPressureDTO(diastolicValue=80, systolicValue=100)

        module_config = self.module_config(
            rag_type=RagThresholdType.VALUE,
            threshold_range_low=[ThresholdRange(minValue=60, maxValue=110)],
            threshold_range_high=[ThresholdRange(minValue=111, maxValue=130)],
        )

        threshold = self.module.get_threshold_data(
            primitive,
            module_config,
            [BloodPressureDTO(diastolicValue=65, systolicValue=110)],
        )

        self.assertDictEqual(
            threshold[BloodPressureDTO.DIASTOLIC_VALUE],
            {
                COLOR: "green",
                DIRECTION: ChangeDirection.INCREASED.value,
                RagThreshold.SEVERITY: 1,
                CustomRagThreshold.IS_CUSTOM: False,
            },
        )
        self.assertDictEqual(
            threshold[BloodPressureDTO.SYSTOLIC_VALUE],
            {
                COLOR: "pink",
                DIRECTION: ChangeDirection.DECREASED.value,
                RagThreshold.SEVERITY: 1,
                CustomRagThreshold.IS_CUSTOM: False,
            },
        )

    def test_single_field_threshold(self):
        primitive = BloodPressureDTO(diastolicValue=80)

        module_config = self.module_config(
            rag_type=RagThresholdType.VALUE,
            threshold_range_low=[ThresholdRange(minValue=60, maxValue=110)],
            threshold_range_high=[ThresholdRange(minValue=111, maxValue=130)],
        )

        threshold = self.module.get_threshold_data(
            primitive,
            module_config,
            [BloodPressureDTO(diastolicValue=50, systolicValue=90)],
        )

        self.assertDictEqual(
            threshold[BloodPressureDTO.DIASTOLIC_VALUE],
            {
                COLOR: "green",
                DIRECTION: ChangeDirection.INCREASED.value,
                RagThreshold.SEVERITY: 1,
                CustomRagThreshold.IS_CUSTOM: False,
            },
        )
        self.assertEqual(threshold[BloodPressureDTO.SYSTOLIC_VALUE], {})

    def test_single_field_threshold_custom_rag(self):
        primitive = BloodPressureDTO(diastolicValue=80)

        module_config = self.module_config(
            rag_type=RagThresholdType.VALUE,
            threshold_range_low=[ThresholdRange(minValue=60, maxValue=110)],
            threshold_range_high=[ThresholdRange(minValue=111, maxValue=130)],
        )
        for rag in module_config.ragThresholds:
            rag.isCustom = True

        threshold = self.module.get_threshold_data(
            primitive,
            module_config,
            [BloodPressureDTO(diastolicValue=50, systolicValue=90)],
        )

        self.assertDictEqual(
            threshold[BloodPressureDTO.DIASTOLIC_VALUE],
            {
                COLOR: "green",
                DIRECTION: ChangeDirection.INCREASED.value,
                RagThreshold.SEVERITY: 1,
                CustomRagThreshold.IS_CUSTOM: True,
            },
        )
        self.assertEqual(threshold[BloodPressureDTO.SYSTOLIC_VALUE], {})

    def test_severity_still_has_precedence_for_individual_fields(self):
        primitive = BloodPressureDTO(diastolicValue=115)

        module_config = self.module_config(
            rag_type=RagThresholdType.VALUE,
            threshold_range_low=[ThresholdRange(minValue=60, maxValue=110)],
            threshold_range_high=[ThresholdRange(minValue=111, maxValue=130)],
        )

        threshold = self.module.get_threshold_data(
            primitive,
            module_config,
            [BloodPressureDTO(diastolicValue=65, systolicValue=105)],
        )
        self.assertDictEqual(
            threshold[BloodPressureDTO.DIASTOLIC_VALUE],
            {
                COLOR: "yellow",
                DIRECTION: ChangeDirection.INCREASED.value,
                RagThreshold.SEVERITY: 3,
                CustomRagThreshold.IS_CUSTOM: False,
            },
        )
        self.assertDictEqual(threshold[BloodPressureDTO.SYSTOLIC_VALUE], {})

    def test_threshold_type_change_number_for_multiple_fields(self):
        module_config = self.module_config(
            RagThresholdType.CHANGE_NUMBER,
            [ThresholdRange(minValue=30, maxValue=40)],
            [ThresholdRange(minValue=41, maxValue=60)],
        )

        primitive = BloodPressureDTO(diastolicValue=90, systolicValue=110)

        threshold = self.module.calculate_threshold(
            primitive,
            module_config,
            [BloodPressureDTO(diastolicValue=40, systolicValue=70)],
        )

        self.assertEqual(threshold[BloodPressureDTO.DIASTOLIC_VALUE].color, "yellow")
        self.assertEqual(threshold[BloodPressureDTO.SYSTOLIC_VALUE].color, "pink")

    def test_threshold_type_change_number_for_primitive_with_missing_value(self):
        module_config = self.module_config(
            RagThresholdType.CHANGE_NUMBER,
            [ThresholdRange(minValue=30, maxValue=40)],
            [ThresholdRange(minValue=41, maxValue=60)],
        )

        primitive = BloodPressureDTO(diastolicValue=90, systolicValue=110)

        threshold = self.module.calculate_threshold(
            primitive,
            module_config,
            [BloodPressureDTO(diastolicValue=50)],
        )

        self.assertEqual(threshold[BloodPressureDTO.DIASTOLIC_VALUE].color, "green")

    def _calculate_sample_threshold(self):
        primitive = BloodPressureDTO(diastolicValue=80, systolicValue=100)

        module_config = self.module_config(
            rag_type=RagThresholdType.VALUE,
            threshold_range_low=[ThresholdRange(minValue=60, maxValue=110)],
            threshold_range_high=[ThresholdRange(minValue=111, maxValue=130)],
        )

        return self.module.get_threshold_data(
            primitive,
            module_config,
            [BloodPressureDTO(diastolicValue=50, systolicValue=90)],
        )

    def test_get_threshold_data(self):
        threshold = self._calculate_sample_threshold()
        self.assertEqual(2, len(threshold["severities"]))


class MultipleFieldsOxfordKneeScoreThresholdTestCase(BaseThresholdTestCase):
    @property
    def module(self):
        return OxfordKneeScoreQuestionnaireModule()

    def module_config(self):
        rag_threshold_dicts = [
            {
                RagThreshold.TYPE: RagThresholdType.VALUE.value,
                RagThreshold.SEVERITY: 3,
                RagThreshold.RANGE: [{"maxValue": 19.0}],
                COLOR: "#FBCCD7",
                RagThreshold.FIELD_NAME: OxfordKneeScoreDTO.LEFT_KNEE_SCORE,
                RagThreshold.ENABLED: True,
            },
            {
                RagThreshold.TYPE: RagThresholdType.VALUE.value,
                RagThreshold.SEVERITY: 3,
                RagThreshold.RANGE: [{"maxValue": 19.0}],
                COLOR: "#FBCCD7",
                RagThreshold.FIELD_NAME: OxfordKneeScoreDTO.RIGHT_KNEE_SCORE,
                RagThreshold.ENABLED: True,
            },
            {
                RagThreshold.TYPE: RagThresholdType.VALUE.value,
                RagThreshold.SEVERITY: 2,
                RagThreshold.RANGE: [{"minValue": 20.0, "maxValue": 29.0}],
                COLOR: "#FFDA9F",
                RagThreshold.FIELD_NAME: OxfordKneeScoreDTO.LEFT_KNEE_SCORE,
                RagThreshold.ENABLED: True,
            },
            {
                RagThreshold.TYPE: RagThresholdType.VALUE.value,
                RagThreshold.SEVERITY: 2,
                RagThreshold.RANGE: [{"minValue": 20.0, "maxValue": 29.0}],
                COLOR: "#FFDA9F",
                RagThreshold.FIELD_NAME: OxfordKneeScoreDTO.RIGHT_KNEE_SCORE,
                RagThreshold.ENABLED: True,
            },
            {
                RagThreshold.TYPE: RagThresholdType.VALUE.value,
                RagThreshold.SEVERITY: 1,
                RagThreshold.RANGE: [{"minValue": 30.0, "maxValue": 39.0}],
                COLOR: "#CBEBF0",
                RagThreshold.FIELD_NAME: OxfordKneeScoreDTO.LEFT_KNEE_SCORE,
                RagThreshold.ENABLED: True,
            },
            {
                RagThreshold.TYPE: RagThresholdType.VALUE.value,
                RagThreshold.SEVERITY: 1,
                RagThreshold.RANGE: [{"minValue": 30.0, "maxValue": 39.0}],
                COLOR: "#CBEBF0",
                RagThreshold.FIELD_NAME: OxfordKneeScoreDTO.RIGHT_KNEE_SCORE,
                RagThreshold.ENABLED: True,
            },
        ]
        rag_thresholds = [RagThreshold.from_dict(rag_dict) for rag_dict in rag_threshold_dicts]
        return ModuleConfig(
            moduleId=OxfordKneeScoreQuestionnaireModule.moduleId,
            moduleName=OxfordKneeScoreDTO.get_primitive_name(),
            ragThresholds=rag_thresholds,
        )

    def test_multiple_field_knee_score_threshold(self):
        knee_score_data = {
            OxfordKneeScoreDTO.LEG_AFFECTED: LegAffected.BOTH,
            OxfordKneeScoreDTO.RIGHT_KNEE_SCORE: 14,
            OxfordKneeScoreDTO.LEFT_KNEE_SCORE: 25,
        }

        primitive = OxfordKneeScoreDTO(**knee_score_data)

        module_config = self.module_config()

        threshold = self.module.get_threshold_data(
            primitive,
            module_config,
            [],
        )

        self.assertDictEqual(
            threshold[OxfordKneeScoreDTO.LEFT_KNEE_SCORE],
            {
                COLOR: "#FFDA9F",
                RagThreshold.SEVERITY: 2,
                CustomRagThreshold.IS_CUSTOM: False,
            },
        )
        self.assertDictEqual(
            threshold[OxfordKneeScoreDTO.RIGHT_KNEE_SCORE],
            {
                COLOR: "#FBCCD7",
                RagThreshold.SEVERITY: 3,
                CustomRagThreshold.IS_CUSTOM: False,
            },
        )

        self.assertEqual(2, len(threshold["severities"]))


class ChangeDirectionTestCase(BaseThresholdTestCase):
    def setUp(self):
        super().setUp()

        self.config = ModuleConfig(id="testModuleId", moduleId="HeartRate")

    def test_calculate_change_direction_min_calculated(self):
        with HeartRateModule().configure(self.config) as module:
            primitive = HeartRateDTO(value=99)
            result = module.calculate_change_direction(primitive, [primitive], HeartRateDTO.VALUE)
            self.assertEqual(ChangeDirection.NOCHANGE.value, result)

    def test_calculate_change_direction__exact_value_str_no_calculated(self):
        with HeartRateModule().configure(self.config) as module:
            primitive = HeartRateDTO(classification="AF")
            result = module.calculate_change_direction(primitive, [primitive], HeartRateDTO.CLASSIFICATION)
            self.assertIsNone(result)

    def test_calculate_change_direction_previous_zero(self):
        with HeartRateModule().configure(self.config) as module:
            primitive = CAT(value=10)
            result = module.calculate_change_direction(primitive, [CAT(value=0)], CAT.VALUE)
            self.assertEqual(result, ChangeDirection.INCREASED.value)


class MealGroupThresholdTestCase(BaseThresholdTestCase):
    def setUp(self):
        super().setUp()

        self.config = ModuleConfig(id="testModuleId", moduleId="HeartRate")

    def test_meal_group_threshold_matches(self):
        primitive = BloodGlucoseDTO(value=5, meal=MealType.AFTER_LUNCH)
        self.config.ragThresholds = [self._get_rag_threshold(MealGroup.AFTER_MEAL)]
        with BloodGlucoseModule().configure(self.config) as module:
            threshold = module.calculate_threshold(primitive, self.config, [])
            self.assertEqual(1, threshold["value"].severity)

    def test_meal_group_threshold_not_configured(self):
        primitive = BloodGlucoseDTO(value=5, meal=MealType.BEFORE_LUNCH)
        self.config.ragThresholds = [self._get_rag_threshold()]
        with BloodGlucoseModule().configure(self.config) as module:
            threshold = module.calculate_threshold(primitive, self.config, [])
            self.assertEqual(1, threshold["value"].severity)

    def test_meal_group_threshold_not_matching_primitive(self):
        primitive = BloodGlucoseDTO(value=5, meal=MealType.BEFORE_LUNCH)
        self.config.ragThresholds = [self._get_rag_threshold(MealGroup.AFTER_MEAL)]
        with BloodGlucoseModule().configure(self.config) as module:
            threshold = module.calculate_threshold(primitive, self.config, [])
            self.assertIsNone(threshold["value"])

    def test_use_default_rag_threshold_if_wrong_meal_group_is_provided(self):
        primitive = BloodGlucoseDTO(value=12, meal=MealType.BEFORE_LUNCH)
        self.config.ragThresholds = [
            self._get_rag_threshold(MealGroup.AFTER_MEAL),
            RagThreshold(
                enabled=True,
                type=RagThresholdType.VALUE,
                fieldName="value",
                severity=2,
                thresholdRange=[ThresholdRange(minValue=1, maxValue=20.0)],
            ),
        ]
        with BloodGlucoseModule().configure(self.config) as module:
            threshold = module.calculate_threshold(primitive, self.config, [])
            self.assertEqual(2, threshold["value"].severity)

            primitive.value = 5
            threshold = module.calculate_threshold(primitive, self.config, [])
            self.assertEqual(2, threshold["value"].severity)

    def test_meal_less_primitive_for_configured_threshold(self):
        primitive = BloodGlucoseDTO(value=5)
        self.config.ragThresholds = [self._get_rag_threshold(MealGroup.NONE)]
        with BloodGlucoseModule().configure(self.config) as module:
            threshold = module.calculate_threshold(primitive, self.config, [])
            self.assertEqual(1, threshold["value"].severity)

    def test_meal_less_primitive_mismatched_threshold_meal_group(self):
        primitive = BloodGlucoseDTO(value=5)
        self.config.ragThresholds = [self._get_rag_threshold(MealGroup.AFTER_MEAL)]
        with BloodGlucoseModule().configure(self.config) as module:
            threshold = module.calculate_threshold(primitive, self.config, [])
            self.assertIsNone(threshold["value"])

    @staticmethod
    def _get_rag_threshold(meal_group=None):
        return RagThreshold(
            enabled=True,
            type=RagThresholdType.VALUE,
            fieldName="value",
            severity=1,
            mealGroup=meal_group,
            thresholdRange=[ThresholdRange(minValue=1, maxValue=10.0)],
        )
