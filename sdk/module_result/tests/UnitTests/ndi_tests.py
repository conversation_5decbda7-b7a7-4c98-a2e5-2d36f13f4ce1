from pathlib import Path
from typing import Optional
from unittest import TestCase
from unittest.mock import MagicMock

from sdk.common.localization.utils import Language
from sdk.common.utils import inject
from sdk.common.utils.json_utils import replace_values
from sdk.common.utils.validators import read_json_file
from sdk.deployment.dtos.status import EnableStatus
from sdk.module_result.dtos.module_config import ModuleConfig
from sdk.module_result.exceptions import (
    NotAllRequiredQuestionsAnsweredException,
    MoreThanOneQuestionnaireSubmittedException,
    NotQuestionnaireSubmittedException,
)
from sdk.module_result.modules.licensed_questionnaire_module import get_localizations
from sdk.module_result.modules.modules_manager import ModulesManager
from sdk.module_result.modules.ndi import NDIModule, NDIDTO
from sdk.module_result.modules.questionnaire import QuestionnaireDTO
from sdk.module_result.tests.IntegrationTests.test_samples import (
    sample_ndi_questionnaire_result_all_answers,
    sample_ndi_questionnaire_result_skipped_answers,
)
from sdk.phoenix.config.server_config import PhoenixServerConfig, Server
from sdk.versioning.models.version import Version
from sdk.versioning.models.version_field import VersionField

USER_ID = "6131bdaaf9af87a4f08f4d02"
SIMPLE_CALCULATOR_PATH = "sdk.module_result.modules.questionnaire.calculators._simple_score_calculator"
NDI_CALCULATOR_PATH = "sdk.module_result.modules.ndi._calculator"


class NDITestCase(TestCase):
    def setUp(self):
        self.version = Version(server=VersionField("1.30.0"), api="api")
        self.config = MagicMock()
        self.config.server = Server(hostUrl="localhost")

        def bind(binder):
            binder.bind(Version, self.version)
            binder.bind(PhoenixServerConfig, self.config)
            binder.bind(ModulesManager, MagicMock())

        inject.clear_and_configure(bind)

        self.module = NDIModule()
        self.mc = ModuleConfig(
            moduleId=NDIModule.moduleId,
            configBody=self._get_config_body(),
            status=EnableStatus.ENABLED,
        )
        self.module.config = self.mc
        self.primitive = self._get_ndi_primitive()

    @staticmethod
    def _get_config_body() -> Optional[dict]:
        loc = Path(__file__).parents[2]
        path = "modules/licensed_questionnaire_module/licensed_configs/NDI/v1/config_body.json"
        config_body_file = read_json_file(path, loc)
        return config_body_file.get(ModuleConfig.CONFIG_BODY, {})

    def _get_ndi_primitive(self, all_answers=True):
        localization = get_localizations(Language.EN, Language.EN, [NDIModule.moduleId])
        reverse_localization = {val: key for key, val in localization.items()}

        raw_data = (
            sample_ndi_questionnaire_result_all_answers()
            if all_answers
            else sample_ndi_questionnaire_result_skipped_answers()
        )
        raw_data = replace_values(raw_data, reverse_localization)

        return QuestionnaireDTO.from_dict({**raw_data, QuestionnaireDTO.USER_ID: USER_ID})

    def test_ndi_module_preprocess_success(self):
        primitives = [self.primitive]
        self.module.preprocess(primitives, MagicMock())
        self.assertEqual(2, len(primitives))
        ndi_primitive = primitives[-1]
        self.assertEqual(0, ndi_primitive.value)
        self.assertEqual(
            len(sample_ndi_questionnaire_result_all_answers()[QuestionnaireDTO.ANSWERS]),
            len(ndi_primitive.answers),
        )
        for primitive in primitives:
            self.assertIsNotNone(primitive.server)

    def test_ndi_module_preprocess_errors(self):
        primitives = [self.primitive] * 2
        with self.assertRaises(MoreThanOneQuestionnaireSubmittedException):
            self.module.preprocess(primitives, MagicMock())

    def test_not_all_required_questions_answered_error(self):
        primitive = self._get_ndi_primitive(all_answers=False)
        self.module.preprocess([primitive], MagicMock())

        primitive.answers = primitive.answers[:-1]
        with self.assertRaises(NotAllRequiredQuestionsAnsweredException):
            self.module.preprocess([primitive], MagicMock())

    def test_wrong_type_cannot_be_submitted(self):
        ndi_data = {**self.primitive.to_dict(include_none=False), NDIDTO.VALUE: 0}
        ndi_primitive = NDIDTO.from_dict(ndi_data)
        with self.assertRaises(NotQuestionnaireSubmittedException):
            self.module.preprocess([ndi_primitive], MagicMock())

    def test_calculate_ndi(self):
        primitives = [self.primitive]

        self.module.preprocess(primitives, MagicMock())
        questionnaire_primitive, ndi_primitive = primitives[0], primitives[1]

        self.assertIsNone(questionnaire_primitive.value)
        self.assertEqual(0, ndi_primitive.value)

        self.module.calculate(ndi_primitive)

        self.assertEqual(50, ndi_primitive.value)

    def test_default_sort_primitive_is_ndi(self):
        self.assertEqual(NDIDTO.get_primitive_name(), self.module.default_sort_primitive)
