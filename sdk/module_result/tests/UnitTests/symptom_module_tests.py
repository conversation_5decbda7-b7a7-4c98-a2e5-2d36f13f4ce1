import unittest
from unittest.mock import patch, MagicMock

from sdk.common.utils import inject
from sdk.common.utils.convertible import ConvertibleClassValidationError
from sdk.deployment.dtos.status import EnableStatus
from sdk.module_result.dtos.module_config import ModuleConfig
from sdk.module_result.exceptions import InvalidModuleConfiguration
from sdk.module_result.modules import SymptomModule, SymptomV2Module
from sdk.module_result.modules.questionnaire import QuestionnaireDTO
from sdk.module_result.modules.symptom import SymptomDTO, ComplexSymptomValue
from sdk.phoenix.config.server_config import PhoenixServerConfig, Server
from sdk.versioning.models.version import Version
from sdk.versioning.models.version_field import VersionField

PATH = "sdk.module_result.modules.symptom._module"
SYMPTOM_V2_PATH = "sdk.module_result.modules.symptom._module"
FOLLOW_UP_PATH = "sdk.module_result.modules.follow_up_questionnaire._module"


class MockRagThreshold:
    instance = MagicMock()
    fieldName = "some_name"
    thresholdRange = [MagicMock()]
    severity = 0
    color = "some_color"


class MockComplexValue:
    def __init__(self, severity: int = 1, name: str = "Persistent cough"):
        self.severity = severity
        self.name = name

    instance = MagicMock()


sample_config = {
    "complexSymptoms": [
        {
            "name": "Persistent cough",
            "scale": [
                {"severity": 1, "value": "Mild"},
                {"severity": 2, "value": "Moderate"},
            ],
        }
    ]
}


class SymptomModuleTestCase(unittest.TestCase):
    def setUp(self) -> None:
        self.repo = MagicMock()
        self.module_config = ModuleConfig(moduleId=SymptomModule.__name__, configBody=sample_config)

        def configure_with_binder(binder: inject.Binder):
            binder.bind(PhoenixServerConfig, MagicMock())

        inject.clear_and_configure(config=configure_with_binder)

    @patch(f"{PATH}.read_json_file")
    def test_get_validation_schema(self, read_json_file):
        module = SymptomModule()
        module.get_validation_schema()
        read_json_file.assert_called_once()

    @patch(f"{PATH}.SymptomModule.calculate_threshold")
    def test_get_threshold_data(self, calculate_threshold):
        target_primitive = MagicMock()
        module_config = MagicMock()
        primitives = [target_primitive]
        module = SymptomModule()
        calculate_threshold.return_value = MagicMock(), MagicMock()
        module.get_threshold_data(target_primitive, module_config, primitives)
        calculate_threshold.assert_called_with(target_primitive, module_config, primitives)

    @patch(f"{PATH}.SymptomModule._match_threshold_range")
    def test_calculate_threshold(self, match_threshold):
        primitive = SymptomDTO()
        value = MagicMock()
        value.name = "some_name"
        primitive.complexValues = [value]
        module_config = MagicMock()
        rag_threshold = MockRagThreshold()
        module_config.ragThresholds = [rag_threshold]
        module = SymptomModule()
        module.calculate_threshold(primitive, module_config, [primitive])
        match_threshold.assert_called_with(rag_threshold.thresholdRange[0], value.severity)

    @patch(f"{PATH}.SymptomModule._validate_severity_range")
    def test_validate_severity(self, validate_severity_range):
        primitive = MagicMock()
        primitive.complexValues = [MockComplexValue()]
        with SymptomModule().configure(self.module_config) as module:
            module.preprocess([primitive], MagicMock())
            validate_severity_range.assert_called_with(primitive)

    def test_success_validate_severity(self):
        primitive = MagicMock()
        primitive.complexValues = [MockComplexValue()]
        with SymptomModule().configure(self.module_config) as module:
            module._validate_severity_range(primitive)

    def test_failure_validate_severity_invalid_symptom_name(self):
        primitive = MagicMock()
        primitive.complexValues = [MockComplexValue(name="Some name")]
        with SymptomModule().configure(self.module_config) as module:
            with self.assertRaises(ConvertibleClassValidationError):
                module._validate_severity_range(primitive)

    def test_failure_validate_severity_invalid_severity(self):
        primitive = MagicMock()
        primitive.complexValues = [MockComplexValue(severity=6)]
        with SymptomModule().configure(self.module_config) as module:
            with self.assertRaises(ConvertibleClassValidationError):
                module._validate_severity_range(primitive)

    def test_apply_overall_flags_logic(self):
        sample_primitive = MagicMock(**{SymptomDTO.COMPLEX_VALUES: [1, 2, 3]})
        primitives = [sample_primitive, sample_primitive]
        module = SymptomModule()
        module.config = MagicMock(spec_set=ModuleConfig)
        module.config.ragThresholds = None
        module.apply_overall_flags_logic(primitives)
        expected_res = {"red": 0, "amber": 0, "gray": 6}
        self.assertEqual(expected_res, primitives[0].flags)

    def test_apply_overall_flags_logic_with_none(self):
        sample_primitive = MagicMock(**{SymptomDTO.COMPLEX_VALUES: None})
        primitives = [sample_primitive, sample_primitive]
        module = SymptomModule()
        module.config = MagicMock(spec_set=ModuleConfig)
        module.config.ragThresholds = None
        module.apply_overall_flags_logic(primitives)
        expected_res = {"red": 0, "amber": 0, "gray": 0}
        self.assertEqual(expected_res, primitives[0].flags)


class SymptomV2ModuleTestCase(unittest.TestCase):
    def setUp(self) -> None:
        server_config = MagicMock()
        server_config.server = Server(hostUrl="localhost")
        version = Version(server=VersionField("1.17.1"), api="api", build="build")
        self.module_config = ModuleConfig(moduleId=SymptomV2Module.__name__, configBody={})

        def configure_with_binder(binder: inject.Binder):
            binder.bind(PhoenixServerConfig, server_config)
            binder.bind(Version, version)

        inject.clear_and_configure(config=configure_with_binder)

    @patch(f"{SYMPTOM_V2_PATH}.SymptomModule.preprocess")
    def test_mock_symptom_module_preprocess(self, symptom_module_preprocess):
        self.assert_parent_preprocess_have_been_called(symptom_module_preprocess)

    @patch(f"{FOLLOW_UP_PATH}.FollowUpQuestionnaireModule.preprocess")
    def test_mock_follow_up_module_module_preprocess(self, follow_up_module_preprocess):
        self.assert_parent_preprocess_have_been_called(follow_up_module_preprocess)

    def assert_parent_preprocess_have_been_called(self, preprocess_method):
        primitives = [
            SymptomDTO(complexValues=[ComplexSymptomValue(name="Headache", severity=2)]),
            QuestionnaireDTO(answers=[MagicMock()]),
        ]
        user = MagicMock()
        with SymptomV2Module().configure(self.module_config) as module:
            module.preprocess(primitives, user)
            preprocess_method.assert_called_with(primitives, user)

    @staticmethod
    def _sample_module_config(complex_symptoms=None):
        if complex_symptoms is None:
            complex_symptoms = []
        config_body = {"complexSymptoms": complex_symptoms}
        module_config = MagicMock(configBody=config_body)
        module_config.status = EnableStatus.ENABLED
        return module_config

    def test_validate_module_config__no_complex_symptoms(self):
        module_config = self._sample_module_config()
        with SymptomV2Module().configure(self.module_config) as module:
            module.validate_module_config(module_config)
            self.assertEqual(EnableStatus.DISABLED, module_config.status)

    def test_validate_module_config__symptom_empty_dict(self):
        module_config = self._sample_module_config([{}])
        with SymptomV2Module().configure(self.module_config) as module:
            with self.assertRaises(InvalidModuleConfiguration):
                module.validate_module_config(module_config)

    def test_validate_module_config__complex_symptoms_exist(self):
        module_config = self._sample_module_config(
            [
                {
                    "name": "Chills",
                    "scale": [
                        {"severity": 1, "value": "never"},
                        {"severity": 2, "value": "seldom"},
                        {"severity": 3, "value": "usually"},
                        {"severity": 4, "value": "often"},
                    ],
                }
            ]
        )
        with SymptomV2Module().configure(self.module_config) as module:
            module.validate_module_config(module_config)
            self.assertEqual(EnableStatus.ENABLED, module_config.status)

    def test_validate_module_config__complex_symptoms_exceed_the_limit(self):
        module_config = self._sample_module_config(
            [
                {
                    "name": "Chills",
                    "scale": [
                        {"severity": 1, "value": "never"},
                        {"severity": 2, "value": "seldom"},
                        {"severity": 3, "value": "usually"},
                        {"severity": 4, "value": "often"},
                        {"severity": 4, "value": "often"},
                    ],
                }
            ]
        )
        with SymptomV2Module().configure(self.module_config) as module:
            with self.assertRaises(InvalidModuleConfiguration):
                module.validate_module_config(module_config)

    def test_validate_module_config__severities_exceed_the_limit(self):
        module_config = self._sample_module_config(
            [
                {
                    "name": "Chills",
                    "scale": [
                        {"severity": 1, "value": "never"},
                        {"severity": 2, "value": "seldom"},
                        {"severity": 6, "value": "often"},
                        {"severity": 7, "value": "often"},
                    ],
                }
            ]
        )
        with SymptomV2Module().configure(self.module_config) as module:
            with self.assertRaises(ConvertibleClassValidationError):
                module.validate_module_config(module_config)

    def test_validate_module_config__complex_symptoms_not_a_list(self):
        module_config = self._sample_module_config([{"a": "b"}])
        with SymptomV2Module().configure(self.module_config) as module:
            with self.assertRaises(ConvertibleClassValidationError):
                module.validate_module_config(module_config)

    def test_validate_module_config__same_follow_up_ids(self):
        module_config = self._sample_module_config(
            [
                {
                    "name": "Chills",
                    "scale": [{"severity": 1, "value": "never"}],
                    "followUpQuestionIds": ["id1"],
                },
                {
                    "name": "Other Symptom",
                    "scale": [{"severity": 1, "value": "never"}],
                    "followUpQuestionIds": ["id1"],
                },
            ]
        )
        with SymptomV2Module().configure(self.module_config) as module:
            with self.assertRaises(InvalidModuleConfiguration):
                module.validate_module_config(module_config)

    def test_validate_module_config__same_follow_up_ids_in_global_config(self):
        complex_symptoms = [
            {
                "name": "Chills",
                "scale": [{"severity": 1, "value": "never"}],
                "followUpQuestionIds": ["id1"],
            }
        ]
        follow_up_questions_ids = ["id1"]
        module_config = MagicMock(
            configBody={
                "complexSymptoms": complex_symptoms,
                "followUpQuestionIds": follow_up_questions_ids,
            }
        )
        with SymptomV2Module().configure(self.module_config) as module:
            with self.assertRaises(InvalidModuleConfiguration):
                module.validate_module_config(module_config)

    def test_validate_module_config__same_follow_up_ids_in_same_symptom(self):
        module_config = MagicMock(
            configBody={
                "complexSymptoms": [
                    {
                        "name": "Chills",
                        "scale": [{"severity": 1, "value": "never"}],
                        "followUpQuestionIds": ["id1", "id1"],
                    }
                ],
                "pages": [self._sample_page()],
            }
        )
        with SymptomV2Module().configure(self.module_config) as module:
            with self.assertRaises(InvalidModuleConfiguration):
                module.validate_module_config(module_config)

    def test_validate_module_config__same_ids_in_question_items(self):
        module_config = MagicMock(
            configBody={
                "complexSymptoms": [],
                "pages": [self._sample_page(), self._sample_page()],
            }
        )
        with SymptomV2Module().configure(self.module_config) as module:
            with self.assertRaises(InvalidModuleConfiguration):
                module.validate_module_config(module_config)

    def test_validate_module_config__non_existing_follow_up_ids(self):
        module_config = MagicMock(
            configBody={
                "complexSymptoms": [
                    {
                        "name": "Chills",
                        "scale": [{"severity": 1, "value": "never"}],
                        "followUpQuestionIds": ["id1", "id2"],
                    }
                ],
                "pages": [self._sample_page(item_id="id3")],
            }
        )
        with SymptomV2Module().configure(self.module_config) as module:
            with self.assertRaises(InvalidModuleConfiguration):
                module.validate_module_config(module_config)

    def test_set_fields_on_module_update(self):
        old_module_configs = MagicMock(
            configBody={"complexSymptoms": [], "followUpQuestionIds": [], "pages": []},
            moduleId=SymptomV2Module.moduleId,
        )
        updated_configs = MagicMock(
            configBody={
                "complexSymptoms": [
                    {
                        "name": "Chills",
                        "scale": [{"severity": 1, "value": "never"}],
                        "followUpQuestionIds": ["id1", "id2"],
                    }
                ],
                "pages": [self._sample_page(item_id="id3")],
            },
            moduleId=SymptomV2Module.moduleId,
        )
        with SymptomV2Module().configure(self.module_config) as module:
            try:
                module.set_fields_on_module_update(updated_configs, [old_module_configs])
            except AttributeError as e:
                self.fail(e)

    def test_symptom_v2_submit_zero_severity_to_disabled_symptom(self):
        module_config = ModuleConfig(
            moduleId=SymptomV2Module.moduleId,
            configBody={
                "complexSymptoms": [
                    {
                        "name": "Symptom1",
                        "scale": [
                            {"severity": 1, "value": "Mild"},
                            {"severity": 2, "value": "Moderate"},
                            {"severity": 3, "value": "Severe"},
                            {"severity": 4, "value": "Very Severe"},
                        ],
                        "followUpQuestionIds": ["Q1"],
                        "enabled": False,
                        "scaleUpdateDateTime": "2022-11-29T06:13:53.947557Z",
                    }
                ],
                "followUpQuestionIds": ["Q2"],
                "pages": [],
            },
        )

        with SymptomV2Module().configure(module_config) as module:
            primitives = [SymptomDTO(complexValues=[ComplexSymptomValue(name="Symptom1", severity=0)])]
            try:
                module.preprocess(primitives, MagicMock())
            except ValueError as e:
                self.fail(e)

    @staticmethod
    def _sample_page(page_id: str = "d23deeb0-a72e-4ab0-bba0-e52bc6fb1666", item_id: str = "id1") -> dict:
        return {
            "type": "QUESTION",
            "id": page_id,
            "order": 2,
            "text": "Some hint?",
            "description": "Explain more here?",
            "items": [
                {
                    "id": item_id,
                    "format": "TEXT",
                    "order": 1,
                    "text": "Text Question",
                    "showIfNoSymptomsReported": True,
                    "description": "Text Question Description?",
                    "regex": "[0-9]{8}",
                    "defaultValue": "some text",
                    "placeholder": "enter your phone here",
                    "required": False,
                }
            ],
        }


if __name__ == "__main__":
    unittest.main()
