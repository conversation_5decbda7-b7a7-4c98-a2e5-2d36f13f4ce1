import unittest
from unittest.mock import MagicMock, patch

from sdk.authorization.dtos.user import UserDTO
from sdk.common.utils import inject
from sdk.common.utils.convertible import ConvertibleClassValidationError
from sdk.common.utils.inject import Binder
from sdk.module_result.dtos.module_config import ModuleConfig
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.exceptions import (
    NotQuestionnaireSubmittedException,
    MoreThanOneQuestionnaireSubmittedException,
)
from sdk.module_result.modules import DashQuestionnaireModule
from sdk.module_result.modules.dash_score import DashScoreDTO, DashQuestionnaireCalculator
from sdk.module_result.modules.modules_manager import ModulesManager
from sdk.module_result.modules.questionnaire import QuestionnaireDTO
from sdk.module_result.tests.IntegrationTests.test_samples import (
    sample_dash_questionnaire_result,
    sample_dash_primitive,
)
from sdk.phoenix.config.server_config import Server, PhoenixServerConfig
from sdk.versioning.models.version import Version
from sdk.versioning.models.version_field import VersionField

SAMPLE_ID = "60fa9d91632c017458608307"
TEST_USER_ID = "5e8f0c74b50aa9656c34789a"


def user():
    return UserDTO(id=TEST_USER_ID)


def sample_data(is_error=False, add_optional=False):
    return {
        **sample_dash_questionnaire_result(is_error, add_optional),
        PrimitiveDTO.USER_ID: SAMPLE_ID,
        PrimitiveDTO.MODULE_ID: SAMPLE_ID,
    }


def sample_primitive(default_value=2):
    data = {
        **sample_dash_primitive(default_value),
        PrimitiveDTO.USER_ID: SAMPLE_ID,
        PrimitiveDTO.MODULE_ID: SAMPLE_ID,
    }
    return DashScoreDTO.from_dict(data)


sample_module_config = {
    "moduleId": "DashScore",
    "moduleName": "dash_moduleName",
    "shortModuleName": "Dash",
    "ragThresholds": [],
    "schedule": {"friendlyText": "AS NEEDED", "timesOfDay": [], "timesPerDuration": 0},
    "status": "ENABLED",
    "about": "hu_dash_body",
    "order": 59,
    "configBody": {},
}


class DashScoreTestCase(unittest.TestCase):
    def setUp(self) -> None:
        server_config = MagicMock()
        server_config.server = Server(hostUrl="localhost")
        version = Version(server=VersionField("1.17.1"), api="api", build="build")

        def configure_with_binder(binder: Binder):
            binder.bind(PhoenixServerConfig, server_config)
            binder.bind(Version, version)
            binder.bind(ModulesManager, ModulesManager())

        inject.clear_and_configure(config=configure_with_binder)
        self.module_config = ModuleConfig.from_dict(sample_module_config, use_validator_field=False)

    def test_success_creation(self):
        data = sample_data(add_optional=True)
        primitive = QuestionnaireDTO.from_dict(data)
        module = DashQuestionnaireModule()
        module.config = module.extract_module_config(module_configs=[self.module_config], primitive=None)
        primitives = [primitive]
        module.preprocess(primitives, user())
        self.assertEqual(2, len(primitives))

    def test_failed_creation(self):
        data = sample_data(is_error=True, add_optional=True)
        primitive = QuestionnaireDTO.from_dict(data)
        module = DashQuestionnaireModule()
        module.config = module.extract_module_config(module_configs=[self.module_config], primitive=None)
        primitives = [primitive]
        with self.assertRaises(ConvertibleClassValidationError):
            module.preprocess(primitives, user())

    def test_error_many_primitives(self):
        with self.assertRaises(MoreThanOneQuestionnaireSubmittedException):
            DashQuestionnaireModule().preprocess([DashScoreDTO, QuestionnaireDTO], MagicMock())

    def test_error_incorrect_type(self):
        with self.assertRaises(NotQuestionnaireSubmittedException):
            DashQuestionnaireModule().preprocess([DashScoreDTO], MagicMock())

    def test_calculate_only_quick_dash(self):
        primitive = sample_primitive()
        DashQuestionnaireCalculator().calculate(primitive, self.module_config)
        self.assertIsNone(primitive.workScore)
        self.assertIsNone(primitive.sportArtScore)
        self.assertEqual(25, primitive.quickDashScore)

    def test_calculate_incorrect_type(self):
        primitive = None
        with patch.object(DashQuestionnaireCalculator, "_process_quick_dash") as mock_process:
            DashQuestionnaireCalculator().calculate(primitive, self.module_config)
        mock_process.assert_not_called()
