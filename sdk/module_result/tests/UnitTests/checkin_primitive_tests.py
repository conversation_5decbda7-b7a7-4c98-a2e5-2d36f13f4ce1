import unittest

from sdk.common.utils.convertible import ConvertibleClassValidationError
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.dtos.primitives.primitive_checkin_dto import CheckinDTO
from sdk.module_result.tests.IntegrationTests.test_samples import common_fields

SAMPLE_ID = "61724a9675a276cff9ac72c7"


class CheckInTestCase(unittest.TestCase):
    @staticmethod
    def _sample_data():
        return {
            **common_fields(),
            PrimitiveDTO.USER_ID: SAMPLE_ID,
            PrimitiveDTO.MODULE_ID: SAMPLE_ID,
            CheckinDTO.VALUE: "some_value",
        }

    def test_success_create_checkin_primitive(self):
        data = self._sample_data()
        try:
            CheckinDTO.from_dict(data)
        except ConvertibleClassValidationError:
            self.fail()

    def test_failure_create_checkin__no_required_fields(self):
        data = self._sample_data()
        data.pop(CheckinDTO.VALUE)
        with self.assertRaises(ConvertibleClassValidationError):
            CheckinDTO.from_dict(data)


if __name__ == "__main__":
    unittest.main()
