import unittest
from unittest.mock import MagicMock

from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.module_result.exceptions import AnswersNotProvidedOrInvalidException
from sdk.module_result.dtos.module_config import ModuleConfig
from sdk.module_result.modules.questionnaire import QuestionnaireAnswer
from sdk.module_result.modules import SelfRatedHealthModule

SAMPLE_CONFIG_BODY = {
    "pages": [
        {
            "type": "QUESTION",
            "items": [
                {
                    "id": "hu_srh_health",
                    "format": "TEXTCHOICE",
                    "text": "hu_srh_health_text",
                    "description": "",
                    "options": [
                        {"label": "hu_srh_health_label1", "value": "1", "weight": 1},
                        {"label": "hu_srh_health_label2", "value": "2", "weight": 2},
                        {"label": "hu_srh_health_label3", "value": "3", "weight": 3},
                        {"label": "hu_srh_health_label4", "value": "4", "weight": 4},
                        {"label": "hu_srh_health_label5", "value": "5", "weight": 5},
                    ],
                    "order": 1,
                    "required": True,
                    "selectionCriteria": "SINGLE",
                }
            ],
            "order": 1,
        }
    ],
    "publisherName": "RT",
    "submissionPage": {
        "id": "hu_srh_submission",
        "type": "SUBMISSION",
        "text": "hu_srh_submission_text",
        "description": "hu_srh_submission_description",
        "buttonText": "hu_srh_submission_button",
        "order": 2,
    },
}

ANSWERS = [
    {
        QuestionnaireAnswer.ANSWER_TEXT: "hu_srh_health_label3",
        QuestionnaireAnswer.QUESTION_ID: "hu_srh_health",
        QuestionnaireAnswer.QUESTION: "In general, how would you rate your physical health?",
    }
]

WRONG_ANSWERS = [
    {
        QuestionnaireAnswer.ANSWER_TEXT: "WRONG",
        QuestionnaireAnswer.QUESTION_ID: "hu_srh_health",
        QuestionnaireAnswer.QUESTION: "In general, how would you rate your physical health?",
    }
]


class SelfRatedHealthModuleTestCase(unittest.TestCase):
    def setUp(self) -> None:
        self.module = SelfRatedHealthModule()
        module_config = ModuleConfig(MagicMock())
        module_config.configBody = SAMPLE_CONFIG_BODY
        self.module.config = module_config
        self.answers = [QuestionnaireAnswer.from_dict(a) for a in ANSWERS]
        self.primitive = MagicMock()

    def test_calculate(self):
        self.primitive.answers = self.answers
        self.module.calculate(self.primitive)
        self.assertEqual(3, self.primitive.value)

    def test_failure_no_answers_passed(self):
        self.primitive.answers = []
        with self.assertRaises(AnswersNotProvidedOrInvalidException):
            self.module.calculate(self.primitive)

    def test_failure_wrong_option(self):
        self.primitive.answers = [QuestionnaireAnswer.from_dict(a) for a in WRONG_ANSWERS]
        with self.assertRaises(InvalidRequestException):
            self.module.calculate(self.primitive)

    def test_failure_missing_weight(self):
        self.primitive.answers = self.answers
        # remove weight from third option
        self.module.config.configBody["pages"][0]["items"][0]["options"][2].pop("weight")
        with self.assertRaises(InvalidRequestException):
            self.module.calculate(self.primitive)
