from collections import defaultdict
from datetime import date, datetime

from ordered_set import OrderedSet

from sdk.common.adapter.event_bus_adapter import EventBusAdapter
from sdk.common.utils import inject
from sdk.common.utils.common_functions_utils import find
from sdk.common.utils.date_utils import localize_from_utc, localize_to_utc
from sdk.common.utils.validators import utc_str_field_to_val
from sdk.module_result.dtos.diary_item import ChipsType, DiaryBackgroundColor, DiaryDetail, DiaryItem
from sdk.module_result.event_bus.pre_generate_diary_event import PreGenerateDiaryEvent
from sdk.module_result.modules.diary_generator import DiaryGenerator
from sdk.module_result.modules.diary_generators import PrimitiveDiaryGenerator
from sdk.module_result.modules.step._primitive import StepDTO


class StepDiaryGenerator(DiaryGenerator):
    supportedPrimitives = [StepDTO]

    def __init__(self, module=None, timezone: str | None = None):
        super().__init__(module, timezone)
        self.primitives: list[StepDTO] | None = None

    def generate(self, primitives: list[StepDTO]) -> list[DiaryItem]:
        self.primitives = [p for p in primitives if type(p) in (self.supportedPrimitives or [])]
        primitives.sort(key=lambda p: p.startDateTime)
        if not self.primitives:
            return []
        primitives_data = []
        date_to_daily_data = self._group_primitives_by_day()
        for date_, data in date_to_daily_data.items():
            day_diary = self._get_diary_dict(data, date_)
            if existing_diary := self._get_diary_for_date(date_, data):
                day_diary = self._merge_diaries(existing_diary, day_diary)
            primitives_data.append(day_diary)
        return [DiaryItem.from_dict(primitive_data, ignore_none=True) for primitive_data in primitives_data]

    def _group_primitives_by_day(self) -> defaultdict[date, dict]:
        """
        For user's each local day - map amount of steps per hour in UTC.
        """

        def daily_data() -> dict:
            return {"steps": defaultdict(int), "refPrimitive": None, "sources": OrderedSet()}

        result = defaultdict(daily_data)
        for p in self.primitives:
            if p.multipleValues:
                for value_item in p.multipleValues:
                    date_ = localize_from_utc(value_item.d, self.timezone).date()
                    result[date_]["steps"][value_item.d.replace(minute=0, second=0, microsecond=0)] += value_item.h["0"]
                    result[date_]["refPrimitive"] = p
                    result[date_]["sources"].add(p.source)
            else:
                date_ = localize_from_utc(p.startDateTime, self.timezone).date()
                result[date_]["steps"][p.startDateTime.replace(minute=0, second=0, microsecond=0)] += p.value
                result[date_]["refPrimitive"] = p
                result[date_]["sources"].add(p.source)
        return result

    def _get_diary_dict(self, data: dict, date_: date) -> dict:
        reference_primitive = data["refPrimitive"]
        value = sum((hourly_steps for hourly_steps in data["steps"].values()))
        start_date_time = (
            reference_primitive.startDateTime
            if localize_from_utc(reference_primitive.startDateTime, self.timezone).date() == date_
            else list(data["steps"].keys())[0]
        )
        diary_dict = {
            **PrimitiveDiaryGenerator.common_primitive_data(reference_primitive, start_date_time),
            DiaryItem.VALUE: [str(int(value))],
            DiaryItem.UNIT: [f"Diary.unit.{reference_primitive.moduleId}"],
            DiaryItem.DIARY_BG: DiaryBackgroundColor.GRAY_LIGHT.value,
            DiaryItem.DIARY_SIDEBAR: DiaryBackgroundColor.GRAY.value,
            DiaryItem.DIARY_CHIPS: ChipsType.get_chips([DiaryItem.parsed_source(source) for source in data["sources"]]),
            DiaryItem.FLAGS: {"amber": 0, "gray": 1, "red": 0},
            DiaryItem.DETAILS: [
                {DiaryDetail.KEY: utc_str_field_to_val(time_stamp), DiaryDetail.VALUE: str(int(steps))}
                for time_stamp, steps in data["steps"].items()
            ],
        }
        return diary_dict

    def _get_diary_for_date(self, date_: date, data: dict) -> DiaryItem | None:
        event_bus = inject.instance(EventBusAdapter)
        ref_primitive = data["refPrimitive"]
        event = PreGenerateDiaryEvent(
            diary_type=ref_primitive.moduleId,
            user_id=ref_primitive.userId,
            from_dt=localize_to_utc(datetime.combine(date_, datetime.min.time()), self.timezone),
            to_dt=localize_to_utc(datetime.combine(date_, datetime.max.time()), self.timezone),
        )
        result: list[dict] = event_bus.emit(event)
        steps_result: dict[str, list[DiaryItem]] = find(lambda r: ref_primitive.moduleId in r, result)
        return next(iter(steps_result.get(ref_primitive.moduleId, [])), None)

    def _merge_diaries(self, existing_diary: DiaryItem, new_diary_dict: dict) -> dict:
        old_value = existing_diary.value
        old_details_dict = [detail.to_dict(include_none=False) for detail in existing_diary.details]
        old_chips = existing_diary.diaryChips or []
        merged_value = str(int(old_value[0]) + int(new_diary_dict[DiaryItem.VALUE][0]))
        merged_details = self._merge_details(old_details_dict, new_diary_dict[DiaryItem.DETAILS])
        chips = list({chip: "" for chip in [*old_chips, *(new_diary_dict[DiaryItem.DIARY_CHIPS] or [])]}.keys())
        return {
            **new_diary_dict,
            DiaryItem.ID: existing_diary.id,
            DiaryItem.DIARY_CHIPS: chips,
            DiaryItem.VALUE: [merged_value],
            DiaryItem.DETAILS: merged_details,
            DiaryItem.UPDATE_DATE_TIME: utc_str_field_to_val(datetime.utcnow()),
            DiaryItem.CREATE_DATE_TIME: utc_str_field_to_val(existing_diary.createDateTime),
        }

    @staticmethod
    def _merge_details(old_details: list[dict], new_details: list[dict]) -> list[dict]:
        def get_value(detail_items: list[dict], time_stamp: str) -> int:
            item: dict = find(lambda i: i[DiaryDetail.KEY] == time_stamp, detail_items)
            return int(item[DiaryDetail.VALUE])

        result = []
        new_timestamps: list[str] = [item[DiaryDetail.KEY] for item in new_details]
        for item in old_details:
            if (ts := item[DiaryDetail.KEY]) not in new_timestamps:
                result.append(item)
            else:
                detail_item = {**item, DiaryDetail.VALUE: str(get_value(old_details, ts) + get_value(new_details, ts))}
                result.append(detail_item)
        merged_timestamps: list[str] = [item[DiaryDetail.KEY] for item in result]
        for item in new_details:
            if item[DiaryDetail.KEY] not in merged_timestamps:
                result.append(item)
        return result
