"""model for reaction time object"""

from typing import Optional
from typing import TYPE_CHECKING

from sdk.common.utils.convertible import convertibleclass, meta, required_field
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.module_result_utils import AggregateFunc

if TYPE_CHECKING:
    from sdk.module_result.models import Primitive


@convertibleclass
class ReactionTime(PrimitiveDTO):
    """ReactionTime model"""

    ALLOWED_AGGREGATE_FUNCS = (AggregateFunc.AVG, AggregateFunc.MIN, AggregateFunc.MAX)
    AGGREGATION_FIELDS = ("value",)

    value: float = required_field(metadata=meta(value_to_field=float))  # in second(could fraction)

    def get_estimated_value(self) -> Optional[float]:  # return by millisecond
        return self.value * 1000.0

    @staticmethod
    def get_django_model() -> type["Primitive"] | None:
        from sdk.module_result.modules.health_score.models import ReactionTime

        return ReactionTime
