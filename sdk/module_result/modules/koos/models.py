from django.db import models

from sdk.module_result.models import Primitive
from sdk.module_result.models.primitive import PrimitiveIndexMeta


class WOMAC(Primitive):
    painScore = models.FloatField()
    symptomsScore = models.FloatField()
    adlScore = models.FloatField()
    totalScore = models.FloatField()

    class Meta(PrimitiveIndexMeta):
        app_label = "koos"
        db_table = "womac"


class KOOS(Primitive):
    adlScore = models.FloatField()
    qualityOfLifeScore = models.FloatField()
    painScore = models.FloatField()
    symptomsScore = models.FloatField()
    sportRecreationScore = models.FloatField()

    class Meta(PrimitiveIndexMeta):
        app_label = "koos"
        db_table = "koos"
