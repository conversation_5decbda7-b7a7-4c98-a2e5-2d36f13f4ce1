# Generated by Django 5.1.10 on 2025-07-03 14:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("bvi", "0001_initial"),
    ]

    operations = [
        migrations.AddIndex(
            model_name="bodymeasurement",
            index=models.Index(
                fields=["userId", "moduleId", "moduleConfigId", "-startDateTime"],
                name="body_measur_userId_2e8327_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="bodymeasurement",
            index=models.Index(
                fields=[
                    "userId",
                    "moduleId",
                    "moduleConfigId",
                    "noteId",
                    "-startDateTime",
                ],
                name="body_measur_userId_e317dd_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="bodymeasurement",
            index=models.Index(
                fields=["userId", "moduleId", "moduleConfigId", "createDateTime"],
                name="body_measur_userId_db3064_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="bodymeasurement",
            index=models.Index(
                fields=[
                    "userId",
                    "moduleId",
                    "moduleConfigId",
                    "source",
                    "-startDateTime",
                ],
                name="body_measur_userId_c7c421_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="bodymeasurement",
            index=models.Index(
                fields=["userId", "-startDateTime"],
                name="body_measur_userId_1894fb_idx",
            ),
        ),
    ]
