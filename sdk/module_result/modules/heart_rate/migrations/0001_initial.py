# Generated by Django 5.1.5 on 2025-01-28 13:01
import json

import django.core.validators
from bson import ObjectId
from django.db import migrations, models

from sdk.common.utils.file_utils import move_mongo_collection_to_postgres_table

import sdk.common.adapter.sql.sql_utils

def json_serial(obj):
    """JSON serializer for objects not serializable by default json code"""

    if isinstance(obj, ObjectId):
        return str(obj)
    raise TypeError("Type %s not serializable" % type(obj))


def transformer(old_map: dict):
    if "metadata" in old_map:
        old_map["metadata"] = json.loads(json.dumps(old_map["metadata"], default=json_serial))
    return old_map


class Migration(migrations.Migration):
    @staticmethod
    def migrate_data(apps, schema_editor):
        move_mongo_collection_to_postgres_table(
            apps,
            "heart_rate",
            migrations=[
                {
                    "model_name": "HRV",
                    "mongo_model": "hrv",
                    "transformer": transformer,
                },
                {
                    "model_name": "FitbitHRV",
                    "mongo_model": "fitbithrv",
                    "transformer": transformer,
                    "partial_migrate_filter": ("createDateTime", 14),
                },
                {
                    "model_name": "HeartRate",
                    "mongo_model": "heartrate",
                    "transformer": transformer,
                    "partial_migrate_filter": ("createDateTime", 14),
                },
            ],
        )

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="FitbitHRV",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                ("userId", models.CharField(db_index=True, max_length=24, default=None)),
                ("moduleId", models.CharField(db_index=True, max_length=64, default=None)),
                ("moduleConfigId", models.CharField(db_index=True, max_length=24, default=None)),
                (
                    "moduleResultId",
                    models.CharField(blank=True, db_index=True, max_length=32, null=True, default=None),
                ),
                ("deploymentId", models.CharField(db_index=True, max_length=24, default=None)),
                ("version", models.PositiveIntegerField(default=0)),
                ("deviceName", models.CharField(blank=True, max_length=256, null=True)),
                ("isAggregated", models.BooleanField(default=False)),
                ("startDateTime", models.DateTimeField(blank=True, null=True)),
                ("endDateTime", models.DateTimeField(blank=True, null=True)),
                ("createDateTime", models.DateTimeField(blank=True, null=True)),
                ("updateDateTime", models.DateTimeField(blank=True, null=True)),
                ("submitterId", models.CharField(db_index=True, max_length=24, default=None)),
                (
                    "correlationStartDateTime",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("client", models.JSONField(blank=True, null=True)),
                ("server", models.JSONField(blank=True, null=True)),
                ("ragThreshold", models.JSONField(blank=True, null=True)),
                ("flags", models.JSONField(blank=True, null=True)),
                ("eventLogId", models.CharField(blank=True, max_length=24, null=True, default=None)),
                ("device", models.JSONField(blank=True, null=True)),
                ("source", models.TextField(blank=True, null=True)),
                ("isAverage", models.BooleanField(blank=True, null=True)),
                (
                    "noteId",
                    models.CharField(blank=True, db_index=True, max_length=24, null=True, default=None),
                ),
                ("feedback", models.JSONField(blank=True, null=True)),
                (
                    "meal",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("FASTING", "FASTING"),
                            ("AFTER_BREAKFAST", "AFTER_BREAKFAST"),
                            ("AFTER_BREAKFAST_2_HOURS", "AFTER_BREAKFAST_2_HOURS"),
                            ("BEFORE_LUNCH", "BEFORE_LUNCH"),
                            ("AFTER_LUNCH", "AFTER_LUNCH"),
                            ("AFTER_LUNCH_2_HOURS", "AFTER_LUNCH_2_HOURS"),
                            ("BEFORE_DINNER", "BEFORE_DINNER"),
                            ("AFTER_DINNER", "AFTER_DINNER"),
                            ("AFTER_DINNER_2_HOURS", "AFTER_DINNER_2_HOURS"),
                            ("NONE", "NONE"),
                        ],
                        max_length=32,
                        default=None,
                        null=True,
                    ),
                ),
                ("userNote", models.CharField(blank=True, max_length=1000, null=True, default=None)),
                ("rmssd", models.FloatField(blank=True, null=True)),
                ("coverage", models.FloatField(blank=True, null=True)),
                ("hf", models.FloatField(blank=True, null=True)),
                ("lf", models.FloatField(blank=True, null=True)),
            ],
            options={
                "db_table": "fitbit_hrv",
            },
        ),
        migrations.CreateModel(
            name="HeartRate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                ("userId", models.CharField(db_index=True, max_length=24, default=None)),
                ("moduleId", models.CharField(db_index=True, max_length=64, default=None)),
                ("moduleConfigId", models.CharField(db_index=True, max_length=24, default=None)),
                (
                    "moduleResultId",
                    models.CharField(blank=True, db_index=True, max_length=32, null=True, default=None),
                ),
                ("deploymentId", models.CharField(db_index=True, max_length=24, default=None)),
                ("version", models.PositiveIntegerField(default=0)),
                ("deviceName", models.CharField(blank=True, max_length=256, null=True)),
                ("isAggregated", models.BooleanField(default=False)),
                ("startDateTime", models.DateTimeField(blank=True, null=True)),
                ("endDateTime", models.DateTimeField(blank=True, null=True)),
                ("createDateTime", models.DateTimeField(blank=True, null=True)),
                ("updateDateTime", models.DateTimeField(blank=True, null=True)),
                ("submitterId", models.CharField(db_index=True, max_length=24, default=None)),
                (
                    "correlationStartDateTime",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("client", models.JSONField(blank=True, null=True)),
                ("server", models.JSONField(blank=True, null=True)),
                ("ragThreshold", models.JSONField(blank=True, null=True)),
                ("flags", models.JSONField(blank=True, null=True)),
                ("eventLogId", models.CharField(blank=True, max_length=24, null=True, default=None)),
                ("device", models.JSONField(blank=True, null=True)),
                ("source", models.TextField(blank=True, null=True)),
                ("isAverage", models.BooleanField(blank=True, null=True)),
                (
                    "noteId",
                    models.CharField(blank=True, db_index=True, max_length=24, null=True, default=None),
                ),
                ("feedback", models.JSONField(blank=True, null=True)),
                (
                    "meal",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("FASTING", "FASTING"),
                            ("AFTER_BREAKFAST", "AFTER_BREAKFAST"),
                            ("AFTER_BREAKFAST_2_HOURS", "AFTER_BREAKFAST_2_HOURS"),
                            ("BEFORE_LUNCH", "BEFORE_LUNCH"),
                            ("AFTER_LUNCH", "AFTER_LUNCH"),
                            ("AFTER_LUNCH_2_HOURS", "AFTER_LUNCH_2_HOURS"),
                            ("BEFORE_DINNER", "BEFORE_DINNER"),
                            ("AFTER_DINNER", "AFTER_DINNER"),
                            ("AFTER_DINNER_2_HOURS", "AFTER_DINNER_2_HOURS"),
                            ("NONE", "NONE"),
                        ],
                        max_length=32,
                        default=None,
                        null=True,
                    ),
                ),
                ("userNote", models.CharField(blank=True, max_length=1000, null=True, default=None)),
                (
                    "value",
                    models.PositiveSmallIntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(250),
                        ]
                    ),
                ),
                (
                    "heartRateType",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("UNSPECIFIED", "UNSPECIFIED"),
                            ("RESTING", "RESTING"),
                            ("HIGH_FREQ", "HIGH_FREQ"),
                        ],
                        max_length=16,
                        null=True,
                    ),
                ),
                ("classification", models.CharField(max_length=1000, blank=True, null=True)),
                (
                    "variabilityAVNN",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "variabilitySDNN",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "variabilityRMSSD",
                    models.IntegerField(blank=True, null=True),
                ),
                ("variabilityPNN50", models.FloatField(blank=True, null=True)),
                ("variabilityprcLF", models.FloatField(blank=True, null=True)),
                ("confidence", models.PositiveSmallIntegerField(blank=True, null=True)),
                ("goodIBI", models.PositiveSmallIntegerField(blank=True, null=True)),
                ("rawDataObject", models.JSONField(blank=True, null=True)),
                (
                    "valueUnit",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("oC", "oC"),
                            ("bpm", "bpm"),
                            ("mmHg", "mmHg"),
                            ("rpm", "rpm"),
                            ("%", "%"),
                            ("mmol/L", "mmol/L"),
                            ("kg", "kg"),
                            ("cm", "cm"),
                            ("kg/m2", "kg/m2"),
                            ("L/min", "L/min"),
                        ],
                        default="bpm",
                        max_length=32,
                        null=True,
                    ),
                ),
                ("metadata", models.JSONField(
                    blank=True,
                    encoder=sdk.common.adapter.sql.sql_utils.BSONEncoder,
                    null=True,
                )),
            ],
            options={
                "db_table": "heart_rate",
            },
        ),
        migrations.CreateModel(
            name="HRV",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                ("userId", models.CharField(db_index=True, max_length=24, default=None)),
                ("moduleId", models.CharField(db_index=True, max_length=64, default=None)),
                ("moduleConfigId", models.CharField(db_index=True, max_length=24, default=None)),
                (
                    "moduleResultId",
                    models.CharField(blank=True, db_index=True, max_length=32, null=True, default=None),
                ),
                ("deploymentId", models.CharField(db_index=True, max_length=24, default=None)),
                ("version", models.PositiveIntegerField(default=0)),
                ("deviceName", models.CharField(blank=True, max_length=256, null=True)),
                ("isAggregated", models.BooleanField(default=False)),
                ("startDateTime", models.DateTimeField(blank=True, null=True)),
                ("updateDateTime", models.DateTimeField(blank=True, null=True)),
                ("endDateTime", models.DateTimeField(blank=True, null=True)),
                ("createDateTime", models.DateTimeField(blank=True, null=True)),
                ("submitterId", models.CharField(db_index=True, max_length=24, default=None)),
                (
                    "correlationStartDateTime",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("client", models.JSONField(blank=True, null=True)),
                ("server", models.JSONField(blank=True, null=True)),
                ("ragThreshold", models.JSONField(blank=True, null=True)),
                ("flags", models.JSONField(blank=True, null=True)),
                ("eventLogId", models.CharField(blank=True, max_length=24, null=True, default=None)),
                ("device", models.JSONField(blank=True, null=True)),
                ("source", models.TextField(blank=True, null=True)),
                ("isAverage", models.BooleanField(blank=True, null=True)),
                (
                    "noteId",
                    models.CharField(blank=True, db_index=True, max_length=24, null=True, default=None),
                ),
                ("feedback", models.JSONField(blank=True, null=True)),
                (
                    "meal",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("FASTING", "FASTING"),
                            ("AFTER_BREAKFAST", "AFTER_BREAKFAST"),
                            ("AFTER_BREAKFAST_2_HOURS", "AFTER_BREAKFAST_2_HOURS"),
                            ("BEFORE_LUNCH", "BEFORE_LUNCH"),
                            ("AFTER_LUNCH", "AFTER_LUNCH"),
                            ("AFTER_LUNCH_2_HOURS", "AFTER_LUNCH_2_HOURS"),
                            ("BEFORE_DINNER", "BEFORE_DINNER"),
                            ("AFTER_DINNER", "AFTER_DINNER"),
                            ("AFTER_DINNER_2_HOURS", "AFTER_DINNER_2_HOURS"),
                            ("NONE", "NONE"),
                        ],
                        max_length=32,
                        null=True,
                        default=None,
                    ),
                ),
                ("userNote", models.CharField(blank=True, max_length=1000, null=True, default=None)),
                (
                    "value",
                    models.FloatField(
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(300),
                        ]
                    ),
                ),
                (
                    "medianValue",
                    models.FloatField(
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(300),
                        ],
                        blank=True,
                        null=True,
                    ),
                ),
                (
                    "minValue",
                    models.PositiveSmallIntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(300),
                        ],
                        blank=True,
                        null=True,
                    ),
                ),
                (
                    "maxValue",
                    models.PositiveSmallIntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(300),
                        ],
                        blank=True,
                        null=True,
                    ),
                ),
                ("rawDataObject", models.JSONField(blank=True, null=True)),
            ],
            options={
                "db_table": "hrv",
            },
        ),
        migrations.RunPython(migrate_data, lambda *args, **kwargs: 0, atomic=True),
    ]
