import random
from typing import TYPE_CHECKING

from sdk.common.utils.convertible import (
    convertibleclass,
    required_field,
    default_field,
    meta,
)
from sdk.module_result.dtos.module_config import ModuleConfig
from sdk.module_result.dtos.primitives import PrimitiveDTO

if TYPE_CHECKING:
    from sdk.module_result.models import Primitive


class ComplexSymptomConfigBodyKeys:
    NAME = "name"
    SEVERITY = "severity"
    VALUE = "value"
    SCALE = "scale"
    ENABLED = "enabled"
    SCALE_UPDATE_DT = "scaleUpdateDateTime"
    COMPLEX_SYMPTOMS = "complexSymptoms"


@convertibleclass
class ComplexSymptomValue:
    """ComplexSymptom model"""

    NAME = "name"
    SEVERITY = "severity"
    VALUE = "value"
    FOLLOW_UP_QUESTION_IDS = "followUpQuestionIds"

    name: str = required_field()
    severity: int = default_field()
    value: str = default_field()
    followUpQuestionIds: list[str] = default_field()


@convertibleclass
class SymptomDTO(PrimitiveDTO):
    COMPLEX_VALUES = "complexValues"
    FOLLOW_UP_QUESTION_IDS = "followUpQuestionIds"

    # value field is for backward compatibility only
    value: list[str] = default_field(metadata=meta(dump_only=True))
    complexValues: list[ComplexSymptomValue] = default_field()
    followUpQuestionIds: list[str] = default_field()

    @classmethod
    def create_dummy_value(cls):
        return None

    @classmethod
    def create_dummy_data(cls, module_config: ModuleConfig, *args, **kwargs) -> dict:
        result = super().create_dummy_data(module_config=module_config, *args, **kwargs)
        complex_values = []
        for symptom in module_config.configBody[ComplexSymptomConfigBodyKeys.COMPLEX_SYMPTOMS]:
            complex_values.append(
                {
                    ComplexSymptomValue.NAME: symptom[ComplexSymptomValue.NAME],
                    ComplexSymptomValue.SEVERITY: random.choice(symptom[ComplexSymptomConfigBodyKeys.SCALE])[
                        ComplexSymptomValue.SEVERITY
                    ],
                }
            )
        return {**result, cls.COMPLEX_VALUES: complex_values}

    @staticmethod
    def get_django_model() -> type["Primitive"] | None:
        from sdk.module_result.modules.symptom.models import Symptom

        return Symptom
