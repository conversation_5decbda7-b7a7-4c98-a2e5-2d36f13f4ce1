{% if weekly_data %}
  <div>
    <div class="general general-container">
      <div class="charts-container general-container">
        <h3 class="title container-align">{{ title }}</h3>
        <div class="generic-data font-size-12">
          <div class="column generic-column-padding">
            {% for field in fields %}
              <div class="generic-row">
                <div class="generic-titles">{{ field.title }}</div>
                <div class="generic-values">{{ field.value }}</div>
              </div>
            {% endfor %}
          </div>
        </div>
        <div class="for-charts page-direction" id="SRHCharts">
          <div class="display-none" id="SRH6monthChart"></div>
          <div class="display-none" id="SRHWeekChart"></div>
        </div>
      </div>
      <div class="generic-footer">
        <div class="for-bottom-markers container-align">
          <div class="bottom-markers monthly-mean-value">
            <div class="dark-dot"></div>
            <div class="line"></div>
            <div class="dark-dot"></div>
          </div>
          <div>{{ txt_monthly_mean_value }}</div>
        </div>
        <div class="for-bottom-markers containers-align">
          <div class="bottom-markers">
            <div class="dark-dot title-dot-margin"></div>
          </div>
          <div>{{ txt_individual_data_point }}</div>
        </div>
        <div class="for-bottom-markers containers-align">
          <div class="bottom-markers standard-deviation">
            <div class="standard-deviation-line"></div>
            <div class="standard-deviation-line"></div>
            <div class="standard-deviation-line"></div>
          </div>
          <div>{{ txt_sample_standard_deviation }}</div>
        </div>
      </div>
    </div>
    <script>
      am5.addLicense("AM5C332366403");

      let monthlySRH = {{weekly_data}};
      let lastWeekDataSRH = {{last_week_data}};
      let monthNamesSRH = {{month_names}};
      let axisValueMapping = {{value_to_name}};
      var min = 0.9;
      var max = 5.1;


      function convertDateToFloatNumber(dateString, day) {
        let date = moment.utc(dateString);
        let hour = date.hour();
        let minutes = date.minutes();
        let seconds = date.seconds();

        let secondsPerDay = 24 * 60 * 60;

        let hourToSeconds = hour * 60 * 60;
        let minutesToSeconds = minutes * 60;

        let calculatedSeconds = hourToSeconds + minutesToSeconds + seconds;

        let fractional = calculatedSeconds / secondsPerDay;

        return Number(day) + Number(fractional);
      }

      var firstDateDay = moment.utc(lastWeekDataSRH.data[0].date).format("DD");
      var maxDay = Math.max(...lastWeekDataSRH.data.map((o) => Number(moment.utc(o.date).format("DD"))));
      var isNegative = false;
      var lastDate = moment.utc(
        lastWeekDataSRH.data[lastWeekDataSRH.data.length - 1].date
      );
      var endOfMonth = lastDate.clone().endOf("month").format("DD");
      var lastDateDay = lastDate.format("DD");
      var negativeValueCounter = -(maxDay - Number(firstDateDay)) - 1;
      var previousElementDate;
      lastWeekDataSRH.data.forEach((element) => {
        const currentElementDay = moment.utc(element.date).format("DD");
        if (Number(firstDateDay) >= 22 && Number(currentElementDay) >= 22) {
          isNegative = true;
          if (currentElementDay !== previousElementDate) {
            negativeValueCounter++;
          }
          element.date = convertDateToFloatNumber(element.date, negativeValueCounter);
          previousElementDate = currentElementDay;
        } else {
          element.date = convertDateToFloatNumber(element.date, currentElementDay);
        }
      });

      function createDateRange(text, yAxis, root) {
        let position = am5.p0;
        let direction = 'ltr';
        const body = document.getElementById('main_body');
        if (body.dir === "rtl") {
          position = am5.p100;
          direction = 'rtl';
        }
        yAxis.axisHeader.children.push(
          am5.Label.new(root, {
            text: text,
            fontSize: 10,
            paddingBottom: 4,
            paddingLeft: 0,
            x: position,
            direction: direction,
            fill: am5.color('#6A6D72'),
          })
        );

        yAxis.axisHeader.get('background').setAll({
          fill: am5.color('#FBFBFB')
        });
      }

      function createRange(value, endValue, color, yAxis) {
        let rangeDataItem = yAxis.makeDataItem({
          value: value || -10000,
          endValue: endValue || 10000,
        });

        yAxis.createAxisRange(rangeDataItem);
        rangeDataItem.get('axisFill').setAll({
          fill: color,
          fillOpacity: 0.35,
          visible: true,
        });
      }

      function createDeviationSeries(chart, root, name, xAxis, yAxis, valueYField, categoryXField) {
        return chart.series.push(
          am5xy.ColumnSeries.new(root, {
            name: name,
            stacked: true,
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: valueYField,
            categoryXField: categoryXField,
          })
        );
      }

      function createSelectedDatesSeries(
        chart,
        root,
        name,
        xAxis,
        yAxis,
        valueYField,
        categoryXField,
        selectedDates
      ) {
        var bulletsSeries = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: name,
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: valueYField,
            categoryXField: categoryXField,
            stroke: am5.color('#FBFBFB'),
          })
        );

        bulletsSeries.bullets.push(function(root, series, dataItem) {
          if (selectedDates.find(elem => elem === dataItem.dataContext.month)) {
            return am5.Bullet.new(root, {
              sprite: am5.Label.new(root, {
                text: '|',
                fontWeight: 200,
                centerX: am5.percent(50),
                centerY: am5.percent(1),
                dy: 110,
                populateText: true,
                fill: am5.color('#424347'),
              }),
            });
          }
        });
        return bulletsSeries;
      }

      function createGenericChart(chartId, genericModuleData, rag, dateRange) {
        let root = am5.Root.new(chartId);

        let chart = root.container.children.push(am5xy.XYChart.new(root, {}));
        chart.zoomOutButton.set("forceHidden", true);

        let yRenderer = am5xy.AxisRendererY.new(root, {
          inversed: true,
          minGridDistance: 1,
          strokeOpacity: 1,
          strokeWidth: 1,
          stroke: am5.color('#424347'),
        });

        yRenderer.grid.template.set('visible', false);

        yRenderer.labels.template.setAll({
          paddingRight: 10,
          paddingLeft: -5,
          fontSize: 10,
          fill: am5.color('#2F3033'),
        });

        let yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            max,
            min,
            maxPrecision: 0,
            strictMinMax: true,
            renderer: yRenderer,
          })
        );

        yRenderer.labels.template.adapters.add("text", function(text) {
          return axisValueMapping[text];
        });

        yRenderer.labels.template.adapters.add("visible", (_, target) => {
          // show only 3 labels of y-Axis: first, middle and last
          let labels = yRenderer.labels.values
            .filter((x) => !!x.get("text"))
            .slice();
          let middle = Math.round((labels.length - 1) / 2);
          let allowedIndexes = [0, middle, labels.length - 1];
          return allowedIndexes.includes(labels.indexOf(target));
        });

        createDateRange(dateRange, yAxis, root);

        let xRenderer = am5xy.AxisRendererX.new(root, {
          minGridDistance: 10,
          strokeOpacity: 1,
          strokeWidth: 1,
          stroke: am5.color('#424347'),
        });

        xRenderer.grid.template.setAll({
          location: 0.5,
          color: am5.color('#EBEBEB'),
          strokeOpacity: 0.05,
        });

        const body = document.getElementById('main_body');
        if (body.dir === "rtl") {
          xRenderer.labels.template.setAll({
            paddingTop: rtlLabelPadding,
            fontSize: 9,
            fill: am5.color("#2F3033"),
          });
        } else {
          xRenderer.labels.template.setAll({
            paddingTop: 10,
            fontSize: 9,
            fill: am5.color('#2F3033'),
          });
        }

        let xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: 'month',
            renderer: xRenderer,
          })
        );

        let selectedDates = [];
        let monthLabel;
        xRenderer.labels.template.adapters.add('text', (text, target) => {
          if (target.dataItem && target.dataItem.dataContext) {
            let currentMonthLabel = monthNamesSRH[moment
              .utc(target.dataItem.dataContext.month)
              .format("MMMM")];
            let firstDate = moment.utc(genericModuleData[0].month);
            let firstDayFormat = Number(firstDate.format("DD"));
            let firstMonthFormat = monthNamesSRH[firstDate.format("MMMM")];
            if (monthLabel !== currentMonthLabel) {
              if (
                firstDayFormat > 14 &&
                currentMonthLabel === firstMonthFormat
              ) return;

              if ((firstDayFormat < 8 &&
                  currentMonthLabel === firstMonthFormat) ||
                currentMonthLabel !== firstMonthFormat
              ) {
                selectedDates.push(target.dataItem.dataContext.month);
              }
              monthLabel = currentMonthLabel;
              return currentMonthLabel;
            }
          }
        });

        xAxis.data.setAll(genericModuleData);

        let series1 = createDeviationSeries(
          chart,
          root,
          'DeviationStart',
          xAxis,
          yAxis,
          'deviationStart',
          'month'
        );

        series1.columns.template.setAll({
          strokeWidth: 0,
          strokeOpacity: 0,
          fillOpacity: 0,
          width: am5.percent(40),
        });

        series1.data.setAll(genericModuleData);

        let series2 = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: 'MiddleMonthValue',
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: 'value',
            categoryXField: 'month',
            stroke: am5.color('#424347'),
          })
        );

        series2.strokes.template.set('strokeWidth', 0.5);

        series2.bullets.push(function() {
          return am5.Bullet.new(root, {
            sprite: am5.Circle.new(root, {
              strokeWidth: 0,
              fill: am5.color('#424347'),
              stroke: series2.get('stroke'),
              radius: 2,
            }),
          });
        });

        series2.data.setAll(genericModuleData);

        let series3 = createDeviationSeries(
          chart,
          root,
          'DeviationEnd',
          xAxis,
          yAxis,
          'deviationEnd',
          'month'
        );

        series3.columns.template.setAll({
          fillOpacity: 0.3,
          cornerRadiusBL: 20,
          cornerRadiusBR: 20,
          cornerRadiusTL: 20,
          cornerRadiusTR: 20,
          fill: am5.color('#909499'),
          strokeOpacity: 0,
          width: am5.percent(40),
        });

        series3.data.setAll(genericModuleData);

        let series4 = createSelectedDatesSeries(
          chart,
          root,
          'MinValue',
          xAxis,
          yAxis,
          'minValue',
          'month',
          selectedDates
        );

        let timeout;
        let hasRendered = false;
        root.events.on("frameended", () => {
          if (timeout) clearTimeout(timeout);

          if (!hasRendered) {
            timeout = setTimeout(function() {
              let chartMinValue = yAxis.getPrivate("min");
              genericModuleData.forEach(elem => elem.minValue = chartMinValue);
              series4.data.setAll(genericModuleData);
              hasRendered = true;
              loadedPages++; // this variable is a global counter
            }, 100)
          }
        });

        rag.forEach(({
          color,
          thresholdRange
        }) => {
          thresholdRange.forEach(range => {
            createRange(range.minValue, range.maxValue, am5.color(color), yAxis);
          });
        });
      }

      function createGenericWeekChart(chartId, data, deviationData, rag, dateRange) {
        let root = am5.Root.new(chartId);

        let chart = root.container.children.push(am5xy.XYChart.new(root, {}));
        chart.zoomOutButton.set("forceHidden", true);

        let yRenderer = am5xy.AxisRendererY.new(root, {
          inversed: true,
          minGridDistance: 1,
          strokeOpacity: 1,
          strokeWidth: 1,
          opposite: true,
          stroke: am5.color('#EBEBEB'),
        });

        yRenderer.grid.template.set('visible', false);

        let yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            max,
            min,
            maxPrecision: 0,
            strictMinMax: true,
            renderer: yRenderer,
          })
        );

        yRenderer.labels.template.set("visible", false);

        createDateRange(dateRange, yAxis, root);

        let xRenderer = am5xy.AxisRendererX.new(root, {
          strokeOpacity: 1,
          strokeWidth: 1,
          minGridDistance: 30,
          stroke: am5.color('#424347'),
        });

        xRenderer.grid.template.set('visible', false);

        let xAxis1;
        if (!isNegative && lastDateDay !== endOfMonth) {
          xAxis1 = chart.xAxes.push(
            am5xy.ValueAxis.new(root, {
              max: maxDay + 1,
              min: firstDateDay,
              renderer: xRenderer,
            })
          );
        } else {
          xAxis1 = chart.xAxes.push(
            am5xy.ValueAxis.new(root, {
              renderer: xRenderer,
            })
          );
        }

        xRenderer.labels.template.set("visible", false);

        let series1 = chart.series.push(
          am5xy.SmoothedXLineSeries.new(root, {
            name: 'MiddleDayValue',
            xAxis: xAxis1,
            yAxis: yAxis,
            valueYField: 'middleDayValue',
            valueXField: 'date',
            stroke: am5.color('#2F3033'),
          })
        );

        series1.strokes.template.set('strokeWidth', 0.5);

        series1.data.setAll(data);

        let series2 = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: 'Value',
            calculateAggregates: true,
            xAxis: xAxis1,
            yAxis: yAxis,
            valueYField: 'value',
            valueXField: 'date',
            valueField: 2.5,
          })
        );

        let circleTemplate = am5.Template.new({});
        series2.bullets.push(() => {
          let graphics = am5.Circle.new(
            root, {
              fill: am5.color('#424347'),
              radius: 2.5,
            },
            circleTemplate
          );
          return am5.Bullet.new(root, {
            sprite: graphics,
          });
        });

        series2.set('heatRules', [{
          target: circleTemplate,
          dataField: 'middleDayValue',
          key: 'radius',
        }, ]);

        series2.strokes.template.set('strokeOpacity', 0);
        series2.data.setAll(data);

        let xAxisDeviation = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: 'day',
            renderer: am5xy.AxisRendererX.new(root, {
              minGridDistance: 10,
            }),
          })
        );
        let xRendererDeviation = xAxisDeviation.get('renderer');
        xRendererDeviation.labels.template.setAll({
          location: 0,
          paddingTop: 4,
          fontSize: 9,
          fill: am5.color('#2F3033'),
        });

        xRendererDeviation.grid.template.setAll({
          color: am5.color('#EBEBEB'),
          strokeOpacity: 0.05,
        });

        xAxisDeviation.data.setAll(deviationData);

        let dayLabel;
        xRendererDeviation.labels.template.set('location', 0.5);
        xRendererDeviation.labels.template.adapters.add('text', (_, target) => {
          if (target.dataItem && target.dataItem.dataContext) {
            let currentDayLabel = moment.utc(target.dataItem.dataContext.day)
              .format('DD')
            if (dayLabel !== currentDayLabel) {
              dayLabel = currentDayLabel;
              return currentDayLabel;
            }
          }
        });

        let series3 = createDeviationSeries(
          chart,
          root,
          'DeviationStart',
          xAxisDeviation,
          yAxis,
          'deviationStart',
          'day'
        );

        series3.columns.template.setAll({
          strokeWidth: 0,
          strokeOpacity: 0,
          fillOpacity: 0,
        });

        series3.data.setAll(deviationData);

        let series4 = createDeviationSeries(
          chart,
          root,
          'DeviationEnd',
          xAxisDeviation,
          yAxis,
          'deviationEnd',
          'day'
        );

        series4.columns.template.setAll({
          fillOpacity: 0.3,
          cornerRadiusBL: 6,
          cornerRadiusBR: 6,
          cornerRadiusTL: 6,
          cornerRadiusTR: 6,
          fill: am5.color('#909499'),
          stroke: am5.color('#909499'),
          strokeOpacity: 0,
        });

        series4.data.setAll(deviationData);

        rag.forEach(({
          color,
          thresholdRange
        }) => {
          thresholdRange.forEach(range => {
            createRange(range.minValue, range.maxValue, am5.color(color), yAxis);
          });
        });
      }

      function createChartContainer(chartId, containerId, containerClass) {
        let chartDiv = document.createElement('div');
        chartDiv.id = chartId;
        chartDiv.className = 'chartDiv';
        chartDiv.style.marginTop = '5px';

        let container = document.getElementById(containerId);
        container.className = containerClass;
        container.appendChild(chartDiv);

        let cont = document.getElementById('SRHCharts');
        cont.appendChild(container);
      }

      function displayUnit(id) {
        let unit = document.getElementById(id);
        unit.style.display = 'contents';
      }

      function initCharts() {
        if (monthlySRH?.data?.length > 0) {
          createChartContainer('6MonthChart', 'SRH6monthChart', 'generic-6month-chart');

          monthlySRH.data.forEach(elem => {
            elem.minValue = min;
          });
          createGenericChart(
            '6MonthChart',
            monthlySRH.data,
            monthlySRH.ragThresholds,
            monthlySRH.dateRange
          );
        }

        if (lastWeekDataSRH?.data?.length > 0) {
          createChartContainer('weekChart', 'SRHWeekChart', 'generic-week-chart');

          createGenericWeekChart(
            'weekChart',
            lastWeekDataSRH.data,
            lastWeekDataSRH.deviationData,
            lastWeekDataSRH.ragThresholds,
            lastWeekDataSRH.dateRange
          );
        }
      }

      initCharts();
      setIndentation('.generic-column-padding', '70px', 'padding', 'right', 'left');
      setIndentation('.generic-row', '70px', 'padding', 'right', 'left');
      setIndentation('.for-bottom-markers', '20px', 'margin', 'right', 'left');
      setIndentation('.bottom-markers', '8px', 'margin', 'right', 'left');
    </script>
  </div>
{% endif %}
