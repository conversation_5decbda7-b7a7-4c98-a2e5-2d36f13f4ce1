from typing import TYPE_CHECKING

from sdk import convertibleclass
from sdk.common.utils.fields import required_int_field
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.module_result_utils import AggregateFunc
from sdk.module_result.modules.questionnaire import QuestionnaireAnswer

if TYPE_CHECKING:
    from sdk.module_result.models import Primitive


@convertibleclass
class NDIDTO(PrimitiveDTO):
    """Neck Disability Index model"""

    VALUE = "value"
    AGGREGATION_FIELDS = (VALUE,)
    ALLOWED_AGGREGATE_FUNCS = (AggregateFunc.AVG,)

    DISPLAY_FIELDS = (VALUE,)

    value: int = required_int_field(0, 50)

    _answers = []

    @property
    def answers(self):
        return self._answers

    @answers.setter
    def answers(self, answers: list[QuestionnaireAnswer]):
        self._answers = answers

    @staticmethod
    def get_django_model() -> type["Primitive"] | None:
        from sdk.module_result.modules.ndi.models import NDI

        return NDI
