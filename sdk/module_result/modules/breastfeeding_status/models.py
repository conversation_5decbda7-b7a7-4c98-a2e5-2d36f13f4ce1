from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models

from sdk.module_result.common.enums import YesNoDont
from sdk.module_result.models import Primitive, SkippedFieldsMixin
from sdk.module_result.models.primitive import PrimitiveIndexMeta


class BreastFeedingStatus(Primitive, SkippedFieldsMixin):
    isBreastFeedingCurrently = models.BooleanField()
    numberOfWeeksAtChildBirth = models.IntegerField(blank=True, null=True, default=None)
    hadComplicationsAtBirth = models.BooleanField(blank=True, null=True, default=None)
    complicationList = models.JSONField(default=None, blank=True, null=True)
    hasHighRiskConditions = models.BooleanField(default=None, blank=True, null=True)
    highRiskConditions = models.JSONField(default=None, blank=True, null=True)
    familyHistoryOfDefects = models.IntegerField(
        default=None, choices=[(option.value, option.name) for option in YesNoDont], blank=True, null=True
    )
    familyHistoryOfDefectsList = models.JSONField(default=None, blank=True, null=True)
    numBreastFeedingBabies = models.IntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(3)])
    breastFeedingBabyDetails = models.JSONField(default=None, blank=True, null=True)
    metadata = models.JSONField()

    class Meta(PrimitiveIndexMeta):
        app_label = "breastfeeding_status"
        db_table = "breast_feeding_status"
