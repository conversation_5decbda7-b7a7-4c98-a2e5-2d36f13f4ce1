{"calculatedQuestionnaire": false, "filledBy": "User", "id": "cc880b26-f948-4539-b8a8-d792fe6df0f9", "isForManager": false, "isHorizontal": false, "isOnboarding": false, "name": "Alex - Questionnaire Engine 2", "pages": [{"description": "Description", "id": "9b68ecf6-815e-474a-ba2e-d258cebb69f6", "media": ["641ab71a32567fb20c358833"], "order": 1, "text": "Alex <PERSON>fo page - With image 1", "type": "INFO"}, {"items": [{"description": "Test descriptipn", "format": "NUMERIC", "id": "6fa4cbc0-4958-43a8-a2b0-49c3561b3d7d", "logic": {"isEnabled": false, "rules": []}, "lowerBound": -2, "maxDecimals": 3, "multipleAnswers": {"enabled": true, "maxAnswers": 3}, "normalRange": {"lowerBound": 2, "upperBound": 6}, "order": 2, "required": false, "text": "Test number question", "type": "NUMERIC", "units": ["custom_uni"], "upperBound": 10}], "order": 2, "type": "QUESTION"}, {"items": [{"description": "", "format": "AUTOCOMPLETE_TEXT", "id": "4bc32acd-e56d-472a-9baf-b4a9f14aced1", "order": 3, "required": false, "text": "Test autocomplete", "logic": {"isEnabled": false, "rules": []}, "type": "AUTOCOMPLETE_TEXT", "multipleAnswers": {"enabled": false, "maxAnswers": 0}, "autocomplete": {"options": [{"key": "Test1", "value": "Test1"}, {"key": "test2", "value": "test2"}], "allowFreeText": true}}], "order": 3, "type": "QUESTION"}], "scoreAvailable": false, "submissionPage": {"buttonText": "Test submit button", "description": "", "id": "353b2a60-f3a4-4384-af9a-bc2ae315afff", "order": 4, "text": "Submission page", "type": "SUBMISSION"}}