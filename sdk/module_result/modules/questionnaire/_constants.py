from enum import Enum


class KnownQuestionnaireTypes:
    MULTI_SCORE_QUESTIONNAIRE_TYPE = "MULTI_SCORE"
    EQ5D_5L = "EQ5D_5L"
    PAM = "PAM"
    OBSERVATION_NOTES = ("OBSERVATION_NOTES",)
    PROMIS_TYPES = [
        "PROMIS_PAIN",
        "PROMIS_PAIN_DE",
        "PROMIS_PAIN_ES",
        "PROMIS_PAIN_FR",
        "PROMIS_PAIN_NL",
        "PROMIS_PHYSICAL",
        "PROMIS_PHYSICAL_DE",
        "PROMIS_PHYSICAL_ES",
        "PROMIS_PHYSICAL_FR",
        "PROMIS_PHYSICAL_NL",
    ]


class QuestionnaireConstants:
    QUESTIONNAIRE_TYPE = "questionnaireType"
    DIMENSION_FLAG = "dimensions"
    DIMENSION_ID = "id"
    QUESTIONNAIRE_KIND = "questionnaireKind"

    NO_DEFAULT_DISCLAIMER = [
        KnownQuestionnaireTypes.EQ5D_5L,
        KnownQuestionnaireTypes.PAM,
        KnownQuestionnaireTypes.OBSERVATION_NOTES,
    ] + KnownQuestionnaireTypes.PROMIS_TYPES


class QuestionnaireKind(Enum):
    CMS = "CMS"
    INTERNAL = "INTERNAL"


class QuestionnairePageType(Enum):
    QUESTION = "QUESTION"
    INFO = "INFO"


class QuestionnaireAnswerMediaType(Enum):
    PHOTO = "PHOTO"
    FILE = "FILE"
    VIDEO = "VIDEO"


class QuestionAnswerSelectionCriteria(Enum):
    MULTIPLE = "MULTIPLE"
    FIELD = "FIELD"
    SINGLE = "SINGLE"


class QuestionnaireAppResultValueType(Enum):
    VALUE_FLOAT = "VALUE_FLOAT"
    STD_ERR_FLOAT = "STD_ERR_FLOAT"


class QuestionnaireAppResultType(Enum):
    T_DISTRIBUTION = "T_DISTRIBUTION"
    GRADED_RESULT = "GRADED_RESULT"


class QuestionnaireStatus(Enum):
    SUBMITTED = "SUBMITTED"
    DRAFT = "DRAFT"
    SIGNED = "SIGNED"


class QuestionAnswerFormat(Enum):
    NUMERIC = "NUMERIC"
    NUMERIC_UNIT = "NUMERIC_UNIT"
    TEXTCHOICE = "TEXTCHOICE"
    BOOLEAN = "BOOLEAN"
    DATE = "DATE"
    TEXT = "TEXT"
    DURATION = "DURATION"
    DURATION_SECONDS = "DURATION_SECONDS"
    SCALE = "SCALE"
    COMPOSITE = "COMPOSITE"
    LIST = "LIST"
    MEDIA = "MEDIA"
    AUTOCOMPLETE_TEXT = "AUTOCOMPLETE_TEXT"

    @classmethod
    def _missing_(cls, value):
        """This method is to support backward compatibility for TEXT_CHOICE"""
        if value == "TEXT_CHOICE":
            return QuestionAnswerFormat.TEXTCHOICE
