from sdk.module_result.modules.module import Module
from sdk.module_result.modules.temperature._diary_generator import TemperatureDiaryGenerator
from sdk.module_result.modules.temperature._primitive import TemperatureDTO
from sdk.module_result.modules.temperature._visualizer import TemperatureHTMLVisualizer


class TemperatureModule(Module):
    moduleId = "Temperature"
    primitives = [TemperatureDTO]
    inputPrimitives = (TemperatureDTO,)
    preferredUnitEnabled = True
    ragEnabled = True
    visualizers = [TemperatureHTMLVisualizer]
    diaryEnabled = True
    diaryGenerator = TemperatureDiaryGenerator
