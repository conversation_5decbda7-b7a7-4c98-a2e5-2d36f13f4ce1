from django.core.validators import MinValueValidator, MaxValueValidator
from django.db import models

from sdk.module_result.dtos.primitives import HumaMeasureUnit
from sdk.module_result.models import PrimitivesWithPreferableUnits
from sdk.module_result.models.primitive import PrimitiveIndexMeta


class Temperature(PrimitivesWithPreferableUnits):
    value = models.FloatField(validators=[MinValueValidator(33), MaxValueValidator(42)])
    valueUnit = models.CharField(
        max_length=32,
        blank=True,
        null=True,
        default=HumaMeasureUnit.TEMPERATURE.value,
        choices=HumaMeasureUnit.as_choices(),
    )

    class Meta(PrimitiveIndexMeta):
        app_label = "temperature"
        db_table = "temperature"
