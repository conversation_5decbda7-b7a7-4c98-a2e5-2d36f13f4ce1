from sdk.module_result.modules.breathlessness._diary_generator import BreathlessnessDiaryGenerator
from sdk.module_result.modules.breathlessness._visualizere import (
    BreathlessnessVisualizer,
)
from sdk.module_result.modules.questionnaire import QuestionnaireD<PERSON>, QuestionnaireAnswer, QuestionnaireModule


class BreathlessnessModule(QuestionnaireModule):
    moduleId = "Breathlessness"
    primitives = [QuestionnaireDTO]
    ragEnabled = True
    visualizers = [BreathlessnessVisualizer]
    diaryEnabled = True
    diaryGenerator = BreathlessnessDiaryGenerator
    reminderEnabled = True

    def calculate(self, primitive: QuestionnaireDTO):
        if not primitive.answers:
            return

        score = self._get_answer_weight({}, primitive.answers[0])
        if self._is_score_correct(score):
            primitive.value = score

    @staticmethod
    def _get_answer_weight(question: dict, answer: QuestionnaireAnswer):
        answer_value = answer.answerText
        if answer.selectedChoices:
            answer_value = answer.selectedChoices[0]
        try:
            return int(answer_value)
        except ValueError:
            pass

    @staticmethod
    def _is_score_correct(value):
        return 1 <= value <= 5
