{"$id": "com.medopad.platformplay.fjs-questionnaire.json", "$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "definitions": {"uniqueId": {"type": "string", "examples": ["fjs_hip_hiking", "fjs_knee_hiking"]}, "datetime": {"type": "string", "format": "date-time", "examples": ["2008-09-15T15:53:00+05:00"]}, "s3Object": {"type": "object", "properties": {"key": {"type": "string"}, "region": {"type": "string"}, "bucket": {"type": "string"}}, "required": ["key", "region", "bucket"]}, "condition": {"type": "object", "properties": {"questionId": {"$ref": "#/definitions/uniqueId"}, "eq": {"type": ["string", "number"]}, "neq": {"type": ["string", "number"]}}}, "rule": {"type": "object", "properties": {"allOf": {"type": "array", "items": {"$ref": "#/definitions/condition"}}, "anyOf": {"type": "array", "items": {"$ref": "#/definitions/condition"}}, "jumpToId": {"$ref": "#/definitions/uniqueId"}}, "required": ["jumpToId"]}, "logic": {"type": "object", "properties": {"rules": {"type": "array", "items": {"$ref": "#/definitions/rule"}}, "isEnabled": {"type": "boolean"}}, "required": ["rules", "isEnabled"]}, "pageType": {"enum": ["INFO", "QUESTION", "SUBMISSION"]}, "page": {"type": "object", "properties": {"id": {"$ref": "#/definitions/uniqueId"}, "order": {"type": "number"}, "text": {"type": "string", "minLength": 1, "maxLength": 256}, "description": {"type": "string", "minLength": 0, "maxLength": 1024}}, "required": ["type", "order"]}, "infoPage": {"allOf": [{"$ref": "#/definitions/page"}, {"type": "object", "properties": {"type": {"const": "INFO"}, "image": {"$ref": "#/definitions/s3Object"}}, "required": ["type"]}]}, "submissionPage": {"allOf": [{"$ref": "#/definitions/page"}, {"type": "object", "properties": {"type": {"const": "SUBMISSION"}, "buttonText": {"type": "string", "minLength": 1, "maxLength": 70}}, "required": ["type"]}]}, "questionPage": {"allOf": [{"$ref": "#/definitions/page"}, {"type": "object", "properties": {"type": {"const": "QUESTION"}, "items": {"type": "array", "items": {"anyOf": [{"$ref": "#/definitions/infoItem"}, {"$ref": "#/definitions/textQuestionItem"}, {"$ref": "#/definitions/textChoiceQuestionItem"}, {"$ref": "#/definitions/scaleQuestionItem"}, {"$ref": "#/definitions/booleanQuestionItem"}, {"$ref": "#/definitions/dateQuestionItem"}, {"$ref": "#/definitions/numberQuestionItem"}, {"$ref": "#/definitions/durationSecondsQuestionItem"}, {"$ref": "#/definitions/autocompleteQuestionItem"}]}}}, "required": ["items"]}]}, "pageItemFormat": {"enum": ["TEXT", "TEXTCHOICE", "SCALE", "BOOLEAN", "DATE", "NUMERIC", "DURATION_SECONDS"]}, "pageItemSelectionCriteria": {"enum": ["SINGLE", "MULTIPLE"]}, "stringOption": {"type": "object", "properties": {"label": {"type": "string"}, "value": {"type": "string"}, "weight": {"type": "integer"}}, "required": ["label", "value"]}, "stringOrIntegerOption": {"type": "object", "properties": {"label": {"type": "string"}, "value": {"oneOf": [{"type": "string"}, {"type": "integer"}]}, "weight": {"type": "integer"}}, "required": ["label", "value"]}, "pageItem": {"type": "object", "properties": {"id": {"$ref": "#/definitions/uniqueId"}, "format": {"$ref": "#/definitions/pageItemFormat"}, "order": {"type": "number"}, "text": {"type": "string", "minLength": 1, "maxLength": 256}, "description": {"type": "string", "minLength": 0, "maxLength": 1024}}, "required": ["order", "text", "format"]}, "autocompletePageItem": {"type": "object", "properties": {"id": {"$ref": "#/definitions/uniqueId"}, "format": {"const": "AUTOCOMPLETE_TEXT"}, "order": {"type": "number"}, "text": {"type": "string", "minLength": 1, "maxLength": 256}, "description": {"type": "string", "minLength": 0, "maxLength": 1024}, "required": {"type": "boolean"}, "logic": {"$ref": "#/definitions/logic"}}, "required": ["order", "text", "format", "required"]}, "infoItem": {"allOf": [{"$ref": "#/definitions/pageItem"}, {"type": "object", "properties": {"image": {"$ref": "#/definitions/s3Object"}}}]}, "questionItem": {"allOf": [{"$ref": "#/definitions/pageItem"}, {"type": "object", "properties": {"required": {"type": "boolean"}, "logic": {"$ref": "#/definitions/logic"}}, "required": ["required"]}]}, "textQuestionItem": {"allOf": [{"$ref": "#/definitions/questionItem"}, {"type": "object", "properties": {"regex": {"type": "string", "minLength": 1, "maxLength": 1024}, "defaultValue": {"type": "string"}, "placeholder": {"type": "string", "minLength": 1, "maxLength": 1024}}}]}, "textChoiceQuestionItem": {"allOf": [{"$ref": "#/definitions/questionItem"}, {"type": "object", "properties": {"options": {"type": "array", "items": {"$ref": "#/definitions/stringOption"}}, "selectionCriteria": {"$ref": "#/definitions/pageItemSelectionCriteria"}, "defaultOptions": {"type": "array", "items": {"type": "string"}}}, "required": ["options", "selectionCriteria"]}]}, "scaleQuestionItem": {"allOf": [{"$ref": "#/definitions/questionItem"}, {"type": "object", "properties": {"options": {"type": "array", "items": {"$ref": "#/definitions/stringOrIntegerOption"}}, "lowerBound": {"type": "integer"}, "upperBound": {"type": "integer"}, "defaultValue": {"oneOf": [{"type": "string"}, {"type": "integer"}]}, "isVisualAnalogueScale": {"type": "boolean"}}}]}, "booleanQuestionItem": {"allOf": [{"$ref": "#/definitions/questionItem"}, {"type": "object", "properties": {"defaultValue": {"type": "boolean"}}}]}, "dateQuestionItem": {"allOf": [{"$ref": "#/definitions/questionItem"}, {"type": "object", "properties": {"includeTime": {"type": "boolean"}}}]}, "numberQuestionItem": {"allOf": [{"$ref": "#/definitions/questionItem"}, {"type": "object", "properties": {"unit": {"type": "string"}, "lowerBound": {"type": "integer"}, "upperBound": {"type": "integer"}, "allowsIntegersOnly": {"type": "boolean"}}}]}, "durationSecondsQuestionItem": {"allOf": [{"$ref": "#/definitions/questionItem"}, {"type": "object", "properties": {"increment": {"type": "integer", "minimum": 1}, "lowerBound": {"type": "integer"}, "upperBound": {"type": "integer"}}}]}, "autocompleteQuestionItem": {"allOf": [{"$ref": "#/definitions/autocompletePageItem"}, {"type": "object", "properties": {"autocomplete": {"type": "object", "properties": {"placeholder": {"type": "string"}, "listEndpointId": {"type": "string"}, "allowFreeText": {"type": "boolean"}}, "required": ["listEndpointId"]}}, "required": ["autocomplete"]}]}}, "properties": {"pages": {"type": "array", "items": {"anyOf": [{"$ref": "#/definitions/infoPage"}, {"$ref": "#/definitions/questionPage"}]}}, "region": {"enum": ["Denmark", "France", "Germany", "Japan", "Netherlands", "Spain", "Thailand", "UK", "US", "Zimbabwe"]}, "isForManager": {"type": "boolean"}, "isHorizonalFlow": {"type": "boolean"}, "trademarkText": {"type": "string"}, "publisherName": {"type": "string"}, "submissionPage": {"type": "object", "$ref": "#/definitions/submissionPage"}, "isPAM": {"type": "boolean"}, "isOnboarding": {"type": "boolean"}}, "required": ["isForManager", "pages"]}