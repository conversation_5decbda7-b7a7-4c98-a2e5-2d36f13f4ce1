# Generated by Django 5.1.5 on 2025-02-18 11:57

import django.core.validators
from django.db import migrations, models

import sdk.common.adapter.sql.sql_utils
from sdk.common.utils.file_utils import move_mongo_collection_to_postgres_table


def migrate_data(apps, schema_editor):
    move_mongo_collection_to_postgres_table(
        apps,
        "sleep",
        migrations=[
            {
                "model_name": "Sleep",
                "mongo_model": "sleep",
            },
        ],
    )


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Sleep",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                (
                    "userId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "moduleId",
                    models.<PERSON>r<PERSON><PERSON>(db_index=True, default=None, max_length=64),
                ),
                (
                    "moduleConfigId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "moduleResultId",
                    models.Char<PERSON>ield(
                        blank=True,
                        db_index=True,
                        default=None,
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "deploymentId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                ("version", models.PositiveIntegerField(default=0)),
                ("deviceName", models.CharField(blank=True, max_length=256, null=True)),
                ("isAggregated", models.BooleanField(default=False)),
                ("startDateTime", models.DateTimeField(blank=True, null=True)),
                ("endDateTime", models.DateTimeField(blank=True, null=True)),
                ("createDateTime", models.DateTimeField(blank=True, null=True)),
                ("updateDateTime", models.DateTimeField(blank=True, null=True)),
                (
                    "submitterId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "correlationStartDateTime",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("client", models.JSONField(blank=True, null=True)),
                ("server", models.JSONField(blank=True, null=True)),
                ("ragThreshold", models.JSONField(blank=True, null=True)),
                ("flags", models.JSONField(blank=True, null=True)),
                (
                    "eventLogId",
                    models.CharField(blank=True, default=None, max_length=24, null=True),
                ),
                ("device", models.JSONField(blank=True, null=True)),
                ("source", models.TextField(blank=True, null=True)),
                ("isAverage", models.BooleanField(blank=True, null=True)),
                (
                    "noteId",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default=None,
                        max_length=24,
                        null=True,
                    ),
                ),
                (
                    "meal",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("FASTING", "FASTING"),
                            ("AFTER_BREAKFAST", "AFTER_BREAKFAST"),
                            ("AFTER_BREAKFAST_2_HOURS", "AFTER_BREAKFAST_2_HOURS"),
                            ("BEFORE_LUNCH", "BEFORE_LUNCH"),
                            ("AFTER_LUNCH", "AFTER_LUNCH"),
                            ("AFTER_LUNCH_2_HOURS", "AFTER_LUNCH_2_HOURS"),
                            ("BEFORE_DINNER", "BEFORE_DINNER"),
                            ("AFTER_DINNER", "AFTER_DINNER"),
                            ("AFTER_DINNER_2_HOURS", "AFTER_DINNER_2_HOURS"),
                            ("NONE", "NONE"),
                        ],
                        default=None,
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "userNote",
                    models.CharField(blank=True, default=None, max_length=1000, null=True),
                ),
                (
                    "awake",
                    models.PositiveIntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        validators=[django.core.validators.MaxValueValidator(86400)],
                    ),
                ),
                (
                    "awakeCount",
                    models.PositiveIntegerField(blank=True, default=None, null=True),
                ),
                (
                    "sleep",
                    models.PositiveIntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        validators=[django.core.validators.MaxValueValidator(86400)],
                    ),
                ),
                (
                    "outOfBed",
                    models.PositiveIntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        validators=[django.core.validators.MaxValueValidator(86400)],
                    ),
                ),
                (
                    "lightSleep",
                    models.PositiveIntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        validators=[django.core.validators.MaxValueValidator(86400)],
                    ),
                ),
                (
                    "deepSleep",
                    models.PositiveIntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        validators=[django.core.validators.MaxValueValidator(86400)],
                    ),
                ),
                (
                    "remSleep",
                    models.PositiveIntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        validators=[django.core.validators.MaxValueValidator(86400)],
                    ),
                ),
                (
                    "duration",
                    models.PositiveIntegerField(validators=[django.core.validators.MaxValueValidator(86400)]),
                ),
                ("summary", models.JSONField(blank=True, default=None, null=True)),
                ("dateOfSleep", models.DateField()),
                (
                    "uwake",
                    models.PositiveIntegerField(validators=[django.core.validators.MaxValueValidator(86400)]),
                ),
                (
                    "feedback",
                    models.JSONField(
                        blank=True,
                        default=None,
                        encoder=sdk.common.adapter.sql.sql_utils.BSONEncoder,
                        null=True,
                    ),
                ),
                (
                    "score",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        default=None,
                        null=True,
                        validators=[django.core.validators.MaxValueValidator(100)],
                    ),
                ),
            ],
            options={
                "db_table": "sleep",
            },
        ),
        migrations.RunPython(migrate_data, lambda *args, **kwargs: 0, atomic=True),
    ]
