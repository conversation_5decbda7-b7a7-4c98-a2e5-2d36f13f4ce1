from typing import Optional

from sdk.authorization.dtos.user import UserDTO
from sdk.module_result.dtos.primitives import (
    PrimitiveDTO,
)
from sdk.module_result.module_result_utils import (
    check_answered_required_questions,
    config_body_to_question_map,
)
from sdk.module_result.modules.lysholm_and_tegner._diary_generator import LysholmTegnerDiaryGenerator
from sdk.module_result.modules.lysholm_and_tegner._visualizer import (
    LysholmHTMLVisualizer,
    TegnerHTMLVisualizer,
)
from sdk.module_result.modules.lysholm_and_tegner.primitives import LysholmDTO, TegnerDTO
from sdk.module_result.modules.questionnaire import (
    QuestionnaireDTO,
    QuestionnaireAnswer,
    QuestionnaireModule,
)


class LysholmTegnerModule(QuestionnaireModule):
    moduleId = "LysholmTegner"
    primitives = [QuestionnaireDTO, LysholmDTO, TegnerDTO]
    ragEnabled = True
    visualizers = [LysholmHTMLVisualizer, TegnerHTMLVisualizer]
    diaryEnabled = True
    diaryGenerator = LysholmTegnerDiaryGenerator
    reminderEnabled = True

    def _create_tegner_primitive(
        self,
        questionnaire: QuestionnaireDTO,
        question_map: dict,
        answer_map: dict[str, QuestionnaireAnswer],
    ) -> TegnerDTO:
        tegner_question_ids = {
            TegnerDTO.ACTIVITY_LEVEL_BEFORE: "tegner_activity_level_before",
            TegnerDTO.ACTIVITY_LEVEL_CURRENT: "tegner_activity_level_current",
        }
        answer_weights = {}
        for field, question_id in tegner_question_ids.items():
            question = question_map.get(question_id)
            answer = answer_map.get(question_id)
            answer_weights[field] = self._get_answer_weight(question, answer)

        primitive_dict = {**questionnaire.to_dict(include_none=False), **answer_weights}

        return TegnerDTO.from_dict(primitive_dict)

    def _create_lysholm_primitive(
        self,
        questionnaire: QuestionnaireDTO,
        question_map: dict,
        answer_map: dict[str, QuestionnaireAnswer],
    ) -> LysholmDTO:
        lysholm_question_ids = {
            LysholmDTO.LIMP: "lysholm_limp",
            LysholmDTO.CANE_OR_CRUTCHES: "lysholm_cane_or_crutches",
            LysholmDTO.LOCKING_SENSATION: "lysholm_locking_sensation",
            LysholmDTO.GIVING_WAY_SENSATION: "lysholm_givingway_sensation",
            LysholmDTO.PAIN: "lysholm_pain",
            LysholmDTO.SWELLING: "lysholm_swelling",
            LysholmDTO.CLIMBING_STAIRS: "lysholm_climbing_stairs",
            LysholmDTO.SQUATTING: "lysholm_squatting",
        }
        answer_weights = {}
        for field, question_id in lysholm_question_ids.items():
            question = question_map.get(question_id)
            answer = answer_map.get(question_id)
            answer_weights[field] = self._get_answer_weight(question, answer)

        primitive_dict = {
            **questionnaire.to_dict(include_none=False),
            **answer_weights,
            LysholmDTO.LYSHOLM: sum(answer_weights.values()),
        }

        return LysholmDTO.from_dict(primitive_dict)

    def preprocess(self, primitives: list[PrimitiveDTO], user: Optional[UserDTO]):
        primitive = self.validate_and_get_questionnaire(primitives)

        check_answered_required_questions(primitive, self.config.configBody)

        question_map = config_body_to_question_map(self.config.configBody)
        answer_map = {answer.questionId: answer for answer in primitive.answers}

        primitives.append(self._create_tegner_primitive(primitive, question_map, answer_map))
        primitives.append(self._create_lysholm_primitive(primitive, question_map, answer_map))

        return super(LysholmTegnerModule, self).preprocess(primitives, user)

    def calculate(self, primitive: QuestionnaireDTO):
        if not isinstance(primitive, QuestionnaireDTO):
            return

        calculator = self._get_calculator()
        if calculator:
            calculator.calculate(primitive, self.config)
