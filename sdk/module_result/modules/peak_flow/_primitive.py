"""Model for Peak Flow object"""

from dataclasses import field
from typing import TYPE_CHECKING

from sdk.authorization.dtos.user import UserDTO
from sdk.common.utils.common_functions_utils import round_half_up
from sdk.common.utils.convertible import (
    convertibleclass,
    meta,
    required_field,
    default_field,
)
from sdk.common.utils.validators import validate_range
from sdk.module_result.dtos.primitives import HumaMeasureUnit, MeasureUnit
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.module_result_utils import AggregateFunc

if TYPE_CHECKING:
    from sdk.module_result.models import Primitive


@convertibleclass
class PeakFlowDTO(PrimitiveDTO):
    hasUnits = True

    VALUE = "value"
    VALUE_PERCENT = "valuePercent"

    ALLOWED_AGGREGATE_FUNCS = (AggregateFunc.AVG, AggregateFunc.MIN, AggregateFunc.MAX)
    AGGREGATION_FIELDS = (VALUE,)

    value: int = required_field(
        metadata=meta(validate_range(100, 800), value_to_field=int),
    )
    valuePercent: float = default_field(metadata=meta(value_to_field=lambda v: round_half_up(v, 1)))
    valueUnit: str = field(
        default=HumaMeasureUnit.PEAK_FLOW.value,
        metadata=meta(HumaMeasureUnit),
    )

    def get_value_by_unit(self, unit: MeasureUnit):
        if not unit or unit.value == self.valueUnit:
            return self.value

        if unit is MeasureUnit.PERCENTAGE:
            return self.valuePercent

    def calculate_percent(self, gender, age, height) -> float:
        if gender == UserDTO.Gender.FEMALE:
            value = ((height * 3.72 / 100 + 2.24) - age * 0.03) * 60
        else:
            value = ((height * 5.48 / 100 + 1.58) - age * 0.041) * 60

        return round_half_up(self.value / value * 100, 1)

    @property
    def get_primitive_value(self) -> float:
        return self.valuePercent

    @staticmethod
    def get_django_model() -> type["Primitive"] | None:
        from sdk.module_result.modules.peak_flow.models import PeakFlow

        return PeakFlow
