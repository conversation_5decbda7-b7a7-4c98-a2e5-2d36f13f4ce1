from dataclasses import field
from typing import TYPE_CHECKING

from sdk.common.utils.convertible import convertibleclass, default_field, meta
from sdk.common.utils.fields import required_float_field
from sdk.common.utils.validators import validate_range
from sdk.module_result.dtos.primitives import HumaMeasureUnit
from sdk.module_result.dtos.primitives import PrimitiveDTO
from sdk.module_result.module_result_utils import AggregateFunc
from ._constants import OxygenSupplement

if TYPE_CHECKING:
    from sdk.module_result.models import Primitive


@convertibleclass
class OxygenSaturationDTO(PrimitiveDTO):
    hasUnits = True

    VALUE = "value"
    VALUE_UNIT = "valueUnit"
    ORIGINAL_VALUE = "originalValue"
    OXYGEN_SUPPLEMENT = "oxygenSupplement"

    AGGREGATION_FIELDS = (VALUE,)
    ALLOWED_AGGREGATE_FUNCS = (AggregateFunc.MIN_MAX_AVG,)

    value: float = required_float_field(min_=0.7, max_=1, decimal_places=2)
    valueUnit: str = field(
        default=HumaMeasureUnit.OXYGEN_SATURATION.value,
        metadata=meta(HumaMeasureUnit),
    )
    originalValue: float = default_field(
        metadata=meta(validate_range(0, 100), value_to_field=float),
    )
    oxygenSupplement: OxygenSupplement = default_field()

    @property
    def get_primitive_value(self) -> int:
        return int(self.value * 100)

    @staticmethod
    def get_django_model() -> type["Primitive"] | None:
        from sdk.module_result.modules.oxygen_saturation.models import OxygenSaturation

        return OxygenSaturation
