# Generated by Django 5.1.5 on 2025-02-18 15:33

import django.contrib.postgres.fields
from django.db import migrations, models

import sdk.common.adapter.sql.sql_utils
from sdk.common.utils.file_utils import move_mongo_collection_to_postgres_table



class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="VaccinationDetails",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                (
                    "userId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "moduleId",
                    models.CharField(db_index=True, default=None, max_length=64),
                ),
                (
                    "moduleConfigId",
                    models.Char<PERSON>ield(db_index=True, default=None, max_length=24),
                ),
                (
                    "moduleResultId",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default=None,
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "deploymentId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                ("version", models.PositiveIntegerField(default=0)),
                ("deviceName", models.CharField(blank=True, max_length=256, null=True)),
                ("isAggregated", models.BooleanField(default=False)),
                ("startDateTime", models.DateTimeField(blank=True, null=True)),
                ("endDateTime", models.DateTimeField(blank=True, null=True)),
                ("createDateTime", models.DateTimeField(blank=True, null=True)),
                ("updateDateTime", models.DateTimeField(blank=True, null=True)),
                (
                    "submitterId",
                    models.CharField(db_index=True, default=None, max_length=24),
                ),
                (
                    "correlationStartDateTime",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("client", models.JSONField(blank=True, null=True)),
                ("server", models.JSONField(blank=True, null=True)),
                ("ragThreshold", models.JSONField(blank=True, null=True)),
                ("flags", models.JSONField(blank=True, null=True)),
                (
                    "eventLogId",
                    models.CharField(blank=True, default=None, max_length=24, null=True),
                ),
                ("device", models.JSONField(blank=True, null=True)),
                ("source", models.TextField(blank=True, null=True)),
                ("isAverage", models.BooleanField(blank=True, null=True)),
                (
                    "noteId",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        default=None,
                        max_length=24,
                        null=True,
                    ),
                ),
                ("feedback", models.JSONField(blank=True, null=True)),
                (
                    "meal",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("FASTING", "FASTING"),
                            ("AFTER_BREAKFAST", "AFTER_BREAKFAST"),
                            ("AFTER_BREAKFAST_2_HOURS", "AFTER_BREAKFAST_2_HOURS"),
                            ("BEFORE_LUNCH", "BEFORE_LUNCH"),
                            ("AFTER_LUNCH", "AFTER_LUNCH"),
                            ("AFTER_LUNCH_2_HOURS", "AFTER_LUNCH_2_HOURS"),
                            ("BEFORE_DINNER", "BEFORE_DINNER"),
                            ("AFTER_DINNER", "AFTER_DINNER"),
                            ("AFTER_DINNER_2_HOURS", "AFTER_DINNER_2_HOURS"),
                            ("NONE", "NONE"),
                        ],
                        default=None,
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "userNote",
                    models.CharField(blank=True, default=None, max_length=1000, null=True),
                ),
                (
                    "vaccinatedPlace",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, "HEALTHCARE_PROVIDER"),
                            (1, "DOCTOR_OFFICE_CLINIC"),
                            (2, "HOSPITAL"),
                            (3, "PHARMACY_DRUGSTORE"),
                            (4, "WORK"),
                            (5, "HEALTH_CLINIC_VETERANRS_FACILITY"),
                            (6, "SCHOOL_STUDENT_CLINIC"),
                            (7, "MILITARY_BASE"),
                            (8, "MOBILE_VACC_UNIT"),
                            (9, "OTHER_VACC_CENTER"),
                            (10, "AIRPORT"),
                            (11, "OTHER"),
                        ]
                    ),
                ),
                (
                    "vaccinationLocation",
                    models.CharField(blank=True, default=None, max_length=1000, null=True),
                ),
                ("vaccinationCity", models.CharField(max_length=1000)),
                ("batchNumber", models.CharField(max_length=255)),
                (
                    "isBatchNumberValid",
                    models.BooleanField(blank=True, default=None, null=True),
                ),
                (
                    "isSecondDoseVacc",
                    models.PositiveSmallIntegerField(choices=[(0, "YES"), (1, "NO"), (2, "DONT_KNOW")]),
                ),
                (
                    "secondVacScheduleDate",
                    models.DateField(blank=True, default=None, null=True),
                ),
                ("isSeasonFluVac", models.BooleanField()),
                (
                    "seasonFluVacDate",
                    models.DateField(blank=True, default=None, null=True),
                ),
                ("isOtherSpecifiedVacc", models.BooleanField()),
                (
                    "otherSpecifiedVacc",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.PositiveSmallIntegerField(
                            choices=[
                                (0, "DTPA"),
                                (1, "HEPATITIS_A_or_B"),
                                (2, "HPV"),
                                (3, "MENINGITIS"),
                                (4, "PNEUMOCOCCUS"),
                                (5, "SINGLES"),
                                (6, "OTHER"),
                            ]
                        ),
                        blank=True,
                        default=None,
                        null=True,
                        size=None,
                    ),
                ),
                (
                    "otherVaccCategory",
                    models.JSONField(blank=True, default=None, null=True),
                ),
                (
                    "isAllergicReactionVacc",
                    models.PositiveSmallIntegerField(choices=[(0, "YES"), (1, "NO"), (2, "DONT_KNOW")]),
                ),
                (
                    "allergicReactionVacc",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(),
                        blank=True,
                        default=None,
                        null=True,
                        size=None,
                    ),
                ),
                (
                    "allergicReaction",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(),
                        blank=True,
                        default=None,
                        null=True,
                        size=None,
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        encoder=sdk.common.adapter.sql.sql_utils.BSONEncoder,
                        null=True,
                    ),
                ),
            ],
            options={
                "db_table": "vaccination_detail",
            },
        ),
    ]
