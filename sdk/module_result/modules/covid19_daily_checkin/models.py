from django.db import models

from sdk.module_result.models import Primitive
from sdk.module_result.modules.covid19_daily_checkin._constants import ContactTypeWithCovid19Person
from sdk.module_result.models.primitive import PrimitiveIndexMeta


class Covid19DailyCheckIn(Primitive):
    contactType = models.CharField(
        max_length=50,
        choices=[(tag.value, tag.value) for tag in ContactTypeWithCovid19Person],
    )

    class Meta(PrimitiveIndexMeta):
        app_label = "covid19_daily_checkin"
        db_table = "covid19_daily_checkin"
