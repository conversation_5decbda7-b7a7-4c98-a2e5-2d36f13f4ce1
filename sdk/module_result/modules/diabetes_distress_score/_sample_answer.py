from copy import deepcopy

from sdk.module_result.modules.questionnaire import QuestionnaireDTO


def _sample_dds_questionnaire_answer():
    return deepcopy(
        {
            QuestionnaireDTO.ANSWERS: [
                {
                    "answerText": "Not a problem",
                    "questionId": "screening_question_1",
                    "question": "S1",
                    "format": "TEXTCHOICE",
                    "choices": ["1", "2", "3", "4", "5", "6"],
                    "selectedChoices": ["1"],
                    "selectionCriteria": "SINGLE",
                    "options": [
                        {
                            "exclusive": False,
                            "label": "Not a problem",
                            "value": "1",
                        },
                        {
                            "exclusive": False,
                            "label": "A slight problem",
                            "value": "2",
                        },
                        {
                            "exclusive": False,
                            "label": "A moderate problem",
                            "value": "3",
                        },
                        {
                            "exclusive": False,
                            "label": "Somewhat serious problem",
                            "value": "4",
                        },
                        {
                            "exclusive": False,
                            "label": "A serious problem",
                            "value": "5",
                        },
                        {
                            "exclusive": False,
                            "label": "A very serious problem",
                            "value": "6",
                        },
                    ],
                },
                {
                    "answerText": "A slight problem",
                    "questionId": "screening_question_2_flow1",
                    "question": "S2",
                    "format": "TEXTCHOICE",
                    "choices": ["1", "2", "3", "4", "5", "6"],
                    "selectedChoices": ["2"],
                    "selectionCriteria": "SINGLE",
                    "options": [
                        {
                            "exclusive": False,
                            "label": "Not a problem",
                            "value": "1",
                        },
                        {
                            "exclusive": False,
                            "label": "A slight problem",
                            "value": "2",
                        },
                        {
                            "exclusive": False,
                            "label": "A moderate problem",
                            "value": "3",
                        },
                        {
                            "exclusive": False,
                            "label": "Somewhat serious problem",
                            "value": "4",
                        },
                        {
                            "exclusive": False,
                            "label": "A serious problem",
                            "value": "5",
                        },
                        {
                            "exclusive": False,
                            "label": "A very serious problem",
                            "value": "6",
                        },
                    ],
                },
            ]
        }
    )
