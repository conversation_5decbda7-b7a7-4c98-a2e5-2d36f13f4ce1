from django.db import models

from sdk.module_result.models import Primitive
from sdk.module_result.modules.oxford_knee_score import LegAffected
from sdk.module_result.models.primitive import PrimitiveIndexMeta


class OxfordKneeScore(Primitive):
    legAffected = models.PositiveSmallIntegerField(
        choices=[(aff.value, aff.name) for aff in LegAffected],
    )
    legsData = models.JSONField()
    rightKneeScore = models.IntegerField(null=True, blank=True, default=None)
    leftKneeScore = models.IntegerField(null=True, blank=True, default=None)

    class Meta(PrimitiveIndexMeta):
        app_label = "oxford_knee_score"
        db_table = "oxford_knee_score"
