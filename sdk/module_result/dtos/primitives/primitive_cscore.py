"""Model for C-Score object"""

from sdk.common.utils.convertible import convertibleclass, default_field, meta
from .primitive_dto import PrimitiveDTO


@convertibleclass
class CScore(PrimitiveDTO):
    """CScore model."""

    VALUE = "value"

    # Value is not required as it will be calculated in the backend, rather than be submitted by apps.
    value: int = default_field(metadata=meta(value_to_field=int))
