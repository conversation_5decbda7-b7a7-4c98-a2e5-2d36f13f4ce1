import logging

from sdk.authorization.dtos.role.default_permissions import PolicyType
from sdk.authorization.dtos.role.role import RoleDTO
from sdk.common.exceptions import PermissionDenied
from sdk.common.utils import inject
from sdk.deployment.dtos.deployment import DeploymentDTO
from sdk.deployment.dtos.status import Status
from sdk.deployment.service.deployment_service import DeploymentService
from sdk.organization.repository.organization_repository import OrganizationRepository
from sdk.security.utils import CheckParams, CheckResult
from ._base import BaseAccess

APP_ID_KEY = "deploymentId"
APP_VIEW_ARG_KEY = "deployment_id"
APP_HEADER_KEY = "x-deployment-id"
FALLBACK_KEY = "target_deployment_id"


class _DeploymentAccess(BaseAccess):
    ADD_LIBRARY = PolicyType.ADD_FILE_LIBRARY
    CREATE = PolicyType.CREATE_DEPLOYMENT
    CREATE_TEST_USER = PolicyType.CREATE_TEST_USER
    DRAFT = PolicyType.DRAFT_DEPLOYMENT
    EDIT = PolicyType.EDIT_DEPLOYMENT
    PUBLISH = PolicyType.PUBLISH_DEPLOYMENT
    EDIT_ROLES = PolicyType.EDIT_CUSTOM_ROLES
    MANAGE_LABELS = PolicyType.EDIT_PATIENT_LABELS
    SUSPEND = PolicyType.OFF_BOARD_PATIENT
    VIEW = PolicyType.VIEW_OWN_DEPLOYMENT
    VIEW_LIBRARY = PolicyType.READ_FILE_LIBRARY
    VIEW_STAFF = PolicyType.VIEW_STAFF_LIST

    def check(self, params: CheckParams) -> CheckResult:
        app_ids = self._find_app_ids(params)
        permission = self.value
        if not app_ids:
            for role, _ in params.roles:
                if role.has([permission]):
                    return CheckResult(True)
            return CheckResult(False)

        if len(app_ids) > 1:
            return CheckResult(False, "Access of different resources")

        app_id = app_ids.pop()
        self._validate_app_status(app_id, params.roles)
        requested_resource = f"deployment/{app_id}"
        for role, res in params.roles:
            is_resource_match = res == requested_resource or res == "deployment/*"
            if is_resource_match and role.has([permission]):
                return CheckResult(True)

        org_id = self._find_org_id(app_id)
        if not org_id:
            return CheckResult(False)

        requested_resource = f"organization/{org_id}"
        for role, resource in params.roles:
            if resource == requested_resource and role.has([permission]):
                return CheckResult(True)

        return CheckResult(False)

    def _find_app_ids(self, params: CheckParams) -> set[str]:
        view_args = params.view_args or {}
        resources = (
            params.headers.get(APP_HEADER_KEY),
            view_args.get(APP_VIEW_ARG_KEY),
            view_args.get(FALLBACK_KEY),
            params.query_args.get(APP_ID_KEY),
            self._get_app_id_from_body(params.body),
        )
        return set(filter(None, resources))

    @staticmethod
    def _find_org_id(app_id: str) -> str | None:
        if not (repo := inject.instance(OrganizationRepository, safe=True)):
            return None

        organization = repo.retrieve_organization_by_deployment_id(deployment_id=app_id)
        if organization:
            return organization.id

    @staticmethod
    def _get_app_id_from_body(body: dict | list) -> str | None:
        if isinstance(body, list):
            body = body[0]

        return body.get(APP_ID_KEY)

    def _validate_app_status(self, app_id: str, roles: list[tuple[RoleDTO, str]]):
        if self.value is not PolicyType.EDIT_DEPLOYMENT:
            return

        for role, _ in roles:
            if role.has([PolicyType.FORCE_UPDATE_DEPLOYMENT]):
                return

        app = DeploymentService().retrieve_deployment_with_fields(app_id, [DeploymentDTO.STATUS])
        if app.status != Status.DRAFT:
            raise PermissionDenied("Deployment is not in draft mode", log_level=logging.INFO)
