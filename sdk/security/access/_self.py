from flask import g

from sdk.authorization.dtos.role.default_permissions import PolicyType
from sdk.authorization.dtos.role.role import RoleDTO
from sdk.authorization.dtos.user import RoleAssignmentDTO, UserDTO
from sdk.authorization.services.authorization import AuthorizationService
from sdk.organization.service import OrganizationService
from sdk.security.utils import Check<PERSON>arams, CheckResult, RolesParam
from ._base import BaseAccess

USER_VIEW_ARG_KEY = "user_id"
USER_ID_KEY = "userId"
MANAGER_VIEW_ARG_KEY = "manager_id"
MANAGER_ID_KEY = "managerId"
DEPLOYMENT_KEY = "deploymentId"


class _SelfAccess(BaseAccess):
    CALL_MANAGER = PolicyType.CALL_MANAGER
    EDIT_DATA = PolicyType.EDIT_OWN_DATA
    EDIT_EVENTS = PolicyType.EDIT_OWN_EVENTS
    EDIT_PROFILE = PolicyType.EDIT_OWN_PROFILE
    VIEW_DATA = PolicyType.VIEW_OWN_DATA
    VIEW_EVENTS = PolicyType.VIEW_OWN_EVENTS
    VIEW_PROFILE = PolicyType.VIEW_OWN_PROFILE
    VIEW_RESOURCES = PolicyType.VIEW_OWN_RESOURCES
    SIGN_CONSENT = PolicyType.SIGN_CONSENT

    def check(self, params: CheckParams) -> CheckResult:
        user_ids = self._find_ids(params)
        if not user_ids:
            return CheckResult(False, "No user reference found.")

        if len(user_ids) > 1:
            return CheckResult(False, "Multiple user references found.")

        manager_ids = self._find_ids(params, MANAGER_VIEW_ARG_KEY, MANAGER_ID_KEY)
        if manager_ids and not self._manager_resource_check(params, manager_ids).is_positive:
            return CheckResult(False, "User is not allowed to access this manager")

        deployment_id = self._find_deployment_id(params)
        if deployment_id and not any(resource == f"deployment/{deployment_id}" for _, resource in params.roles):
            return CheckResult(False, "User is not allowed to access this deployment")

        user_id = user_ids.pop()
        if self._is_proxy_for(user_id, params.roles):
            return CheckResult(True)

        if g.path_user and self._is_proxied_by(g.user.id, proxy=g.path_user):
            return CheckResult(True)

        has_permission = any(role.has([self.value]) for role, _ in params.roles)
        if g.user.id == user_id and has_permission:
            return CheckResult(True)

        return CheckResult(False)

    def _is_proxied_by(self, user_id: str, proxy: UserDTO) -> bool:
        """Check if given roles represent a user proxied by user_id."""
        roles: RolesParam = CheckParams.get_roles(proxy.roles)
        roles = list(filter(lambda r: r[0], roles))
        return self._is_proxy_for(user_id, roles)

    def _is_proxy_for(self, user_id: str, roles: RolesParam) -> bool:
        """Check if given roles represent a proxy for the given user_id."""
        return any(resource == f"user/{user_id}" and role.has([self.value]) for role, resource in roles)

    def _find_ids(self, params: CheckParams, arg_key: str = USER_VIEW_ARG_KEY, id_key: str = USER_ID_KEY) -> set[str]:
        resources = (
            (params.view_args or {}).get(arg_key),
            params.query_args.get(id_key),
            self._get_user_id_from_body(params.body, key=id_key),
        )
        return set(filter(None, resources))

    @staticmethod
    def _get_user_id_from_body(body: dict | list, key: str = USER_ID_KEY) -> str | None:
        if not body:
            return None

        if isinstance(body, list):
            body = body[0] if isinstance(body[0], dict) else {}

        return body.get(key)

    def _manager_resource_check(self, params: CheckParams, manager_ids: set) -> CheckResult:
        if len(manager_ids) > 1:
            return CheckResult(False)

        service = AuthorizationService()
        manager = service.retrieve_simple_user_profile(user_id=manager_ids.pop())
        manager_roles = list(filter(lambda x: x.isActive and x.userType == RoleDTO.UserType.MANAGER, manager.roles))
        if not manager_roles:
            return CheckResult(False)

        if self._has_deployment_access(params, manager_roles) or self._has_organization_access(params, manager_roles):
            return CheckResult(True)

        return CheckResult(False)

    def _has_deployment_access(self, params: CheckParams, manager_roles: list[RoleAssignmentDTO]) -> bool:
        return any(self._match(params.roles, role.resource, self.value) for role in manager_roles)

    def _has_organization_access(self, params: CheckParams, manager_roles: list[RoleAssignmentDTO]) -> bool:
        permission = self.value
        resources = [role.resource_id() for role in manager_roles]
        service = OrganizationService()
        organizations = service.retrieve_organizations_by_ids(organization_ids=resources)

        deployment_ids = []
        for org in organizations:
            if org.deploymentIds:
                deployment_ids.extend(org.deploymentIds)

        return any(self._match(params.roles, f"deployment/{dep_id}", permission) for dep_id in deployment_ids)

    @staticmethod
    def _match(roles: RolesParam, resource: str, permission: PolicyType) -> bool:
        return any(res == resource and role.has([permission]) for role, res in roles)

    @staticmethod
    def _find_deployment_id(params: CheckParams) -> str | None:
        body = params.body
        if isinstance(body, list):
            if body:
                body = params.body[0] if isinstance(params.body[0], dict) else {}
            else:
                body = {}

        return body.get(DEPLOYMENT_KEY)
