from abc import ABC, abstractmethod
from datetime import datetime


from sdk.calendar.dtos.calendar_event import CalendarEventDTO, CalendarEventLogDTO


class CalendarRepository(ABC):
    db = None

    @abstractmethod
    def create_calendar_event(self, event: CalendarEventDTO):
        raise NotImplementedError

    @abstractmethod
    def create_or_update_calendar_event(self, event: CalendarEventDTO):
        raise NotImplementedError

    @abstractmethod
    def create_calendar_event_log(self, event: CalendarEventLogDTO):
        raise NotImplementedError

    @abstractmethod
    def create_next_day_events(self, events: list[CalendarEventDTO]):
        raise NotImplementedError

    @abstractmethod
    def batch_create_calendar_events(self, events: list[CalendarEventDTO]):
        raise NotImplementedError

    @abstractmethod
    def batch_update_calendar_events(self, filter_query: dict, update_query: dict):
        raise NotImplementedError

    @abstractmethod
    def retrieve_calendar_event(self, event_id: str):
        raise NotImplementedError

    @abstractmethod
    def retrieve_calendar_events(self, mute_errors=False, **options):
        raise NotImplementedError

    def retrieve_calendar_events_from_models(self, models, mute_errors=False, **options):
        raise NotImplementedError

    @abstractmethod
    def retrieve_calendar_event_logs(self, **options):
        raise NotImplementedError

    @abstractmethod
    def update_calendar_event(self, event_id: str, event: CalendarEventDTO):
        raise NotImplementedError

    @abstractmethod
    def delete_calendar_event(self, event_id: str) -> str:
        raise NotImplementedError

    @abstractmethod
    def delete_calendar_log(self, log_id: str, user_id: str):
        raise NotImplementedError

    @abstractmethod
    def batch_delete_calendar_events(self, filter_options: dict):
        raise NotImplementedError

    @abstractmethod
    def batch_delete_calendar_events_by_ids(self, ids: list[str]):
        raise NotImplementedError

    @abstractmethod
    def batch_delete_next_day_events_by_parent_ids(self, parent_ids: list[str]) -> int:
        raise NotImplementedError

    @abstractmethod
    def retrieve_next_day_events(self, filter_options) -> list[CalendarEventDTO]:
        raise NotImplementedError

    @abstractmethod
    def batch_delete_next_day_events(self, event_ids: list[str] = None, query: dict = None):
        raise NotImplementedError

    @abstractmethod
    def batch_delete_next_day_events_for_user(self, user_id: str):
        raise NotImplementedError

    @abstractmethod
    def batch_delete_next_day_event_raw(self, filter_options):
        raise NotImplementedError

    @abstractmethod
    def clear_cached_events(self):
        raise NotImplementedError

    @abstractmethod
    def delete_user_events(self, user_id: str) -> dict[str, int]:
        raise NotImplementedError

    def bulk_delete_user_event_logs(self, user_id: str) -> int:
        raise NotImplementedError

    @abstractmethod
    def update_events_status(self, filter_options: dict, status: bool) -> int:
        raise NotImplementedError

    @abstractmethod
    def retrieve_calendar_event_logs_count_for_users(self, user_ids: list[str]) -> int:
        raise NotImplementedError

    @abstractmethod
    def end_matching_events(self, filter_options: dict, end_date: datetime) -> int:
        raise NotImplementedError
