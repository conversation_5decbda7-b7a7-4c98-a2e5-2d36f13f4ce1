import copy
import unittest

from sdk.common.utils.convertible import ConvertibleClassValidationError
from sdk.versioning.models.version import Version


class VersionModelTestCase(unittest.TestCase):
    def test_success_version_creation(self):
        try:
            version = {Version.SERVER: "1.17.1", Version.API: "v2"}
            Version.from_dict({**version, Version.SDK: version})
        except ConvertibleClassValidationError:
            self.fail()

    def test_failure_used_wrong_type(self):
        data = {Version.SERVER: "huma.com", Version.API: "v2"}
        data[Version.SDK] = data
        for key in data:
            copy_data = copy.deepcopy(data)
            with self.assertRaises(ConvertibleClassValidationError):
                copy_data[key] = 111
                Version.from_dict(copy_data)

    def test_success_version_with_loca(self):
        version = {Version.SERVER: "1.25.1+1.24", Version.API: "v2"}
        version = Version.from_dict({**version, Version.SDK: version})
        self.assertEqual(version.server.local, "1.24")


if __name__ == "__main__":
    unittest.main()
