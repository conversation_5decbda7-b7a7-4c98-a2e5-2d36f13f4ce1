from abc import ABC, abstractmethod

from sdk.deployment.dtos.builder import <PERSON><PERSON><PERSON>, UIWidget


class BuilderRepository(ABC):
    @abstractmethod
    def create_tab(self, deployment_id: str, tab: UITab) -> str:
        raise NotImplementedError

    @abstractmethod
    def retrieve_tab(self, deployment_id: str, tab_id: str) -> UITab:
        raise NotImplementedError

    @abstractmethod
    def update_tab(self, deployment_id: str, tab: UITab) -> str:
        raise NotImplementedError

    @abstractmethod
    def delete_tab(self, deployment_id: str, tab_id: str):
        raise NotImplementedError

    @abstractmethod
    def create_widget(self, deployment_id: str, tab_id: str, widget: UIWidget) -> str:
        raise NotImplementedError

    @abstractmethod
    def retrieve_widget(self, deployment_id: str, tab_id: str, widget_id: str) -> dict:
        raise NotImplementedError

    @abstractmethod
    def update_widget(self, deployment_id: str, tab_id: str, widget: UIWidget) -> str:
        raise NotImplementedError

    @abstractmethod
    def delete_widget(self, deployment_id: str, tab_id: str, widget_id: str):
        raise NotImplementedError

    @abstractmethod
    def reorder_tabs(self, deployment_id: str, tab_orders: list):
        raise NotImplementedError

    @abstractmethod
    def reorder_widgets(self, deployment_id: str, tab_id: str, widget_orders: list):
        raise NotImplementedError

    @abstractmethod
    def move_widget(self, deployment_id: str, source_tab_id: str, target_tab_id: str, widget_id: str) -> str:
        raise NotImplementedError
