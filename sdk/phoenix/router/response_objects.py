from dataclasses import field

from aenum import StrEnum

from sdk import convertibleclass
from sdk.common.usecase.response_object import ResponseObject


class HealthStatus(StrEnum):
    UP = "UP"
    DOWN = "DOWN"


@convertibleclass
class StatusDetails:
    STATUS = "status"

    status: HealthStatus = field(default=HealthStatus.DOWN)

    @property
    def is_up(self) -> bool:
        return self.status == HealthStatus.UP


@convertibleclass
class StatusResponseObject(ResponseObject):
    CACHE = "cache"

    cache: StatusDetails = field(default_factory=StatusDetails)

    @property
    def is_healthy(self) -> bool:
        return self.cache and self.cache.is_up
