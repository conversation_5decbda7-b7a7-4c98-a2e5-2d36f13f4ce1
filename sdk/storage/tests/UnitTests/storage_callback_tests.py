import unittest
from unittest.mock import MagicMock

from sdk.auth.events.post_delete_user_event import PostDeleteUserEvent
from sdk.authorization.events import PostUserProfileUpdateEvent
from sdk.authorization.events.post_clear_user_profile_fields_event import (
    PostClearUserProfileFieldsEvent,
)
from sdk.authorization.dtos.user import UserDTO
from sdk.common.adapter.file_storage_adapter import FileStorageAdapter
from sdk.common.utils import inject
from sdk.phoenix.config.server_config import PhoenixServerConfig
from sdk.storage.callbacks.callbacks import (
    on_user_delete_callback,
    delete_user_file_storage,
    delete_profile_picture_callback,
    update_profile_picture_callback,
)
from sdk.storage.dtos import FileField, FileStorageDTO
from sdk.storage.repository.storage_repository import StorageRepository

SAMPLE_ID = "600a8476a961574fb38157d5"
SAMPLE_ID1 = "600a8476a961574fb38157d2"


class StorageCallbackTestCase(unittest.TestCase):
    def setUp(self):
        self.config = MagicMock()
        self.storage_adapter = MagicMock()
        self.storage_repo = MagicMock()

        def bind(binder):
            binder.bind(PhoenixServerConfig, self.config)
            binder.bind(FileStorageAdapter, self.storage_adapter)
            binder.bind(StorageRepository, self.storage_repo)

        inject.clear_and_configure(bind)

    def test_success_on_user_delete_callback(self):
        event = PostDeleteUserEvent(user_id=SAMPLE_ID)
        expected_rsp = {FileStorageDTO.MODEL_NAME: self.storage_repo.delete_files_by_user_id.return_value}
        rsp = on_user_delete_callback(event)
        self.storage_repo.delete_files_by_user_id.assert_called_once_with(user_id=SAMPLE_ID)
        self.assertEqual(expected_rsp, rsp)

    def test_success_delete_user_file_storage(self):
        event = PostDeleteUserEvent(user_id=SAMPLE_ID)
        delete_user_file_storage(event)
        self.storage_adapter.bulk_delete_user_files.assert_called_once_with(
            bucket_name=self.config.server.storage.defaultBucket, user_id=SAMPLE_ID
        )

    def test_delete_profile_picture_callback(self):
        user = UserDTO(profilePicture=FileField(fileId=SAMPLE_ID))
        event = PostClearUserProfileFieldsEvent(SAMPLE_ID, [UserDTO.PROFILE_PICTURE], user)
        delete_profile_picture_callback(event)
        self.storage_repo.get_file.assert_called_once_with(event.previous_state.profilePicture.fileId)
        self.storage_adapter.delete_object.assert_called_once_with(
            self.config.server.storage.defaultBucket,
            self.storage_repo.get_file.return_value.key,
        )
        self.storage_repo.remove_file(event.previous_state.profilePicture.fileId)

    def test_test_delete_profile_picture_callback_picture_not_removed(self):
        user = UserDTO(profilePicture=FileField(fileId=SAMPLE_ID))
        event = PostClearUserProfileFieldsEvent(SAMPLE_ID, [UserDTO.SURGERY_DATE_TIME], user)
        delete_profile_picture_callback(event)
        self.storage_adapter.delete_object.assert_not_called()
        self.storage_repo.remove_file.assert_not_called()

    def test_update_profile_picture_callback(self):
        old_user = UserDTO(profilePicture=FileField(fileId=SAMPLE_ID))
        new_user = UserDTO(profilePicture=FileField(fileId=SAMPLE_ID1))
        event = PostUserProfileUpdateEvent(new_user, old_user)
        update_profile_picture_callback(event)

        self.storage_repo.get_file.assert_called_once_with(event.previous_state.profilePicture.fileId)
        self.storage_adapter.delete_object.assert_called_once_with(
            self.config.server.storage.defaultBucket,
            self.storage_repo.get_file.return_value.key,
        )
        self.storage_repo.remove_file(event.previous_state.profilePicture.fileId)

    def test_update_profile_picture_callback_no_picture_old_user(self):
        old_user = UserDTO()
        new_user = UserDTO(profilePicture=FileField(fileId=SAMPLE_ID))
        event = PostUserProfileUpdateEvent(new_user, old_user)
        update_profile_picture_callback(event)

        self.storage_adapter.delete_object.assert_not_called()
        self.storage_repo.remove_file.assert_not_called()

    def test_update_profile_picture_callback_picture_not_changed(self):
        old_user = UserDTO(profilePicture=FileField(fileId=SAMPLE_ID))
        new_user = UserDTO(profilePicture=FileField(fileId=SAMPLE_ID))
        event = PostUserProfileUpdateEvent(new_user, old_user)
        update_profile_picture_callback(event)

        self.storage_adapter.delete_object.assert_not_called()
        self.storage_repo.remove_file.assert_not_called()

    def test_update_profile_picture_callback_no_pictures(self):
        old_user, new_user = UserDTO(), UserDTO()
        event = PostUserProfileUpdateEvent(new_user, old_user)
        update_profile_picture_callback(event)

        self.storage_adapter.delete_object.assert_not_called()
        self.storage_repo.remove_file.assert_not_called()

    def test_update_profile_picture_callback_no_previous_user(self):
        old_user, new_user = UserDTO(), None
        event = PostUserProfileUpdateEvent(new_user, old_user)
        update_profile_picture_callback(event)

        self.storage_adapter.delete_object.assert_not_called()
        self.storage_repo.remove_file.assert_not_called()
