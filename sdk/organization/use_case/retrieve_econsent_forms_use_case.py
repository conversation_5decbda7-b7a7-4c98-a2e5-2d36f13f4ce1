from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams
from sdk.deployment.repository.custom_onboarding_repository import (
    CustomEConsentRepository,
)
from sdk.organization.router.organization_requests import (
    RetrieveEConsentFormsRequestObject,
)
from sdk.organization.router.organization_responses import (
    RetrieveEConsentsResponseObject,
)


class RetrieveEConsentFormsUseCase(UseCase):
    @autoparams()
    def __init__(self, repo: CustomEConsentRepository):
        self.repo = repo

    def process_request(self, request_object: RetrieveEConsentFormsRequestObject) -> RetrieveEConsentsResponseObject:
        econsent_forms, total = self.repo.retrieve_econsent_forms(
            skip=request_object.skip,
            limit=request_object.limit,
            organization_id=request_object.organizationId,
        )

        return RetrieveEConsentsResponseObject.from_dict(
            dict(
                items=[econsent_form.to_dict(include_none=False) for econsent_form in econsent_forms],
                total=total,
                limit=request_object.limit,
                skip=request_object.skip,
            )
        )
