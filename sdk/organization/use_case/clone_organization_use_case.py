from copy import copy
from functools import cached_property

from sdk.common.adapter.file_storage_adapter import FileStorageAdapter
from sdk.common.exceptions import CloneResourceException
from sdk.common.monitoring import report_exception
from sdk.common.utils.inject import autoparams
from sdk.deployment.router.deployment_requests import CloneDeploymentRequestObject
from sdk.deployment.service.deployment_service import DeploymentService
from sdk.deployment.use_case.clone_deployment_use_case import CloneDeploymentUseCase
from sdk.organization.dtos.organization import OrganizationDTO, OrganizationWithExtras
from sdk.organization.router.organization_requests import (
    CloneOrganizationRequestObject,
    CreateOrganizationRequestObject,
)
from sdk.organization.router.organization_responses import CloneOrganizationResponseObject
from sdk.organization.use_case.base_organization_use_case import BaseOrganizationUseCase
from sdk.organization.use_case.create_organization_use_case import CreateOrganizationUseCase
from sdk.phoenix.config.server_config import PhoenixServerConfig


class CloneOrganizationUseCase(BaseOrganizationUseCase):
    request_object: CloneOrganizationRequestObject

    RESET_FIELDS = (
        OrganizationDTO.ID,
        OrganizationDTO.CREATE_DATE_TIME,
        OrganizationDTO.UPDATE_DATE_TIME,
        OrganizationDTO.ROLES,
        OrganizationDTO.TARGET_CONSENTED,
        OrganizationDTO.DEPLOYMENT_IDS,
    )

    @autoparams()
    def __init__(self, file_storage: FileStorageAdapter, config: PhoenixServerConfig):
        super().__init__()
        self._file_storage = file_storage
        self._bucket = config.server.storage.defaultBucket
        self._new_organization_id = None
        self._cloned_deployment_ids = []
        self._copied_file_names = []

    def process_request(self, request_object: CloneOrganizationRequestObject) -> CloneOrganizationResponseObject:
        self._validate_deployments_are_org_members()
        self._create_organization()
        try:
            self._copy_roles()
            self._clone_deployments()
        except Exception as e:
            self._delete_created_organization()
            self._delete_created_deployments()
            self._delete_uploaded_files()
            msg = f"Failed to clone organization {self.request_object.referenceId} with error {e}"
            raise CloneResourceException(msg)

        return CloneOrganizationResponseObject(
            id=self._new_organization_id,
            deploymentIds=self._cloned_deployment_ids,
        )

    @cached_property
    def organization(self) -> OrganizationWithExtras:
        return self._service.retrieve_org_info(self.request_object.referenceId)

    def _validate_deployments_are_org_members(self):
        self.request_object.deploymentIds = set(self.request_object.deploymentIds)
        if not self.request_object.deploymentIds.issubset(set(self.organization.deploymentIds or [])):
            raise CloneResourceException(
                f"Organization {self.request_object.referenceId} does not have all the deployments"
            )

    def _create_organization(self):
        """Organization files are not copied. Since it is not implemented in UI"""
        org_copy = copy(self.organization)
        self._reset_fields(org_copy)
        source_org_dict = org_copy.to_dict(include_none=False)
        source_org_dict.update(
            {
                CreateOrganizationRequestObject.NAME: self.request_object.name,
                CreateOrganizationRequestObject.SUBMITTER_ID: self.request_object.submitter.id,
            }
        )
        create_org_req_obj = CreateOrganizationRequestObject.from_dict(source_org_dict)
        response = CreateOrganizationUseCase().execute(create_org_req_obj)
        self._new_organization_id = response.id

    def _copy_roles(self):
        if self.organization.roles is None:
            return

        self.repo.create_or_update_roles(organization_id=self._new_organization_id, roles=self.organization.roles)

    def _clone_deployments(self):
        for deployment_id in self.request_object.deploymentIds:
            req_obj = CloneDeploymentRequestObject.from_dict(
                {
                    CloneDeploymentRequestObject.NAME: self._get_deployment_name(deployment_id),
                    CloneDeploymentRequestObject.REFERENCE_ID: deployment_id,
                    CloneDeploymentRequestObject.ORGANIZATION_ID: self._new_organization_id,
                    CloneDeploymentRequestObject.SUBMITTER: self.request_object.submitter,
                }
            )
            use_case = CloneDeploymentUseCase()
            use_case.execute(req_obj)
            self._cloned_deployment_ids.append(use_case.new_deployment_id)
            self._copied_file_names.extend(use_case.copied_file_names)

    def _delete_created_organization(self):
        if self._new_organization_id is None:
            return

        try:
            self._service.delete_organization(self._new_organization_id)
        except Exception as e:
            report_exception(e, context_name="CloneOrganization")

    def _delete_created_deployments(self):
        service = DeploymentService()
        for deployment_id in self._cloned_deployment_ids:
            try:
                service.delete_deployment(deployment_id)
            except Exception as e:
                report_exception(e, context_name="CloneOrganization")

    def _delete_uploaded_files(self):
        self._file_storage.bulk_delete_objects(self._bucket, self._copied_file_names)

    def _reset_fields(self, organization: OrganizationDTO):
        for field in self.RESET_FIELDS:
            delattr(organization, field)
        return organization

    def _get_deployment_name(self, deployment_id: str) -> str:
        for deployment in self.organization.deployments:
            if deployment.id == deployment_id:
                return deployment.name
        raise ValueError(f"Deployment {deployment_id} not found")
