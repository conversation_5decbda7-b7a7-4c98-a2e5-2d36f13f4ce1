Get list of econsent forms by id
---
tags:
  - organization
security:
  - Bearer: []
parameters:
  - in: path
    name: organization_id
    type: string
    required: true
  - in: path
    name: econsent_id
    required: true
    type: string
  - name: body
    in: body
    description: body
    required: true
    schema:
      $ref: '#/definitions/RetrieveEConsentRequest'

responses:
  200:
    description: List of econsent forms in organization based on skip and limit
    schema:
      $ref: '#/definitions/RetrieveEConsentsResponse'

definitions:
  RetrieveEConsentRequest:
    type: object
    required:
      - skip
      - limit
    properties:
      skip:
        type: integer
        minimum: 0
        example: 0
      limit:
        type: integer
        minimum: 1
        example: 10

  RetrieveEConsentsResponse:
    type: object
    properties:
      items:
        type: array
        items:
          $ref: "#/definitions/EConsentForm"
      skip:
        type: integer
        example: 0
      limit:
        type: integer
        minimum: 1
        example: 10
      total:
        type: integer
        example: 1000
