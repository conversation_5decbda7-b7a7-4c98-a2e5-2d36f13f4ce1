from dataclasses import field, fields
from datetime import datetime
from typing import ClassVar, Self

import marshmallow
from apiflask import Schema
from flask import g, request
from marshmallow.fields import Dict, String

from sdk.authorization.boarding.manager import BoardingManager
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.authorization.dtos.role.default_roles import DefaultRoles
from sdk.authorization.dtos.role.role import RoleDTO, RoleName
from sdk.authorization.middleware import AuthorizationMiddleware
from sdk.common.common_models.sort import SortField
from sdk.common.exceptions.exceptions import InvalidRequestException, PermissionDenied
from sdk.common.localization.utils import Language
from sdk.common.localization.validators import incorrect_language_to_default, validate_localizations
from sdk.common.usecase.request_object import RequestObject
from sdk.common.utils import inject
from sdk.common.utils.convertible import (
    ConvertibleClassValidationError,
    convertibleclass,
    default_field,
    meta,
    positive_integer_field,
    required_field,
)
from sdk.common.utils.fields import id_field
from sdk.common.utils.marshmallow.schemas import BaseSchema
from sdk.common.utils.validators import (
    default_datetime_meta,
    must_be_only_one_of,
    must_be_present,
    must_not_be_present,
    not_empty,
    not_empty_list,
    validate_entity_name,
    validate_len,
    validate_not_too_long,
    validate_object_id,
)
from sdk.deployment.dtos.consent.consent import Consent
from sdk.deployment.dtos.consent.consent_log import ConsentLogDTO, ConsentLogContent, SharingOption
from sdk.deployment.dtos.deployment import (
    DeploymentDTO,
    DeploymentTemplateDTO,
    ModuleConfig,
    OnboardingModuleConfigDTO,
    Profile,
    ProfileFields,
    Security,
    TemplateCategory,
)
from sdk.deployment.dtos.econsent.econsent import EConsent
from sdk.deployment.dtos.econsent.econsent_log import EConsentLogDTO, EConsentLogContent
from sdk.deployment.dtos.econsent.econsent_section import EConsentSection
from sdk.deployment.dtos.learn import Learn, LearnArticle, LearnArticleContentType, LearnSection, OrderUpdateObject
from sdk.deployment.dtos.status import Status
from sdk.deployment.exceptions import ComponentDoesNotExist
from sdk.deployment.use_case.file_library_metadata import STANDARD_LIBRARIES
from sdk.key_action.models.config import KeyActionConfig
from sdk.module_result.dtos.user_note import UserNoteDTO
from sdk.module_result.modules.questionnaire.calculators.tools import MultiScoreParser
from sdk.phoenix.component_manager import PhoenixComponentManager
from sdk.phoenix.config.dynamic_component_config import BasePhoenixDynamicConfig
from sdk.phoenix.config.server_config import PhoenixServerConfig
from ._validators import (
    extra_validate_duration_iso,
    validate_default_units_field,
    validate_draft_version,
    validate_profile_fields,
    validate_profile_mandatory_fields,
)


@convertibleclass
class CreateDeploymentRequestObject(DeploymentDTO, RequestObject):
    @classmethod
    def validate(cls, deployment):
        must_not_be_present(
            id=deployment.id,
            learn=deployment.learn,
            consent=deployment.consent,
            moduleConfigs=deployment.moduleConfigs,
            onboardingConfigs=deployment.onboardingConfigs,
            keyActions=deployment.keyActions,
            roles=deployment.roles,
            builder=deployment.builder,
            enrollmentCounter=deployment.enrollmentCounter,
            updateDateTime=deployment.updateDateTime,
            createDateTime=deployment.createDateTime,
        )
        must_be_present(name=deployment.name)

        validate_profile_fields(deployment=deployment)
        validate_default_units_field(deployment)

    def post_init(self):
        super().post_init()
        self.status = Status.DRAFT
        self._set_default_value_for_profile_fields_if_not_set()
        self._set_new_roles_support_flag()

    def _set_new_roles_support_flag(self):
        roles_config = {"newRolesSupport": True}
        if self.features.portal:
            if "newRolesSupport" not in self.features.portal:
                self.features.portal.update(roles_config)
        else:
            self.features.portal = roles_config

    def _set_default_value_for_profile_fields_if_not_set(self):
        if not self.profile:
            self.profile = Profile()
        if not self.profile.fields:
            profile_fields = {_field.name: None for _field in fields(ProfileFields)}  # noqa
            profile_fields.update(
                {
                    ProfileFields.BIOLOGICAL_SEX: True,
                    ProfileFields.DATE_OF_BIRTH: True,
                    ProfileFields.MANDATORY_ONBOARDING_FIELDS: [
                        ProfileFields.BIOLOGICAL_SEX,
                        ProfileFields.DATE_OF_BIRTH,
                    ],
                    ProfileFields.GENDER: False,
                }
            )
            self.profile.fields = ProfileFields(**profile_fields)
        elif self.profile.fields:
            self._init_profile_fields(ProfileFields.DATE_OF_BIRTH)
            self._init_profile_fields(ProfileFields.BIOLOGICAL_SEX)
            if not self.profile.fields.gender:
                self.profile.fields.gender = False

    def _init_profile_fields(self, profile_field: str):
        mob_fields_list = self.profile.fields.mandatoryOnboardingFields
        mob_fields_set = set(mob_fields_list) if mob_fields_list else set()

        profile_field_value = getattr(self.profile.fields, profile_field)

        if profile_field_value is None:
            setattr(self.profile.fields, profile_field, True)
            mob_fields_set.add(profile_field)

        elif not profile_field_value and profile_field in mob_fields_set:
            mob_fields_set.remove(profile_field)

        self.profile.fields.mandatoryOnboardingFields = list(mob_fields_set)


@convertibleclass
class CreateConsentRequestObject(Consent, RequestObject):
    @classmethod
    def validate(cls, consent):
        must_not_be_present(id=consent.id, createDateTime=consent.createDateTime)
        must_be_present(enabled=consent.enabled)


@convertibleclass
class CreateLearnSectionRequestObject(LearnSection, RequestObject):
    @classmethod
    def validate(cls, learn_section):
        must_not_be_present(
            id=learn_section.id,
            updateDateTime=learn_section.updateDateTime,
            createDateTime=learn_section.createDateTime,
            articles=learn_section.articles,
        )


@convertibleclass
class CreateLearnArticleRequestObject(LearnArticle, RequestObject):
    @classmethod
    def validate(cls, article):
        must_not_be_present(
            id=article.id,
            updateDateTime=article.updateDateTime,
            createDateTime=article.createDateTime,
        )
        if article.content and article.content.type is not LearnArticleContentType.CMS:
            title_length_validator = validate_len(min=1, max=100)
            if not title_length_validator(article.title):
                raise ConvertibleClassValidationError("Title should be between 1 and 100 characters")


@convertibleclass
class CreateModuleConfigRequestObject(ModuleConfig, RequestObject):
    class Meta:
        exclude = [ModuleConfig.LEARN_ARTICLES]
        unknown = marshmallow.EXCLUDE

    @classmethod
    def validate(cls, module_config):
        must_not_be_present(
            id=module_config.id,
            updateDateTime=module_config.updateDateTime,
            createDateTime=module_config.createDateTime,
        )

        must_be_present(
            moduleConfigId=module_config.moduleId,
            moduleConfigStatus=module_config.status,
        )
        ModuleConfig.run_validation(module_config)


@convertibleclass
class ValidateQuestionnaireDimensionsRequestObject(RequestObject):
    configBody: dict = required_field()

    @classmethod
    def validate(cls, request_obj: Self):
        MultiScoreParser.validate_dimensions(request_obj.configBody, check_names=False)


@convertibleclass
class OnboardingRequestObject(RequestObject):
    ONBOARDING_MODULE_CONFIG = "onboardingModuleConfig"
    DEPLOYMENT_ID = "deploymentId"
    SUBMITTER_ID = "submitterId"

    onboardingModuleConfig: OnboardingModuleConfigDTO = required_field()
    deploymentId: str = required_field(metadata=meta(validate_object_id))
    submitterId: str = required_field(metadata=meta(validate_object_id))

    @classmethod
    def validate(cls, request_obj: Self):
        must_be_present(
            onboardingId=request_obj.onboardingModuleConfig.onboardingId,
            order=request_obj.onboardingModuleConfig.order,
        )
        BoardingManager.validate_onboarding_config_body(
            request_obj.onboardingModuleConfig.onboardingId,
            request_obj.onboardingModuleConfig.configBody,
        )


@convertibleclass
class CreateOnboardingConfigRequestObject(OnboardingRequestObject):
    pass


@convertibleclass
class UpdateOnboardingConfigRequestObject(OnboardingRequestObject):
    pass


@convertibleclass
class DeleteOnboardingConfigRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"
    ONBOARDING_ID = "onboardingId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))
    onboardingId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class CreateKeyActionConfigRequestObject(KeyActionConfig, RequestObject):
    durationIso: str = required_field(metadata=meta(extra_validate_duration_iso))
    notifyEvery: str = default_field(metadata=meta(extra_validate_duration_iso))

    @classmethod
    def validate(cls, action: KeyActionConfig):
        must_not_be_present(
            id=action.id,
            updateDateTime=action.updateDateTime,
            createDateTime=action.createDateTime,
        )

        must_be_only_one_of(
            learnArticleId=action.learnArticleId,
            moduleId=action.moduleId,
            could_be_none=True,
        )
        CreateKeyActionConfigRequestObject.validate_key_action_type_fields(action)

    @classmethod
    def validate_key_action_type_fields(cls, action: KeyActionConfig):
        if action.is_for_module():
            must_be_present(moduleId=action.moduleId, moduleConfigId=action.moduleConfigId)
            action.reset_attributes([action.LEARN_ARTICLE_ID])
        elif action.is_for_learn():
            must_be_present(learnArticleId=action.learnArticleId)
            action.reset_attributes([action.MODULE_ID, action.MODULE_CONFIG_ID])


@convertibleclass
class UpdateModuleConfigRequestObject(ModuleConfig, RequestObject):
    class Meta:
        exclude = [ModuleConfig.LEARN_ARTICLES]
        unknown = marshmallow.EXCLUDE

    @classmethod
    def validate(cls, module_config: ModuleConfig):
        must_not_be_present(
            updateDateTime=module_config.updateDateTime,
            createDateTime=module_config.createDateTime,
        )

        must_be_present(moduleId=module_config.moduleId, status=module_config.status)
        ModuleConfig.run_validation(module_config)


@convertibleclass
class UpdateLearnSectionRequestObject(LearnSection, RequestObject):
    @classmethod
    def validate(cls, section_obj):
        must_not_be_present(
            articles=section_obj.articles,
            updateDateTime=section_obj.updateDateTime,
            createDateTime=section_obj.createDateTime,
        )

        must_be_present(learnSectionId=section_obj.id)


@convertibleclass
class UpdateArticleRequestObject(LearnArticle, RequestObject):
    @classmethod
    def validate(cls, article_obj):
        must_not_be_present(
            updateDateTime=article_obj.updateDateTime,
            createDateTime=article_obj.createDateTime,
        )

        must_be_present(articleId=article_obj.id)


@convertibleclass
class UpdateKeyActionConfigRequestObject(CreateKeyActionConfigRequestObject):
    @classmethod
    def validate(cls, action: KeyActionConfig):
        must_be_present(id=action.id)
        UpdateKeyActionConfigRequestObject.validate_key_action_type_fields(action)


@convertibleclass
class SignConsentRequestData(ConsentLogContent, RequestObject):
    pass


@convertibleclass
class SignConsentRequestObject(ConsentLogDTO, RequestObject):
    sharingOption: SharingOption = default_field()

    @classmethod
    def validate(cls, consent_log):
        must_not_be_present(
            id=consent_log.id,
            createDateTime=consent_log.createDateTime,
            revision=consent_log.revision,
        )

        must_be_present(userId=consent_log.userId, consentId=consent_log.consentId)


@convertibleclass
class RetrieveDeploymentsRequestObject(RequestObject):
    SKIP = "skip"
    LIMIT = "limit"
    SEARCH_CRITERIA = "searchCriteria"
    SEARCH = "search"
    SORT = "sort"
    STATUS = "status"

    skip: int = positive_integer_field(default=None)
    limit: int = positive_integer_field(default=None)
    searchCriteria: str = default_field(metadata=meta(validate_not_too_long))  # deprecated, use search instead
    search: str = default_field(metadata=meta(validate_not_too_long))
    sort: list[SortField] = default_field()
    status: list[Status] = default_field()

    def __iter__(self):
        return iter(self.__dict__.values())


@convertibleclass
class RetrieveDeploymentRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"

    authzUser: AuthorizedUser = default_field()
    deploymentId: str = id_field(required=True)


@convertibleclass
class RetrieveDeploymentByRevisionRequestObject(RetrieveDeploymentRequestObject):
    VERSION_NUMBER = "versionNumber"

    versionNumber: int = default_field()


@convertibleclass
class RetrieveLatestConsentRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class RetrieveLabelsRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class CreateLabelsRequestObjectBody:
    TEXTS = "texts"

    texts: list[str] = required_field(metadata=meta(not_empty_list))


@convertibleclass
class CreateLabelsRequestObject(RetrieveLabelsRequestObject, CreateLabelsRequestObjectBody):
    SUBMITTER_ID = "submitterId"

    submitterId: str = required_field(metadata=meta(validate_object_id))

    @classmethod
    def validate(cls, labels):
        if not labels.texts:
            raise ConvertibleClassValidationError("Texts should not be empty")

        for text in labels.texts:
            if not text:
                raise ConvertibleClassValidationError("Text should not be blank")


@convertibleclass
class UpdateLabelRequestObjectBody:
    TEXT = "text"

    text: str = required_field(metadata=meta(not_empty))


@convertibleclass
class BaseModifyLabelRequestObject(RequestObject):
    LABEL_ID = "labelId"
    DEPLOYMENT_ID = "deploymentId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))
    labelId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class UpdateLabelRequestObject(BaseModifyLabelRequestObject, UpdateLabelRequestObjectBody):
    SUBMITTER_ID = "submitterId"

    submitterId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class DeleteLabelRequestObject(BaseModifyLabelRequestObject):
    pass


@convertibleclass
class UpdateDeploymentRequestObject(DeploymentDTO, RequestObject):
    version: int = default_field()
    language: str = field(default=None, metadata=meta(value_to_field=incorrect_language_to_default))

    @classmethod
    def validate(cls, deployment: DeploymentDTO):
        deployment.validate_keys_should_not_be_present_on_update()
        must_not_be_present(
            createDateTime=deployment.createDateTime,
            updateDateTime=deployment.updateDateTime,
            version=deployment.version,
            isSaMD=deployment.isSaMD,
        )
        must_be_present(id=deployment.id)

        primary_fields_count = 0
        for name, config in (deployment.extraCustomFields or {}).items():
            if config.isPrimary:
                primary_fields_count += 1
        if primary_fields_count > 1:
            raise ConvertibleClassValidationError("Only one Primary Field can exist per deployment.")

        validate_profile_fields(deployment=deployment)
        validate_profile_mandatory_fields(deployment=deployment)
        validate_default_units_field(deployment)

    def post_init(self):
        if self.mfaRequired is not None and not self.security:
            self.security = Security(mfaRequired=self.mfaRequired, appLockRequired=False)

    @marshmallow.pre_load
    def add_language_if_missing(self: Schema, data: dict, **kwargs) -> dict:
        # don't add language if missing, avoid changing deployment language based on header
        return data


@convertibleclass
class UpdateDeploymentRequestBody(UpdateDeploymentRequestObject):
    class Meta:
        exclude = ("version",)
        unknown = marshmallow.EXCLUDE

    @classmethod
    def validate(cls, deployment: DeploymentDTO):
        """To skip RequestObject's validate."""
        pass


@convertibleclass
class DeleteDeploymentRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class DeleteModuleConfigRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"
    MODULE_CONFIG_ID = "moduleConfigId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))
    moduleConfigId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class DeleteLearnSectionRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"
    SECTION_ID = "sectionId"
    USER_ID = "userId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))
    sectionId: str = required_field(metadata=meta(validate_object_id))
    userId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class DeleteArticleRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"
    SECTION_ID = "sectionId"
    ARTICLE_ID = "articleId"
    USER_ID = "userId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))
    sectionId: str = required_field(metadata=meta(validate_object_id))
    articleId: str = required_field(metadata=meta(validate_object_id))
    userId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class DeleteKeyActionRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"
    KEY_ACTION_CONFIG_ID = "keyActionConfigId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))
    keyActionConfigId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class EncryptValueRequestObjectBody(RequestObject):
    VALUE = "value"

    value: str = required_field(metadata=meta(lambda n: not_empty))


@convertibleclass
class EncryptValueRequestObject(EncryptValueRequestObjectBody):
    DEPLOYMENT_ID = "deploymentId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class CreateEConsentRequestObject(EConsent, RequestObject):
    @classmethod
    def validate(cls, econsent):
        must_not_be_present(id=econsent.id, createDateTime=econsent.createDateTime)

        must_be_present(
            enabled=econsent.enabled,
            title=econsent.title,
            overviewText=econsent.overviewText,
            contactText=econsent.contactText,
        )

        cls._validate_econsent_sections(econsent.sections)

    @staticmethod
    def _validate_econsent_sections(sections: list[EConsentSection]):
        for section in sections:
            if section.contentType == EConsentSection.ContentType.IMAGE:
                must_not_be_present(videoUrl=section.videoUrl, videoLocation=section.videoLocation)
                must_be_only_one_of(
                    thumbnailUrl=section.thumbnailUrl,
                    thumbnailLocation=section.thumbnailLocation,
                )
            elif section.contentType == EConsentSection.ContentType.VIDEO:
                must_be_only_one_of(
                    thumbnailUrl=section.thumbnailUrl,
                    thumbnailLocation=section.thumbnailLocation,
                )
                must_be_only_one_of(videoUrl=section.videoUrl, videoLocation=section.videoLocation)


@convertibleclass
class SignEConsentRequestData(EConsentLogContent, RequestObject):
    @marshmallow.validates_schema
    def validate_consent_option(self, data: dict, **_):
        if data.get(EConsentLogDTO.CONSENT_OPTION) != EConsentLogDTO.EConsentOption.NOT_PARTICIPATE:
            must_be_present(
                signature=data.get(EConsentLogDTO.SIGNATURE),
            )


@convertibleclass
class SignEConsentRequestObject(EConsentLogDTO, RequestObject):
    USER = "user"
    REQUEST_ID = "requestId"

    requestId: str = required_field(metadata=meta(validate_len(32, 36)))
    user: AuthorizedUser = required_field()
    sharingOption: SharingOption = default_field()

    @classmethod
    def validate(cls, econsent_log):
        must_not_be_present(
            id=econsent_log.id,
            createDateTime=econsent_log.createDateTime,
            revision=econsent_log.revision,
            pdfLocation=econsent_log.pdfLocation,
        )
        must_be_present(userId=econsent_log.userId, econsentId=econsent_log.econsentId)
        if econsent_log.consentOption != EConsentLogDTO.EConsentOption.NOT_PARTICIPATE:
            must_be_present(signature=econsent_log.signature)


@convertibleclass
class WithdrawEConsentRequestBody(RequestObject):
    LOG_ID = "logId"

    logId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class WithdrawEConsentRequestObject(WithdrawEConsentRequestBody):
    DEPLOYMENT_ID = "deploymentId"
    ECONSENT_ID = "econsentId"
    USER_ID = "userId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))
    econsentId: str = required_field(metadata=meta(validate_object_id))
    userId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class DeploymentRoleUpdateObjectBody(RequestObject):
    ROLES = "roles"

    roles: list[RoleDTO] = default_field()

    @classmethod
    def validate(cls, instance):
        default_roles = inject.instance(DefaultRoles)
        roles = instance.roles or []
        role_names = set(role.name for role in roles)
        if len(role_names) != len(instance.roles):
            raise InvalidRequestException("Can't create multiple roles with same name.")

        msg_template = "Custom role name cannot be one of default roles %s"
        for role in roles:
            must_be_present(name=role.name, permissions=role.has_extra_permissions() or None)
            is_default_role = role.name.lower() in {x.lower() for x in default_roles}
            if is_default_role:
                msg = msg_template % f"[{', '.join(default_roles)}]"
                raise InvalidRequestException(msg)


@convertibleclass
class DeploymentRoleUpdateObject(DeploymentRoleUpdateObjectBody):
    DEPLOYMENT_ID = "deploymentId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class AddUserNotesV2RequestData(RequestObject):
    NOTE = "note"
    TYPE = "type"

    note: str = required_field(metadata=meta(not_empty))
    type: UserNoteDTO.UserNoteType = field(default=UserNoteDTO.UserNoteType.USER_OBSERVATION_NOTES)


@convertibleclass
class AddUserNotesV2RequestObject(AddUserNotesV2RequestData):
    USER_ID = "userId"
    SUBMITTER_ID = "submitterId"
    DEPLOYMENT_ID = "deploymentId"
    CREATE_DATE_TIME = "createDateTime"

    userId: str = required_field(metadata=meta(validate_object_id))
    submitterId: str = default_field(metadata=meta(validate_object_id, value_to_field=str))
    deploymentId: str = required_field(metadata=meta(validate_object_id))
    createDateTime: datetime = default_field(metadata=default_datetime_meta())


@convertibleclass
class RetrieveUserNotesThumbnailsRequestObject(RequestObject):
    USER_ID = "userId"
    SUBMITTER_ID = "submitterId"
    DEPLOYMENT_ID = "deploymentId"
    START_DATE_TIME = "startDateTime"
    END_DATE_TIME = "endDateTime"

    userId: str = id_field(required=True, view_arg="user_id")
    deploymentId: str = id_field(required=True, view_arg="deployment_id")
    startDateTime: datetime = default_field(metadata=default_datetime_meta(data_key="start"))
    endDateTime: datetime = default_field(metadata=default_datetime_meta(data_key="end"))


@convertibleclass
class ResolveUserFlagsRequestObject(RequestObject):
    USER_ID = "userId"
    DEPLOYMENT_ID = "deploymentId"
    NOTE_ID = "noteId"

    userId: str = required_field(metadata=meta(validate_object_id))
    deploymentId: str = required_field(metadata=meta(validate_object_id))
    noteId: str = required_field(metadata=meta(validate_object_id))
    resolveDateTime: datetime = field(default_factory=datetime.utcnow, metadata=default_datetime_meta())


@convertibleclass
class RetrieveUserNotesQueryData(RequestObject):
    SKIP = "skip"
    LIMIT = "limit"

    skip: int = positive_integer_field(default=0)
    limit: int = positive_integer_field(default=100)


@convertibleclass
class RetrieveUserNotesRequestObject(RetrieveUserNotesQueryData):
    USER_ID = "userId"
    DEPLOYMENT_ID = "deploymentId"

    userId: str = required_field(metadata=meta(validate_object_id))
    deploymentId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class RetrieveSingleUserNoteRequestObject(RequestObject):
    USER_ID = "userId"
    DEPLOYMENT_ID = "deploymentId"
    NOTE_ID = "noteId"

    userId: str = required_field(metadata=meta(validate_object_id))
    deploymentId: str = required_field(metadata=meta(validate_object_id))
    noteId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class UpdateLocalizationsRequestObject(RequestObject):
    class LocalizationSchema(BaseSchema):
        class Meta:
            include = {lang: Dict(String, String) for lang in Language.get_valid_languages()}

    DEPLOYMENT_ID = "deploymentId"
    SUBMITTER_ID = "submitterId"
    LOCALIZATIONS = "localizations"

    deploymentId: str = required_field(metadata=meta(validate_object_id))
    submitterId: str = required_field(metadata=meta(validate_object_id))
    localizations: dict = required_field(metadata=meta(validate_localizations))


@convertibleclass
class RetrieveLocalizationsRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"
    LANGUAGES = "languages"

    deploymentId: str = required_field(metadata=meta(validate_object_id))
    languages: list[str] = default_field(
        metadata=meta(lambda languages: all(Language.is_valid_language(language) for language in languages))
    )

    @marshmallow.pre_load
    def add_deployment_id(self, data, **kwargs):
        data[RetrieveLocalizationsRequestObject.DEPLOYMENT_ID] = request.view_args.get("deployment_id")
        return data


@convertibleclass
class ImportLocalizationsRequestObject(RetrieveLocalizationsRequestObject):
    FILE_ID = "fileId"
    SUBMITTER_ID = "submitterId"

    fileId: str = required_field(metadata=meta(validate_object_id))
    submitterId: str = required_field(metadata=meta(validate_object_id))

    @marshmallow.pre_load
    def add_submitter_id(self, data, **kwargs):
        data[ImportLocalizationsRequestObject.SUBMITTER_ID] = g.user.id
        return data


@convertibleclass
class CloneDeploymentRequestObject(RequestObject):
    NAME = "name"
    REFERENCE_ID = "referenceId"
    ORGANIZATION_ID = "organizationId"
    IS_SAMD = "isSaMD"
    SUBMITTER = "submitter"

    name: str = required_field(metadata=meta(validate_entity_name))
    referenceId: str = required_field(metadata=meta(not_empty))
    organizationId: str = required_field(metadata=meta(not_empty))
    isSaMD: bool = field(default=False)
    submitter: AuthorizedUser = default_field()

    @marshmallow.validates_schema
    def add_submitter(self, data: dict, **_):
        data["submitter"] = g.authz_user
        return data


@convertibleclass
class MoveDeploymentsRequestObjectBody(RequestObject):
    DEPLOYMENT_IDS = "deploymentIds"
    SOURCE_ORGANIZATION_ID = "sourceOrganizationId"
    TARGET_ORGANIZATION_ID = "targetOrganizationId"

    deploymentIds: list[str] = required_field(metadata=meta(not_empty_list))
    sourceOrganizationId: str = required_field(metadata=meta(not_empty))
    targetOrganizationId: str = required_field(metadata=meta(not_empty))


@convertibleclass
class MoveDeploymentsRequestObject(MoveDeploymentsRequestObjectBody):
    SUBMITTER = "submitter"

    submitter: AuthorizedUser = required_field()


@convertibleclass
class ReorderRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"
    SUBMITTER_ID = "submitterId"
    ITEMS = "items"

    deploymentId: str = required_field(metadata=meta(validate_object_id))
    submitterId: str = required_field(metadata=meta(validate_object_id))
    items: list[OrderUpdateObject] = required_field()


@convertibleclass
class ReorderLearnArticles(ReorderRequestObject):
    SECTION_ID = "sectionId"

    sectionId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class RetrieveLocalizableFieldsRequestObject(RetrieveDeploymentRequestObject):
    pass


@convertibleclass
class GenerateMasterTranslationRequestObject(RetrieveDeploymentRequestObject):
    SUBMITTER_ID = "submitterId"

    submitterId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class RetrieveDeploymentKeyActionsRequestObjectBody(RequestObject):
    START_DATE = "startDate"
    END_DATE = "endDate"
    TRIGGER_TIME = "triggerTime"

    startDate: datetime = required_field(metadata=default_datetime_meta())
    endDate: datetime = required_field(metadata=default_datetime_meta())
    triggerTime: datetime = default_field(metadata=default_datetime_meta())


@convertibleclass
class RetrieveDeploymentKeyActionsRequestObject(RetrieveDeploymentKeyActionsRequestObjectBody):
    DEPLOYMENT_ID = "deploymentId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))

    def post_init(self):
        if not self.triggerTime:
            self.triggerTime = datetime.utcnow()


@convertibleclass
class CreateMultipleModuleConfigsRequestObjectBody(RequestObject):
    MODULE_CONFIGS = "moduleConfigs"

    moduleConfigs: list[CreateModuleConfigRequestObject] = required_field(metadata=meta(not_empty))


@convertibleclass
class CreateMultipleModuleConfigsRequestObject(CreateMultipleModuleConfigsRequestObjectBody):
    DEPLOYMENT_ID = "deploymentId"
    SUBMITTER_ID = "submitterId"

    deploymentId: str = required_field(metadata=meta(validate_object_id))
    submitterId: str = required_field(metadata=meta(validate_object_id))


@convertibleclass
class CreateDeploymentTemplateRequestObject(DeploymentTemplateDTO, RequestObject):
    SUBMITTER = "submitter"

    submitter: AuthorizedUser = default_field()

    @classmethod
    def validate(cls, deployment_template: Self):
        must_not_be_present(id=deployment_template.id)
        if deployment_template.category == TemplateCategory.SELF_SERVICE:
            must_be_present(organizationIds=deployment_template.organizationIds)

        cls._validate_permission_based_on_user_role(deployment_template)
        cls._remove_unneeded_fields_from_template(deployment_template.template)
        deployment_template.__delattr__(cls.SUBMITTER)

    @staticmethod
    def _validate_permission_based_on_user_role(
        deployment_template: "CreateDeploymentTemplateRequestObject",
    ):
        role_assignment = deployment_template.submitter.role_assignment.roleId

        if role_assignment in (RoleName.SUPER_ADMIN, RoleName.HUMA_SUPPORT):
            if deployment_template.category == TemplateCategory.SELF_SERVICE:
                raise PermissionDenied

        elif role_assignment == RoleName.ACCOUNT_MANAGER:
            if deployment_template.category != TemplateCategory.SELF_SERVICE:
                raise PermissionDenied

            if deployment_template.isVerified is True:
                raise PermissionDenied

            user_orgs = deployment_template.submitter.organization_ids()
            for org in deployment_template.organizationIds:
                if org not in user_orgs:
                    raise PermissionDenied

    @staticmethod
    def _remove_unneeded_fields_from_template(deployment_template: DeploymentDTO):
        template_fields_to_exist = {
            DeploymentDTO.DESCRIPTION,
            DeploymentDTO.STATUS,
            DeploymentDTO.MODULE_CONFIGS,
            DeploymentDTO.LEARN,
            DeploymentDTO.CONSENT,
            DeploymentDTO.KEY_ACTIONS,
            DeploymentDTO.ECONSENT,
            DeploymentDTO.SURGERY_DETAILS,
            DeploymentDTO.PRIVACY_POLICY_URL,
            DeploymentDTO.EULA_URL,
            DeploymentDTO.CONTACT_US_URL,
            DeploymentDTO.TERM_AND_CONDITION_URL,
            DeploymentDTO.ONBOARDING_CONFIGS,
            DeploymentDTO.LOCALIZATIONS,
            DeploymentDTO.PROFILE,
            DeploymentDTO.FEATURES,
            DeploymentDTO.EXTRA_CUSTOM_FIELDS,
            DeploymentDTO.PRIVACY_POLICY_OBJECT,
            DeploymentDTO.TERM_AND_CONDITION_OBJECT,
            DeploymentDTO.EULA_OBJECT,
        }
        for field_name in deployment_template.__annotations__.keys():
            if field_name not in template_fields_to_exist:
                deployment_template.__delattr__(field_name)


@convertibleclass
class OrganizationIdHeaders:
    Schema: ClassVar[type[Schema]] = Schema

    organizationId: str = default_field(metadata=meta(data_key=AuthorizationMiddleware.ORGANIZATION_HEADER_KEY))


@convertibleclass
class RetrieveDeploymentTemplatesRequestObject(RequestObject):
    ORGANIZATION_ID = "organizationId"
    SUBMITTER = "submitter"

    submitter: AuthorizedUser = default_field()
    organizationId: str = id_field()

    @classmethod
    def validate(cls, instance):
        if instance.submitter.role_assignment.roleId in {
            RoleName.ACCOUNT_MANAGER,
            RoleName.ORGANIZATION_EDITOR,
            RoleName.ORGANIZATION_OWNER,
        }:
            must_be_present(id=instance.organizationId)
            user_orgs = instance.submitter.organization_ids()
            if instance.organizationId not in user_orgs:
                raise PermissionDenied


@convertibleclass
class RetrieveDeploymentTemplateRequestObject(RetrieveDeploymentTemplatesRequestObject):
    TEMPLATE_ID = "templateId"

    templateId: str = id_field(required=True)


@convertibleclass
class DeleteDeploymentTemplateRequestObject(RetrieveDeploymentTemplateRequestObject):
    @classmethod
    def validate(cls, instance):
        if instance.submitter.role_assignment == RoleName.ACCOUNT_MANAGER:
            user_orgs = instance.submitter.organization_ids()
            if instance.organizationId not in user_orgs:
                raise PermissionDenied


@convertibleclass
class UpdateDeploymentTemplateRequestObject(RequestObject):
    TEMPLATE_ID = "templateId"
    ORGANIZATION_ID = "organizationId"
    SUBMITTER = "submitter"
    UPDATE_DATA = "updateData"

    submitter: AuthorizedUser = default_field()
    templateId: str = id_field(required=True)
    organizationId: str = id_field()
    updateData: DeploymentTemplateDTO = required_field()

    @classmethod
    def validate(cls, instance):
        role_id = instance.submitter.role_assignment.roleId
        if role_id in {RoleName.SUPER_ADMIN, RoleName.HUMA_SUPPORT}:
            if instance.updateData.category == TemplateCategory.SELF_SERVICE:
                raise PermissionDenied
            return

        if role_id == RoleName.ACCOUNT_MANAGER:
            must_be_present(id=instance.organizationId)
            user_orgs = instance.submitter.organization_ids()
            if instance.organizationId not in user_orgs:
                raise PermissionDenied

            if instance.updateData.category != TemplateCategory.SELF_SERVICE:
                raise PermissionDenied

            if instance.updateData.isVerified is True:
                raise PermissionDenied


class FileLibraryValidationMixin:
    @classmethod
    def validate(cls, req_obj: Self):
        is_deployment_library = req_obj.libraryId not in STANDARD_LIBRARIES
        if is_deployment_library and not req_obj.deploymentId:
            msg = f"Deployment ID is required for accessing files in {req_obj.libraryId}"
            raise InvalidRequestException(msg)


@convertibleclass
class AddFilesToLibraryRequestObjectBody(RequestObject):
    LIBRARY_ID = "libraryId"
    DEPLOYMENT_ID = "deploymentId"
    FILE_IDS = "fileIds"

    libraryId: str = required_field(metadata=meta(not_empty))
    deploymentId: str = id_field()
    fileIds: list[str] = required_field(metadata=meta(not_empty_list))


@convertibleclass
class AddFilesToLibraryRequestObject(AddFilesToLibraryRequestObjectBody, FileLibraryValidationMixin):
    USER_ID = "userId"

    userId: str = id_field()


@convertibleclass
class FileLibraryRequestQuery(RequestObject):
    OFFSET = "offset"
    LIMIT = "limit"
    DEPLOYMENT_ID = "deploymentId"

    offset: int = positive_integer_field(default=0)
    limit: int = positive_integer_field(default=100)
    deploymentId: str = id_field()


@convertibleclass
class RetrieveFilesInLibraryRequestObject(FileLibraryRequestQuery, FileLibraryValidationMixin):
    USER_ID = "userId"
    LIBRARY_ID = "libraryId"

    userId: str = id_field()
    libraryId: str = required_field(metadata=meta(not_empty))


@convertibleclass
class LibraryFileObject:
    ID = "id"
    FILE_NAME = "fileName"
    FILE_SIZE = "fileSize"
    CONTENT_TYPE = "contentType"
    METADATA = "metadata"

    id: str = id_field()
    fileName: str = default_field()
    fileSize: int = default_field()
    contentType: str = default_field()
    metadata: dict = default_field()


@convertibleclass
class ComponentConfigRequestBody(RequestObject):
    COMPONENT_NAME = "componentName"
    CONFIG = "config"

    componentName: str = required_field(metadata=meta(not_empty))
    config: BasePhoenixDynamicConfig = default_field()


@convertibleclass
class ComponentConfigRequestObject(ComponentConfigRequestBody):
    CONFIG_DICT = "configDict"

    configDict: dict = required_field()

    def post_init(self) -> None:
        component_manager = inject.instance(PhoenixComponentManager)
        try:
            component = component_manager.component(self.componentName)
        except Exception:
            raise ComponentDoesNotExist
        dynamic_config_class = component.dynamic_config_class
        self.config = dynamic_config_class.from_dict(self.configDict)


@convertibleclass
class CreateComponentConfigRequestObject(ComponentConfigRequestObject):
    pass


@convertibleclass
class UpdateComponentConfigRequestObject(ComponentConfigRequestObject):
    pass


@convertibleclass
class DeleteComponentConfigRequestObject(RequestObject):
    COMPONENT_NAME = "componentName"

    componentName: str = required_field(metadata=meta(not_empty))


@convertibleclass
class CheckImprovedOnboardingFlagsRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"
    CONFIG = "config"

    deploymentId: str = id_field(required=True)
    config: PhoenixServerConfig = required_field()


@convertibleclass
class CreateFullDeploymentRequestObject(CreateDeploymentRequestObject):
    @convertibleclass
    class FullDeploymentLearn(Learn, RequestObject):
        @convertibleclass
        class CreateFullDeploymentLearnSectionRequestObject(CreateLearnSectionRequestObject):
            articles: list[CreateLearnArticleRequestObject] = default_field()

            @classmethod
            def validate(cls, learn_section):
                must_not_be_present(
                    id=learn_section.id,
                    updateDateTime=learn_section.updateDateTime,
                    createDateTime=learn_section.createDateTime,
                )

        sections: list[CreateFullDeploymentLearnSectionRequestObject] = default_field()

    @convertibleclass
    class CreateFullOnboardingConfigRequestObject(OnboardingModuleConfigDTO, RequestObject):
        @classmethod
        def validate(cls, request_object: Self):
            must_be_present(onboardingId=request_object.onboardingId, order=request_object.order)
            BoardingManager.validate_onboarding_config_body(request_object.onboardingId, request_object.configBody)

    @convertibleclass
    class CreateFullKeyActionConfigRequestObject(CreateKeyActionConfigRequestObject):
        learnArticleId: str = default_field()
        moduleConfigId: str = default_field()

        @classmethod
        def validate(cls, action: KeyActionConfig):
            must_not_be_present(
                id=action.id,
                updateDateTime=action.updateDateTime,
                createDateTime=action.createDateTime,
            )

            must_be_only_one_of(learnArticleId=action.learnArticleId, moduleId=action.moduleId)

            if action.is_for_module():
                must_be_present(moduleId=action.moduleId)
            elif action.is_for_learn():
                must_be_present(learnArticleId=action.learnArticleId)

    keyActions: list[CreateFullKeyActionConfigRequestObject] = default_field()
    moduleConfigs: list[CreateModuleConfigRequestObject] = default_field()
    learn: FullDeploymentLearn = default_field()
    consent: CreateConsentRequestObject = default_field()
    econsent: CreateEConsentRequestObject = default_field()
    onboardingConfigs: list[CreateFullOnboardingConfigRequestObject] = default_field()

    @classmethod
    def validate(cls, deployment):
        must_not_be_present(
            id=deployment.id,
            roles=deployment.roles,
            builder=deployment.builder,
            enrollmentCounter=deployment.enrollmentCounter,
            updateDateTime=deployment.updateDateTime,
            createDateTime=deployment.createDateTime,
        )
        must_be_present(name=deployment.name)

        validate_profile_fields(deployment=deployment)
        validate_default_units_field(deployment)


@convertibleclass
class CreateDraftRequestObject(RequestObject):
    DEPLOYMENT_ID = "deploymentId"

    deploymentId: str = id_field(required=True)


@convertibleclass
class PublishDeploymentRequestBody(RequestObject):
    NAME = "name"

    name: str = default_field(metadata=meta(validate_not_too_long))


@convertibleclass
class PublishDeploymentRequestObject(CreateDraftRequestObject, PublishDeploymentRequestBody):
    SUBMITTER_ID = "submitterId"

    submitterId: str = id_field(required=True)


@convertibleclass
class RollbackRequestBody(RequestObject):
    VERSION = "version"

    version: str = required_field(metadata=meta(validate_draft_version, example="1.5"))


@convertibleclass
class RollbackRequestObject(RollbackRequestBody):
    DEPLOYMENT_ID = "deploymentId"

    deploymentId: str = id_field(required=True)
