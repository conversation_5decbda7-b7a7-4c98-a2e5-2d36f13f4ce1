from copy import copy, deepcopy
from typing import Any

import flask
from flask import url_for
from sdk.deployment.models import Deployment

from sdk.common.adapter.feature_toggle import InMemoryFeatureToggleConfig
from sdk.common.adapter.feature_toggle.feature_toggle_adapter import FeatureToggle
from sdk.common.adapter.feature_toggle.in_memory import InMemoryFeatureToggleAdapter
from sdk.common.exceptions.exceptions import ErrorCodes
from sdk.common.localization import Localization
from sdk.common.utils import inject
from sdk.common.utils.common_functions_utils import find
from sdk.common.utils.validators import utc_str_field_to_val, model_to_dict
from sdk.deployment.dtos.deployment import DeploymentDTO, Profile, ProfileFields
from sdk.deployment.service.deployment_service import LocalizationKeys
from sdk.deployment.tests.IntegrationTests.deployment_router_tests import (
    AbstractDeploymentTestCase,
)
from sdk.deployment.tests.IntegrationTests.test_helpers import (
    get_module_config_by_id,
    get_sample_bmi_module_config,
    get_sample_feedback,
    get_sample_feedback_config,
    get_sample_feedback_config_new,
    get_sample_pam_questionnaire,
    get_sample_peak_flow_module_config,
    get_sample_promis_pain_questionnaire,
    get_sample_questionnaire_with_trademark_text,
    module_config_with_config_body,
    module_config_with_config_body_with_dimensions,
    simple_module_config,
    simple_module_config_not_requiring_default_disclaimer_config,
    simple_module_config_requiring_default_disclaimer_config,
)
from sdk.module_result.dtos.module_config import (
    FeedBacksGroupedByRag,
    FeedbackConfig,
    FeedbackText,
    Footnote,
    ModuleConfig,
)
from sdk.module_result.modules import PeakFlowModule, SymptomV2Module
from sdk.module_result.modules.further_pregnancy_key_action_trigger import (
    AZFurtherPregnancyKeyActionTriggerDTO,
)
from sdk.module_result.modules.group_key_action_trigger import AZGroupKeyActionTriggerDTO
from sdk.module_result.modules.symptom import ComplexSymptomConfigBodyKeys

VALID_MANAGER_ID = "60071f359e7e44330f732037"
VALID_USER_ID = "5e8f0c74b50aa9656c34789c"


class BaseModuleConfigTestCase(AbstractDeploymentTestCase):
    @classmethod
    def setUpClass(cls) -> None:
        super().setUpClass()
        cls.deployment_id = "5d386cc6ff885918d96edb2c"
        cls.create_or_update_url = f"{cls.deployment_route}/deployment/{cls.deployment_id}/module-config"
        cls.validate_dimensions_url = flask.url_for("deployment_route.validate_questionnaire_dimensions")
        cls.createModuleConfigBody = simple_module_config()
        cls.user_config_url = f"/api/extensions/v1/user/{VALID_USER_ID}/configuration"
        default_disclaimer_text_key = LocalizationKeys.DEFAULT_DISCLAIMER_TEXT
        localization = inject.instance(Localization)
        user_localization = localization.get("de")
        cls.default_disclaimer_text = user_localization.get(default_disclaimer_text_key)

    def _create_module_config(self, code: int = 201, body: dict = None):
        url = url_for("deployment_route.create_module_config", deployment_id=self.deployment_id)
        req_body = body or simple_module_config()
        rsp = self.flask_client.post(url, json=req_body, headers=self.headers)
        self.assertEqual(code, rsp.status_code)
        return rsp

    def _update_module_config(self, body: dict) -> dict:
        url = url_for("deployment_route.update_module_config", deployment_id=self.deployment_id)
        rsp = self.flask_client.put(url, json=body, headers=self.headers)
        self.assertEqual(200, rsp.status_code)
        return rsp.json

    def _retrieve_deployment(self):
        url = url_for("deployment_route.retrieve_deployment", deployment_id=self.deployment_id)
        rsp = self.flask_client.get(url, headers=self.headers)
        self.assertEqual(200, rsp.status_code)
        return rsp

    def _assert_failed_module_config_creation_with_feedback(self, feedback_config):
        body = copy(self.createModuleConfigBody)
        body[ModuleConfig.MODULE_ID] = PeakFlowModule.moduleId
        body[ModuleConfig.FEEDBACK] = feedback_config
        rsp = self._create_module_config(code=403, body=body)
        self.assertEqual(ErrorCodes.DATA_VALIDATION_ERROR, rsp.json["code"])


class ModuleConfigTestCase(BaseModuleConfigTestCase):
    def test_module_config_creation(self):
        body = copy(self.createModuleConfigBody)
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

    def test_success_create_module_config_WHEN_dimension_added(self):
        body = module_config_with_config_body_with_dimensions()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code, rsp.json)

    def test_failure_WHEN_dimensions_has_missing_display_settings(self):
        body = module_config_with_config_body_with_dimensions()
        body["configBody"]["dimensions"][0].pop("showToPatient")
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(403, rsp.status_code, rsp.json)

    def test_failure_create_module_config_WHEN_dimensions_are_NOT_correct(self):
        body = module_config_with_config_body_with_dimensions()
        body["configBody"]["dimensions"][0].pop("formula")
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(403, rsp.status_code)

        body = module_config_with_config_body_with_dimensions()
        body["configBody"].pop("questionnaireType")
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(400, rsp.status_code)

        body = module_config_with_config_body_with_dimensions()
        body["configBody"].pop("dimensions")
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(400, rsp.status_code)

    def test_validate_config_body_dimensions(self):
        config_body = module_config_with_config_body_with_dimensions()["configBody"]
        body = {"configBody": config_body}

        rsp = self._validate_dimensions(body)
        self.assertEqual(200, rsp.status_code)

        config_body["dimensions"][0]["formula"] = "100 * (q2 + q3) // 4 / 29"

        rsp = self._validate_dimensions(body)
        self.assertEqual(400, rsp.status_code)

    def _validate_dimensions(self, body):
        return self.flask_client.post(self.validate_dimensions_url, json=body, headers=self.headers)

    def test_failure_create_module_config_WHEN_formula_is_NOT_correct(self):
        body = module_config_with_config_body_with_dimensions()
        body["configBody"]["dimensions"][0]["formula"] = "q3 - q1 ++ q2"
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(400, rsp.status_code)

    def test_create_symptom_v2__scale_dt_should_be_added(self):
        self._create_update_symptom_v2_configs()
        complex_symptoms = self._retrieve_symptom_from_configs()

        for symptom in complex_symptoms:
            self.assertIsNotNone(symptom["scaleUpdateDateTime"])

        headers = self.get_headers_for_token(VALID_USER_ID)
        rsp = self.flask_client.get(
            f"/api/extensions/v1/user/{VALID_USER_ID}/configuration",
            headers=headers,
        )
        self.assertEqual(200, rsp.status_code)

    def test_update_one_symptom__scale_should_be_updated_only_in_one_symptom(self):
        config_id = self._create_update_symptom_v2_configs()
        complex_symptoms = self._retrieve_symptom_from_configs()

        unchanged_symptom_dt_before_update = complex_symptoms[1]["scaleUpdateDateTime"]
        changed_symptom_dt_before_update = complex_symptoms[0]["scaleUpdateDateTime"]

        self._create_update_symptom_v2_configs(
            scale=[
                {"severity": 1, "value": "aaa"},
                {"severity": 3, "value": "bbb"},
                {"severity": 2, "value": "ccc"},
            ],
            id_=config_id,
        )
        complex_symptoms = self._retrieve_symptom_from_configs()
        unchanged_symptom_dt_after_update = complex_symptoms[1]["scaleUpdateDateTime"]
        changed_symptom_dt_after_update = complex_symptoms[0]["scaleUpdateDateTime"]
        self.assertEqual(unchanged_symptom_dt_before_update, unchanged_symptom_dt_after_update)
        self.assertNotEqual(changed_symptom_dt_before_update, changed_symptom_dt_after_update)

    def test_update_symptom_v2__scale_should_be_updated(self):
        config_id = self._create_update_symptom_v2_configs()
        complex_symptoms_before_update = self._retrieve_symptom_from_configs()
        self._create_update_symptom_v2_configs(
            scale=[
                {"severity": 1, "value": "aaa"},
                {"severity": 3, "value": "bbb"},
                {"severity": 2, "value": "ccc"},
            ],
            id_=config_id,
        )

        complex_symptoms_after_update = self._retrieve_symptom_from_configs()
        self.assertNotEqual(complex_symptoms_after_update, complex_symptoms_before_update)

    def test_update_symptom_multiple_symptoms_configs_in_same_deployment(self):
        sample_configs = [
            {"severity": 1, "value": "a1"},
            {"severity": 3, "value": "b1"},
        ]
        self._create_update_symptom_v2_configs(scale=sample_configs)
        config_id = self._create_update_symptom_v2_configs(scale=sample_configs)
        complex_symptoms_before_update = self._retrieve_symptom_from_configs()

        for symptom in complex_symptoms_before_update:
            self.assertIsNotNone(symptom[ComplexSymptomConfigBodyKeys.SCALE_UPDATE_DT])

        self._create_update_symptom_v2_configs(
            scale=[
                {"severity": 1, "value": "b2"},
                {"severity": 3, "value": "a2"},
            ],
            id_=config_id,
        )

        complex_symptoms_after_update = self._retrieve_symptom_from_configs(config_id=config_id)
        self.assertNotEqual(complex_symptoms_after_update, complex_symptoms_before_update)

        self._create_update_symptom_v2_configs(
            scale=[
                {"severity": 1, "value": "b2"},
                {"severity": 3, "value": "a2"},
            ],
            id_=config_id,
        )

        complex_symptoms_after_second_update = self._retrieve_symptom_from_configs(config_id=config_id)
        self.assertEqual(complex_symptoms_after_update, complex_symptoms_after_second_update)

    def _retrieve_symptom_from_configs(self, deployment_id: str = "5d386cc6ff885918d96edb2c", config_id: str = None):
        deployment = model_to_dict(Deployment.objects.filter(mongoId=deployment_id).first())
        module_configs = deployment[DeploymentDTO.MODULE_CONFIGS]
        if not config_id:
            symptom_module = [m for m in module_configs if m[ModuleConfig.MODULE_ID] == "SymptomV2"][0]
        else:
            symptom_module = [m for m in module_configs if m[ModuleConfig.ID] == config_id][0]
        return symptom_module[ModuleConfig.CONFIG_BODY]["complexSymptoms"]

    def _create_update_symptom_v2_configs(self, scale: list[dict] = None, id_: str = None) -> str:
        if not scale:
            scale = [
                {"severity": 1, "value": "Mild"},
                {"severity": 2, "value": "Moderate"},
                {"severity": 3, "value": "Severe"},
            ]
        body = {
            "moduleId": "SymptomV2",
            "moduleName": "",
            "status": "ENABLED",
            "order": 2,
            "configBody": {
                "complexSymptoms": [
                    {"name": "Symptom1", "scale": scale},
                    {"name": "Symptom2", "scale": [{"severity": 1, "value": "Mild"}]},
                ],
                "followUpQuestionIds": ["d23deeb0-a72e-4ab0-bba0-e52bc6fb166a"],
                "pages": [
                    {
                        "type": "QUESTION",
                        "id": "d23deeb0-a72e-4ab0-bba0-e52bc6fb166a",
                        "order": 1,
                        "text": "Some hint?",
                        "description": "Explain more here?",
                        "items": [
                            {
                                "id": "d23deeb0-a72e-4ab0-bba0-e52bc6fb166a",
                                "format": "TEXT",
                                "required": True,
                                "order": 1,
                                "text": "Text Question",
                                "description": "Text Question Description?",
                                "regex": "[0-9]{8}",
                                "defaultValue": "some text",
                                "placeholder": "enter your phone here",
                            }
                        ],
                    }
                ],
            },
            "ragThresholds": [
                {
                    "type": "VALUE",
                    "severity": 1,
                    "thresholdRange": [{"minValue": 1.0, "maxValue": 1.0}],
                    "color": "#CBEBF0",
                    "fieldName": "Persistent cough",
                    "enabled": True,
                }
            ],
            "keyActions": [],
        }
        if id_:
            body["id"] = id_
            rsp = self.flask_client.put(self.create_or_update_url, json=body, headers=self.headers)
            self.assertEqual(200, rsp.status_code)
            return rsp.json["id"]

        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)
        return rsp.json["id"]

    def test_module_config_creation_eq5(self):
        body = {
            "moduleId": "EQ5D5L",
            "moduleName": "hu_eq5d5l_modulename",
            "ragThresholds": [],
            "shortModuleName": "EQ5D",
            "schedule": {
                "friendlyText": "AS NEEDED",
                "timesOfDay": [],
                "timesPerDuration": 0,
            },
            "notificationData": {
                "title": "hu_eq5d5l_notification_title",
                "body": "hu_eq5d5l_notification_body",
            },
            "status": "ENABLED",
            "about": "hu_eq5d5l_about",
            "order": 4,
            "configBody": {
                "toggleIndexValue": True,
                "id": "hu_eq5d5l_configbody",
                "isForManager": False,
                "publisherName": "MZ",
                "questionnaireName": "hu_eq5d5l_moduleName",
                "name": "EQ-5D-5L",
                "questionnaireId": "eq5d5l_module",
                "trademarkText": "hu_eq5d5l_trademark",
                "region": "UK",
                "isHorizontalFlow": True,
            },
        }
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

    def test_height_mandatory_after_bmi_module_config_creation(self):
        body = get_sample_bmi_module_config()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)
        deployment = self.get_deployment()
        profile = deployment.get(DeploymentDTO.PROFILE)
        self.assertIsNotNone(profile)
        profile_fields = profile.get(Profile.FIELDS)
        self.assertIsNotNone(profile_fields)
        self._assert_profile_field_is_mandatory(ProfileFields.HEIGHT, profile_fields)

    def test_dob_gender_height_mandatory_after_peak_flow_module_config_creation(self):
        body = get_sample_peak_flow_module_config()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)
        deployment = self.get_deployment()
        profile = deployment.get(DeploymentDTO.PROFILE)
        self.assertIsNotNone(profile)
        profile_fields = profile.get(Profile.FIELDS)
        self.assertIsNotNone(profile_fields)
        self._assert_profile_field_is_mandatory(ProfileFields.HEIGHT, profile_fields)
        self._assert_profile_field_is_mandatory(ProfileFields.GENDER, profile_fields)
        self._assert_profile_field_is_mandatory(ProfileFields.DATE_OF_BIRTH, profile_fields)

    def test_success_group_trigger_module_config_creation(self):
        body = copy(self.createModuleConfigBody)
        body[ModuleConfig.MODULE_ID] = AZGroupKeyActionTriggerDTO.get_primitive_name()
        body[ModuleConfig.CONFIG_BODY] = {
            "keyActions": {
                "PREGNANT": ["InfantFollowUp"],
                "BREAST_FEEDING": ["PregnancyUpdate"],
                "BOTH_P_AND_B": ["InfantFollowUp"],
                "FEMALE_LESS_50_NOT_P_OR_B": ["PregnancyUpdate"],
                "MALE_OR_FEMALE_OVER_50": ["AdditionalQoL"],
            }
        }
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

    def test_failure_trigger_module_config_creation(self):
        body = copy(self.createModuleConfigBody)
        body[ModuleConfig.MODULE_ID] = AZGroupKeyActionTriggerDTO.get_primitive_name()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(400, rsp.status_code, rsp.json)

    def test_failure_trigger_module_config_creation_invalid_config_body(self):
        body = copy(self.createModuleConfigBody)
        modules = [
            AZGroupKeyActionTriggerDTO.get_primitive_name(),
            AZFurtherPregnancyKeyActionTriggerDTO.get_primitive_name(),
        ]

        for module_id in modules:
            body[ModuleConfig.MODULE_ID] = module_id
            body[ModuleConfig.CONFIG_BODY] = None
            rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
            self.assertEqual(400, rsp.status_code, rsp.json)

    def test_failure_trigger_module_config_creation_missing_keys(self):
        body = copy(self.createModuleConfigBody)
        body[ModuleConfig.MODULE_ID] = AZGroupKeyActionTriggerDTO.get_primitive_name()
        body[ModuleConfig.CONFIG_BODY] = {"keyActions": {}}
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(400, rsp.status_code)

    def test_success_pregnancy_trigger_module_config_creation(self):
        body = copy(self.createModuleConfigBody)
        body[ModuleConfig.MODULE_ID] = AZFurtherPregnancyKeyActionTriggerDTO.get_primitive_name()
        body[ModuleConfig.CONFIG_BODY] = {"keyActions": {"PREGNANT": ["InfantFollowUp"], "NOT_PREGNANT": []}}
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

    def test_failure_pregnancy_trigger_module_config_creation(self):
        body = copy(self.createModuleConfigBody)
        body[ModuleConfig.MODULE_ID] = AZFurtherPregnancyKeyActionTriggerDTO.get_primitive_name()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(400, rsp.status_code)

    def test_failure_pregnancy_trigger_module_config_creation_missing_keys(self):
        body = copy(self.createModuleConfigBody)
        body[ModuleConfig.CONFIG_BODY] = {"keyActions": {}}
        body[ModuleConfig.MODULE_ID] = AZFurtherPregnancyKeyActionTriggerDTO.get_primitive_name()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(400, rsp.status_code)

    def test_module_config_update(self):
        body = copy(self.createModuleConfigBody)
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)
        module_config_id = rsp.json["id"]

        deployment = self.get_deployment()
        old_config = get_module_config_by_id(deployment, module_config_id)

        body.update({"about": "New test about.", "id": module_config_id})
        rsp = self.flask_client.put(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(200, rsp.status_code)

        deployment = self.get_deployment()
        new_config = get_module_config_by_id(deployment, module_config_id)
        self.assertNotEqual(new_config["about"], old_config["about"])
        old_update_datetime = utc_str_field_to_val(old_config["updateDateTime"])
        new_update_datetime = utc_str_field_to_val(new_config["updateDateTime"])
        self.assertGreater(new_update_datetime, old_update_datetime)

        body.update({"about": "Newer test about .", "id": module_config_id})
        rsp = self.flask_client.put(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(200, rsp.status_code)

        deployment = self.get_deployment()
        new_config = get_module_config_by_id(deployment, module_config_id)
        self.assertNotEqual(new_config["about"], old_config["about"])

        old_update_datetime = utc_str_field_to_val(old_config["updateDateTime"])
        new_update_datetime = utc_str_field_to_val(new_config["updateDateTime"])
        self.assertGreater(new_update_datetime, old_update_datetime)

    def test_create_module_config_with_config_body(self):
        body = module_config_with_config_body()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

    def test_create_and_update_module_config_with_export_short_code(self):
        body = module_config_with_config_body()
        body["configBody"]["pages"][0]["items"][0]["exportShortCode"] = "test"

        # 1. Creation
        created_rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, created_rsp.status_code)
        # 2. Failed duplicate creation
        failed_create_rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(403, failed_create_rsp.status_code)
        # 3. Failed duplicate update
        module_config_id = created_rsp.json[ModuleConfig.ID]
        body[ModuleConfig.ID] = module_config_id
        failed_update_rsp = self.flask_client.put(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(403, failed_update_rsp.status_code)
        # 4. Updated with new unique value
        body["configBody"]["pages"][0]["items"][0]["exportShortCode"] = "test2"
        updated_rsp = self.flask_client.put(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(200, updated_rsp.status_code)

    def test_create_module_config_with_invalid_option_value(self):
        invalid_option = {"label": "None", "value": 1, "weight": 1}
        body = module_config_with_config_body(invalid_option)
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(403, rsp.status_code)

    def test_create_module_config_with_no_option(self):
        body = module_config_with_config_body()
        body["configBody"]["pages"][0]["items"][0]["options"] = None
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(403, rsp.status_code)
        body["configBody"]["pages"][0]["items"][0]["options"] = []
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(403, rsp.status_code)

    def test_create_and_update_module_config_with_with_no_option(self):
        body = module_config_with_config_body()

        # 1. Creation
        created_rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, created_rsp.status_code)

        # 2. Updated with options as None
        body["configBody"]["pages"][0]["items"][0]["options"] = None
        updated_rsp = self.flask_client.put(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(403, updated_rsp.status_code)
        # 3. Updated with options as an empty list
        body["configBody"]["pages"][0]["items"][0]["options"] = []
        updated_rsp = self.flask_client.put(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(403, updated_rsp.status_code)

    def test_module_config_creation_with_date(self):
        body = copy(self.createModuleConfigBody)
        body["createDateTime"] = "2020-04-07T17:05:51"
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(403, rsp.status_code)
        self.assertEqual(ErrorCodes.DATA_VALIDATION_ERROR, rsp.json["code"])

    def test_module_config_creation_for_not_existing_deployment(self):
        body = copy(self.createModuleConfigBody)
        deployment_id = "5d386cc6ff885918d96edb5c"
        create_or_update_url = f"{self.deployment_route}/deployment/{deployment_id}/module-config"
        rsp = self.flask_client.post(create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(404, rsp.status_code)
        self.assertEqual(300012, rsp.json["code"])

    def test_module_config_retrieve_and_change_footnote(self):
        body = module_config_with_config_body()

        # 1. Creation
        created_rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, created_rsp.status_code)

        # 2. Updated with enabled changed to false
        body["footnote"]["enabled"] = True
        updated_rsp = self.flask_client.put(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(200, updated_rsp.status_code)

        # 3. Retrieve
        rsp = self.flask_client.get(
            f"{self.deployment_route}/deployment/{self.deployment_id}",
            headers=self.headers,
        )
        self.assertEqual(200, rsp.status_code)
        for mc in rsp.json["moduleConfigs"]:
            if mc["moduleId"] == body["moduleId"]:
                result_footnote = mc["footnote"]
        self.assertEqual(True, result_footnote["enabled"])

    def test_module_config_apply_default_disclaimer_config_when_footnote_is_missing(
        self,
    ):
        body = simple_module_config_requiring_default_disclaimer_config()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

        rsp = self.flask_client.get(
            self.user_config_url,
            headers=self.get_headers_for_token(VALID_USER_ID),
        )
        self.assertEqual(200, rsp.status_code)
        for mc in rsp.json["moduleConfigs"]:
            if mc.get("moduleId") == body.get("moduleId"):
                module_footnote = mc.get("footnote")
        self.assertEqual(True, module_footnote.get("enabled"))
        self.assertEqual(self.default_disclaimer_text, module_footnote.get(Footnote.TEXT))

    def test_module_config_skip_applying_default_disclaimer_config_for_excluded_modules(
        self,
    ):
        body = simple_module_config_not_requiring_default_disclaimer_config()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

        rsp = self.flask_client.get(
            self.user_config_url,
            headers=self.get_headers_for_token(VALID_USER_ID),
        )
        self.assertEqual(200, rsp.status_code)
        for mc in rsp.json.get("moduleConfigs"):
            if mc.get("moduleId") == body.get("moduleId"):
                module_footnote = mc.get("footnote")
        self.assertIsNone(module_footnote)

    def test_module_config_skip_applying_default_disclaimer_config_when_trademark_text_is_available(
        self,
    ):
        body = get_sample_questionnaire_with_trademark_text()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

        rsp = self.flask_client.get(
            self.user_config_url,
            headers=self.get_headers_for_token(VALID_USER_ID),
        )
        self.assertEqual(200, rsp.status_code)
        for mc in rsp.json.get("moduleConfigs"):
            if mc.get("moduleId") == body.get("moduleId"):
                module_footnote = mc.get("footnote")
        self.assertIsNone(module_footnote)

    def test_module_config_skip_applying_default_disclaimer_config_for_excluded_questionnaire_types(
        self,
    ):
        body = get_sample_promis_pain_questionnaire()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

        rsp = self.flask_client.get(
            self.user_config_url,
            headers=self.get_headers_for_token(VALID_USER_ID),
        )
        self.assertEqual(200, rsp.status_code)
        for mc in rsp.json.get("moduleConfigs"):
            if mc.get("moduleId") == body.get("moduleId"):
                module_footnote = mc.get("footnote")
        self.assertIsNone(module_footnote)

    def test_module_config_skip_applying_default_disclaimer_config_to_pam_questionnaires(
        self,
    ):
        body = get_sample_pam_questionnaire()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

        rsp = self.flask_client.get(
            self.user_config_url,
            headers=self.get_headers_for_token(VALID_USER_ID),
        )
        self.assertEqual(200, rsp.status_code)
        for mc in rsp.json.get("moduleConfigs"):
            if mc.get("moduleId") == body.get("moduleId"):
                module_footnote = mc.get("footnote")
        self.assertIsNone(module_footnote)

    def test_module_config_apply_default_disclaimer_config_when_footnote_text_is_missing(
        self,
    ):
        body = simple_module_config_requiring_default_disclaimer_config()
        body[ModuleConfig.FOOTNOTE] = {Footnote.ENABLED: True}
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

        rsp = self.flask_client.get(
            self.user_config_url,
            headers=self.get_headers_for_token(VALID_USER_ID),
        )
        self.assertEqual(200, rsp.status_code)
        for mc in rsp.json.get("moduleConfigs"):
            if mc.get("moduleId") == body.get("moduleId"):
                module_footnote = mc.get("footnote")
        self.assertEqual(True, module_footnote.get("enabled"))
        self.assertEqual(self.default_disclaimer_text, module_footnote.get(Footnote.TEXT))

    def test_module_config_apply_default_disclaimer_config_when_footnote_text_is_empty(
        self,
    ):
        body = simple_module_config_requiring_default_disclaimer_config()
        body[ModuleConfig.FOOTNOTE] = {Footnote.ENABLED: True, Footnote.TEXT: ""}
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

        rsp = self.flask_client.get(
            self.user_config_url,
            headers=self.get_headers_for_token(VALID_USER_ID),
        )
        self.assertEqual(200, rsp.status_code)
        for mc in rsp.json.get("moduleConfigs"):
            if mc.get("moduleId") == body.get("moduleId"):
                module_footnote = mc.get("footnote")
        self.assertEqual(True, module_footnote.get("enabled"))
        self.assertEqual(self.default_disclaimer_text, module_footnote.get(Footnote.TEXT))

    def test_module_config_skip_applying_default_disclaimer_config_when_footnote_is_disabled(
        self,
    ):
        body = simple_module_config_requiring_default_disclaimer_config()
        footnote_text = "sample footnote text"
        body[ModuleConfig.FOOTNOTE] = {
            Footnote.ENABLED: False,
            Footnote.TEXT: footnote_text,
        }
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

        rsp = self.flask_client.get(
            self.user_config_url,
            headers=self.get_headers_for_token(VALID_USER_ID),
        )
        self.assertEqual(200, rsp.status_code)
        for mc in rsp.json.get("moduleConfigs"):
            if mc.get("moduleId") == body.get("moduleId"):
                module_footnote = mc.get("footnote")
        self.assertFalse(module_footnote.get(Footnote.ENABLED))
        self.assertEqual(footnote_text, module_footnote.get(Footnote.TEXT))

    def test_modules_retrieve(self):
        rsp = self.flask_client.get(f"{self.deployment_route}/deployment/modules", headers=self.headers)
        self.assertEqual(200, rsp.status_code)
        self.assertNotEqual(0, len(rsp.json))

    def test_module_config_delete(self):
        deployment = self.get_deployment()
        old_key_actions_count = len(deployment["keyActions"])

        url = f"{self.deployment_route}/deployment/{self.deployment_id}/module-config/5e94b2007773091c9a592650"
        rsp = self.flask_client.delete(url, headers=self.headers)
        self.assertEqual(204, rsp.status_code)

        deployment = self.get_deployment()
        new_key_action_count = len(deployment["keyActions"])
        self.assertLess(new_key_action_count, old_key_actions_count)

    def test_deployment_revision_after_module_config_creation(self):
        rsp = self.flask_client.get(
            f"{self.deployment_route}/deployment/{self.deployment_id}",
            headers=self.headers,
        )
        self.assertEqual(200, rsp.status_code)
        old_version = rsp.json["version"]
        body = copy(self.createModuleConfigBody)

        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)
        rsp = self.flask_client.get(
            f"{self.deployment_route}/deployment/{self.deployment_id}",
            headers=self.headers,
        )
        self.assertEqual(200, rsp.status_code)
        new_version = rsp.json["version"]
        self.assertEqual(old_version + 1, new_version)

    def test_deployment_revision_after_module_config_update(self):
        rsp = self.flask_client.get(
            f"{self.deployment_route}/deployment/{self.deployment_id}",
            headers=self.headers,
        )
        self.assertEqual(200, rsp.status_code)
        old_version = rsp.json["version"]

        body = copy(self.createModuleConfigBody)
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)
        rsp = self.flask_client.get(
            f"{self.deployment_route}/deployment/{self.deployment_id}",
            headers=self.headers,
        )
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(old_version + 1, rsp.json["version"])
        old_version = rsp.json["version"]

        body[ModuleConfig.ID] = "5e94b2007773091c9a592650"
        rsp = self.flask_client.put(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(200, rsp.status_code)
        rsp = self.flask_client.get(
            f"{self.deployment_route}/deployment/{self.deployment_id}",
            headers=self.headers,
        )
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(old_version + 1, rsp.json["version"])

    def _assert_profile_field_is_mandatory(self, field: str, profile_fields: dict[str, Any]):
        self.assertTrue(profile_fields.get(field))
        self.assertIn(
            field,
            profile_fields.get(ProfileFields.MANDATORY_ONBOARDING_FIELDS),
        )

    def test_success_create_module_config_with_feedback(self):
        body = copy(self.createModuleConfigBody)
        body[ModuleConfig.MODULE_ID] = PeakFlowModule.moduleId
        body[ModuleConfig.FEEDBACK] = get_sample_feedback_config()
        self._create_module_config(body=body)

        rsp = self._retrieve_deployment()
        peakflow_config = find(
            lambda mc: mc.get("moduleId") == PeakFlowModule.moduleId,
            rsp.json["moduleConfigs"],
        )
        feedback_config = peakflow_config.get("feedback")
        self.assertIsNotNone(feedback_config)
        self.assertEqual(feedback_config, get_sample_feedback_config())

    def test_failed_create_mc_with_invalid_feedback_config(self):
        feedback_config = get_sample_feedback_config()
        feedback_config.pop(FeedbackConfig.ENABLED)
        self._assert_failed_module_config_creation_with_feedback(feedback_config)

        feedback_config[FeedbackConfig.UPPERBOUND] = "12"
        self._assert_failed_module_config_creation_with_feedback(feedback_config)

    def test_failed_create_mc_with_invalid_feedback_rag(self):
        feedback_config = get_sample_feedback_config()
        feedback = get_sample_feedback()
        feedback[0][FeedBacksGroupedByRag.RAG] = ["BLUE"]
        feedback_config[FeedbackConfig.FEEDBACKS] = feedback
        self._assert_failed_module_config_creation_with_feedback(feedback_config)

    def test_failed_create_mc_with_invalid_feedback_text(self):
        feedback_config = get_sample_feedback_config()
        feedback = get_sample_feedback()
        feedback[0][FeedBacksGroupedByRag.TEXTS][0][FeedbackText.TEXT] = ""
        feedback_config[FeedbackConfig.FEEDBACKS] = feedback
        self._assert_failed_module_config_creation_with_feedback(feedback_config)

        feedback[0][FeedBacksGroupedByRag.TEXTS][0][FeedbackText.ID] = "invalid_id"
        feedback_config[FeedbackConfig.FEEDBACKS] = feedback
        self._assert_failed_module_config_creation_with_feedback(feedback_config)

    def test_handle_new_feedback_configs_gracefully(self):
        body = copy(self.createModuleConfigBody)
        body[ModuleConfig.FEEDBACK] = get_sample_feedback_config_new()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

        # we should be able to work with deployments even when they contain new feedback configuration
        self._retrieve_deployment()

    def test_success_update_symptom_v2_module_config(self):
        mc_id = self._create_update_symptom_v2_configs()
        rsp = self._retrieve_deployment()
        symptom_v2_mc = find(lambda mc: mc["id"] == mc_id, rsp.json["moduleConfigs"])
        self.assertEqual(symptom_v2_mc[ModuleConfig.STATUS], "ENABLED")

        body = {
            ModuleConfig.CONFIG_BODY: {"complexSymptoms": []},
            ModuleConfig.ID: mc_id,
            ModuleConfig.MODULE_ID: SymptomV2Module.moduleId,
            ModuleConfig.STATUS: "ENABLED",
        }
        self._update_module_config(body)
        rsp = self._retrieve_deployment()
        symptom_v2_mc = find(lambda mc: mc["id"] == mc_id, rsp.json["moduleConfigs"])
        self.assertEqual(symptom_v2_mc[ModuleConfig.CONFIG_BODY], body[ModuleConfig.CONFIG_BODY])
        self.assertEqual(symptom_v2_mc[ModuleConfig.STATUS], "DISABLED")


class NewFeedbackConfigTestCase(BaseModuleConfigTestCase):
    @classmethod
    def setUpClass(cls) -> None:
        super().setUpClass()
        feature_config = InMemoryFeatureToggleConfig(
            keys={
                # "hu-cms-recommendations": True, # No CMS test in SDK
                "module-feedback": True,
                # "hu-cms-questionnaires": True, # No CMS test in SDK
                # "cms_questionnaire": True, # No CMS test in SDK
            }
        )
        inject.get_injector().rebind(
            lambda binder: binder.bind(FeatureToggle, InMemoryFeatureToggleAdapter(feature_config))
        )

    def test_success_create_module_config_with_feedback_new(self):
        body = deepcopy(self.createModuleConfigBody)
        body[ModuleConfig.MODULE_ID] = PeakFlowModule.moduleId
        body[ModuleConfig.FEEDBACK] = get_sample_feedback_config_new()
        self._create_module_config(body=body)

        rsp = self._retrieve_deployment()
        peakflow_config = find(
            lambda mc: mc.get("moduleId") == PeakFlowModule.moduleId,
            rsp.json["moduleConfigs"],
        )
        feedback_config = peakflow_config.get("feedback")
        self.assertIsNotNone(feedback_config)
        self.assertEqual(feedback_config, get_sample_feedback_config_new())

    def test_failed_create_mc_with_invalid_feedback_config_new(self):
        feedback_config = get_sample_feedback_config_new()
        feedback_config.pop(FeedbackConfig.ENABLED)
        self._assert_failed_module_config_creation_with_feedback(feedback_config)

    def test_failed_create_mc_with_two_search_parameters_new(self):
        feedback_config = get_sample_feedback_config_new()
        feedback_config[FeedbackConfig.CMS_CONTENT_IDS] = ["content_id"]
        self._assert_failed_module_config_creation_with_feedback(feedback_config)

    # ToDo: Move to test for CMS component in huma-server-plugins repo
    # def test_cms_questionnaire_module_config_update(self):
    #     rsp = self.flask_client.get(
    #         f"{self.deployment_route}/deployment/{self.deployment_id}",
    #         headers=self.headers,
    #     )
    #     self.assertEqual(200, rsp.status_code)
    #     old_version = rsp.json["version"]
    #
    #     body = sample_cms_questionnaire_module_config()
    #     rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
    #     self.assertEqual(201, rsp.status_code)
    #
    #     module_config_id = rsp.json["id"]
    #
    #     body.update({"about": "New test about.", "id": module_config_id})
    #     rsp = self.flask_client.put(self.create_or_update_url, json=body, headers=self.headers)
    #     self.assertEqual(200, rsp.status_code)
    #
    #     body.update(
    #         {"configBody": sample_questionnaire_config_body, "about": "New test about.", "id": module_config_id}
    #     )
    #     rsp = self.flask_client.put(self.create_or_update_url, json=body, headers=self.headers)
    #     self.assertEqual(200, rsp.status_code)
    #
    #     rsp = self.flask_client.get(
    #         f"{self.deployment_route}/deployment/{self.deployment_id}",
    #         headers=self.headers,
    #     )
    #     self.assertEqual(200, rsp.status_code)
    #     new_version = rsp.json["version"]
    #
    #     self.assertEqual(old_version + 3, new_version)

    def test_handle_old_feedback_configs_gracefully(self):
        body = copy(self.createModuleConfigBody)
        body[ModuleConfig.FEEDBACK] = get_sample_feedback_config()
        rsp = self.flask_client.post(self.create_or_update_url, json=body, headers=self.headers)
        self.assertEqual(201, rsp.status_code)

        # we should be able to work with deployments even when they contain new feedback configuration
        self._retrieve_deployment()
