import io
from pathlib import Path

from flask import url_for
from jsonpatch import make_patch

from sdk.common.localization.utils import Language
from sdk.deployment.dtos.deployment import DeploymentDTO, Reason
from sdk.deployment.router.deployment_requests import RetrieveLocalizationsRequestObject
from sdk.deployment.tests.IntegrationTests.deployment_router_tests import (
    AbstractDeploymentTestCase,
)
from sdk.storage.component import StorageComponentV1

USER_ID = "5e8f0c74b50aa9656c34789c"
SAMPLE_LOCALIZATIONS = {
    "en": {
        "hu_deployment_name": "deployment 1",
        "hu_consent_sections_0_details": "The following screens explain how Medopad works, the data it collects and privacy.",
        "hu_consent_sections_0_reviewDetails": "Medopad helps to share",
        "hu_BloodPressure_moduleName": "BloodPressure Module",
        "hu_BloodPressure_schedule_friendlyText": "Please input your weight.",
        "hu_BloodPressure_shortModuleName": "BM",
    },
    "de": {
        "hu_deployment_name": "Bereitstellung 1",
        "hu_consent_sections_0_details": "In den folgenden Bildschirmen werden die Funktionsweise von Medopad, die gesammelten Daten und der Datenschutz erläutert.",
        "hu_consent_sections_0_reviewDetails": "Medopad hilft beim Teilen",
        "hu_BloodPressure_moduleName": "BloodPressure-Modul",
        "hu_BloodPressure_schedule_friendlyText": "Bitte geben Sie Ihr Gewicht ein.",
        "hu_BloodPressure_shortModuleName": "BM DE",
    },
}


LOCALIZATION_RESULT = {
    "deploymentId": "5d386cc6ff885918d96edb2c",
    "name": "Deployment1",
    "description": "Deployment Description",
    "status": "DEPLOYED",
    "color": "0x007AFF",
    "code": "Deployment code",
    "moduleConfigs": [
        {
            "moduleName": "BloodPressure ModuleName",
            "shortModuleName": "BloodPressure shortModuleName",
            "schedule": {"friendlyText": "BloodPressure schedule friendly text"},
        }
    ],
    "learn": {
        "sections": [
            {
                "title": "Test section",
                "articles": [
                    {
                        "title": "Article 0",
                        "content": {"textDetails": "Here what you read"},
                    }
                ],
            }
        ]
    },
    "keyActions": [
        {
            "title": "PAM Questionnaire",
            "description": "You have a new activity for the DeTAP study. Please complete as soon as you are able to.",
        }
    ],
    "extraCustomFields": {
        "mediclinicNumber": {
            "errorMessage": "Insurance Number is incorrect",
            "onboardingCollectionText": "Please enter mediclinic number",
            "profileCollectionText": "Patient Unique ID",
        }
    },
    "features": {
        "messaging": {
            "messages": [
                "Great job! Keep up the good work.",
                "You're doing great!",
                "Awesome – you're right on track with your targets.",
                "Excellent work",
                "You're close to completing your tasks, keep going.",
            ]
        }
    },
    "stats": {"completedCount": {"unit": "state unit"}},
    "icon": {"key": "deployment/5d386cc6ff885918d96edb2c/sample.png"},
    "updateDateTime": "2020-04-09T11:53:37.042000Z",
    "roles": [{"name": "Custom Role", "permissions": ["VIEW_PATIENT_IDENTIFIER"]}],
    "localizations": {"en": {"hu_BloodPressure_moduleName": "BloodPressure ModuleName"}},
}


class LocalizationsTestCase(AbstractDeploymentTestCase):
    user_route = "/api/extensions/v1/user"

    def _update_localizations(self, body=None):
        if body is None:
            body = SAMPLE_LOCALIZATIONS
        rsp = self.flask_client.post(
            url_for("deployment_route.create_localization", deployment_id=self.deployment_id),
            json=body,
            headers=self.headers,
        )
        self.assertEqual(201, rsp.status_code)

    def test_success_deployment_update_dt_gets_updated(self):
        deployment = self.get_deployment()
        old_update_date = deployment[DeploymentDTO.UPDATE_DATE_TIME]

        self._update_localizations()

        deployment = self.get_deployment()
        self.assertLess(old_update_date, deployment[DeploymentDTO.UPDATE_DATE_TIME])

    def test_success_retrieve_user_language_specified_configuration(self):
        body = SAMPLE_LOCALIZATIONS
        self._update_localizations(body)
        headers_user = self.get_headers_for_token(USER_ID)
        rsp = self.flask_client.get(f"{self.user_route}/{USER_ID}/configuration", headers=headers_user)
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(body["de"]["hu_deployment_name"], rsp.json["name"])
        self.assertEqual(
            body["de"]["hu_consent_sections_0_details"],
            rsp.json["consent"]["sections"][0]["details"],
        )
        self.assertEqual(
            body["de"]["hu_consent_sections_0_reviewDetails"],
            rsp.json["consent"]["sections"][0]["reviewDetails"],
        )
        self.assertEqual(
            body["de"]["hu_BloodPressure_moduleName"],
            rsp.json["moduleConfigs"][0]["moduleName"],
        )
        self.assertEqual(
            body["de"]["hu_BloodPressure_schedule_friendlyText"],
            rsp.json["moduleConfigs"][0]["schedule"]["friendlyText"],
        )
        self.assertEqual(
            body["de"]["hu_BloodPressure_shortModuleName"],
            rsp.json["moduleConfigs"][0]["shortModuleName"],
        )

    def test_success_retrieve_default_language_specified_configuration(self):
        body = SAMPLE_LOCALIZATIONS
        self._update_localizations(body)
        headers_user = self.get_headers_for_token(USER_ID)
        headers_user["x-hu-locale"] = "fr"
        rsp = self.flask_client.get(f"{self.user_route}/{USER_ID}/configuration", headers=headers_user)
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(body["en"]["hu_deployment_name"], rsp.json["name"])
        self.assertEqual(
            body["en"]["hu_consent_sections_0_details"],
            rsp.json["consent"]["sections"][0]["details"],
        )
        self.assertEqual(
            body["en"]["hu_consent_sections_0_reviewDetails"],
            rsp.json["consent"]["sections"][0]["reviewDetails"],
        )
        self.assertEqual(
            body["en"]["hu_BloodPressure_moduleName"],
            rsp.json["moduleConfigs"][0]["moduleName"],
        )
        self.assertEqual(
            body["en"]["hu_BloodPressure_schedule_friendlyText"],
            rsp.json["moduleConfigs"][0]["schedule"]["friendlyText"],
        )
        self.assertEqual(
            body["en"]["hu_BloodPressure_shortModuleName"],
            rsp.json["moduleConfigs"][0]["shortModuleName"],
        )

    def test_success_retrieve_default_language_specified_configuration_with_invalid_lang(
        self,
    ):
        body = SAMPLE_LOCALIZATIONS
        self._update_localizations(body)
        headers_user = self.get_headers_for_token(USER_ID)
        headers_user["x-hu-locale"] = "invalid-lang"
        rsp = self.flask_client.get(f"{self.user_route}/{USER_ID}/configuration", headers=headers_user)
        self.assertEqual(200, rsp.status_code)
        self.assertEqual(body["en"]["hu_deployment_name"], rsp.json["name"])

    def test_offboarding_reason_localization_configuration(self):
        headers = self.get_headers_for_token(USER_ID)
        headers["x-hu-locale"] = Language.DE_DE
        rsp = self.flask_client.get(f"{self.user_route}/{USER_ID}/configuration", headers=headers)
        self.assertEqual(200, rsp.status_code)
        reasons = rsp.json["features"]["offBoardingReasons"]["reasons"]
        self.assertEqual("Behandlung abgeschlossen", reasons[0][Reason.DISPLAY_NAME])
        self.assertEqual("Verstorben", reasons[1][Reason.DISPLAY_NAME])


class LocalizationsFullTestCase(AbstractDeploymentTestCase):
    fixtures = [Path(__file__).parent.joinpath("fixtures/localizations_test_dump.json")]
    user_route = "/api/extensions/v1/user"
    headers_user: None

    @classmethod
    def setUpClass(cls) -> None:
        super().setUpClass()
        cls.headers_user = cls.get_headers_for_token(USER_ID)

    def test_success_configuration_translated(self):
        self._publish_all_deployments()
        rsp = self.flask_client.get(f"{self.user_route}/{USER_ID}/configuration", headers=self.headers_user)
        self.assertEqual(200, rsp.status_code)
        patch = make_patch(LOCALIZATION_RESULT, rsp.json)
        self.assertEqual(len(list(filter(lambda d: d["op"] == "replace", patch.patch))), 0)

    def test_success_full_configuration_translated(self):
        self._publish_all_deployments()
        rsp = self.flask_client.get(f"{self.user_route}/{USER_ID}/fullconfiguration", headers=self.headers_user)
        self.assertEqual(200, rsp.status_code)
        patch = make_patch(LOCALIZATION_RESULT, rsp.json["deployments"][0])
        self.assertEqual(len(list(filter(lambda d: d["op"] == "replace", patch.patch))), 0)


class ImportExportDeploymentLocalizationTestCase(AbstractDeploymentTestCase):
    components = AbstractDeploymentTestCase.components + [StorageComponentV1()]
    fixtures = [Path(__file__).parent.joinpath("fixtures/localization_deployment_dump.json")]
    USER_ID = "60d321da1714272686488b8c"
    deployment_id = "5d386cc6ff885918d92edb2c"

    def setUp(self):
        super().setUp()
        self.headers = self.get_headers_for_token(ImportExportDeploymentLocalizationTestCase.USER_ID)

    def test_export_languages(self):
        url = url_for(
            "deployment_route.export_deployment_localizations",
            deployment_id=self.deployment_id,
        )
        rsp = self.flask_client.post(
            url,
            json={RetrieveLocalizationsRequestObject.LANGUAGES: [Language.EN]},
            headers=self.headers,
        )
        self.assertEqual(200, rsp.status_code, rsp.json)
        return rsp.json

    def _upload_test_file(self):
        with open(Path(__file__).parent.joinpath("fixtures/languages.zip"), "rb") as file:
            data = {
                "file": (io.BytesIO(file.read()), "languages.zip"),
            }
            rsp = self.flask_client.post(
                "/api/storage/v1/upload",
                data=data,
                headers=self.headers,
                content_type="multipart/form-data",
            )
            self.assertEqual(201, rsp.status_code, rsp.json)
            return rsp.json["id"]

    def test_import_languages(self):
        file_id = self._upload_test_file()
        url = url_for(
            "deployment_route.import_deployment_localizations",
            deployment_id=self.deployment_id,
        )
        rsp = self.flask_client.post(
            url,
            json={"fileId": file_id},
            headers=self.headers,
        )
        self.assertEqual(200, rsp.status_code, rsp.json)
        dep_json = self.get_deployment(headers=self.headers)
        self.assertEqual(dep_json.get("localizations", {}).get("en", {}).get("abc"), "efg", dep_json)
        self.assertIsNotNone(
            dep_json.get("localizations", {}).get("en", {}).get("someloca"),
            "Should not override the existing localizations",
        )
        return rsp.json
