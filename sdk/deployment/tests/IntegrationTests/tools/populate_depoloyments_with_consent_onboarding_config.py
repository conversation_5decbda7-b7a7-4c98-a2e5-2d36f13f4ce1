import logging
from datetime import datetime

from bson import ObjectId
from django.db import connection

from sdk.common.utils.file_utils import convert_complex_types_in_jsonb_to_str
from sdk.deployment.dtos.onboardingconfig import OnboardingModuleConfigDTO
from sdk.deployment.models import Deployment

logger = logging.getLogger(__name__)


def populate_deployments_with_consent_onboarding_config():
    config_data = {
        OnboardingModuleConfigDTO.ONBOARDING_ID: "Consent",
        OnboardingModuleConfigDTO.STATUS: "ENABLED",
        OnboardingModuleConfigDTO.ORDER: 1,
    }
    query = """
    SELECT deployment."mongoId"
    FROM deployment
    WHERE "consent" IS NOT NULL and NOT EXISTS (
        SELECT 1
        FROM jsonb_array_elements(deployment."onboardingConfigs") as onboardingConfig
        WHERE onboardingConfig->>'onboardingId' = 'Consent'
    )
    """
    with connection.cursor() as cursor:
        cursor.execute(query)
        result = cursor.fetchall()

    deployment_ids = {row[0].strip('"') for row in result}
    for deployment_id in deployment_ids:
        config_id = ObjectId()
        config_data[OnboardingModuleConfigDTO.ID] = config_id
        config_data = convert_complex_types_in_jsonb_to_str(config_data, 0, 0)
        deployment = Deployment.objects.filter(mongoId=deployment_id).first()
        deployment.onboardingConfigs.append(config_data)
        deployment.updateDateTime = datetime.utcnow()
        deployment.save()
        logger.info(f"Deployment {deployment_id} updated with Consent config {config_id}")
