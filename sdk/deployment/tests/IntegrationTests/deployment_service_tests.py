from pathlib import Path

from sdk.deployment.component import DeploymentComponent
from sdk.deployment.dtos.consent import ConsentLogDTO
from sdk.deployment.dtos.deployment import (
    ModuleConfig,
    OnboardingModuleConfigDTO,
)
from sdk.deployment.models import Deployment
from sdk.deployment.service.deployment_service import DeploymentService
from sdk.deployment.tests.IntegrationTests.test_helpers import get_module_config
from sdk.module_result.component import ModuleResultComponent
from sdk.module_result.modules import JournalModule
from sdk.tests.extension_test_case import ExtensionTestCase

DEPLOYMENT_ID = "5d386cc6ff885918d96eda1e"


class DeploymentServiceTests(ExtensionTestCase):
    components = [
        DeploymentComponent(),
        ModuleResultComponent(additional_modules=[JournalModule()]),
    ]
    fixtures = [Path(__file__).parent.joinpath("fixtures/deployment_service_dump.json")]

    def setUp(self):
        super(DeploymentServiceTests, self).setUp()
        self.service = DeploymentService()

    def test_create_module_config(self):
        module_config = ModuleConfig.from_dict(get_module_config())
        config_id = self.service.create_module_config(DEPLOYMENT_ID, module_config)
        deployment_count = len(
            [
                deployment
                for deployment in Deployment.objects.all()
                if len([module for module in deployment.moduleConfigs if module["id"] == config_id]) > 0
            ]
        )
        self.assertEqual(1, deployment_count)

    def test_create_onboarding_module_config(self):
        config_dict = {
            OnboardingModuleConfigDTO.ONBOARDING_ID: "Consent",
            OnboardingModuleConfigDTO.ORDER: 1,
        }
        config = OnboardingModuleConfigDTO.from_dict(config_dict)
        config_id = self.service.create_onboarding_module_config(DEPLOYMENT_ID, config)
        deployment_count = len(
            [
                deployment
                for deployment in Deployment.objects.all()
                if len([module for module in (deployment.onboardingConfigs or []) if module["id"] == config_id]) > 0
            ]
        )
        self.assertEqual(1, deployment_count)

    def test_retrieve_deployment_ids_by_tags(self):
        tags = ["test"]
        deployment_ids = self.service.retrieve_deployment_ids_by_tags(tags)
        self.assertEqual(len(deployment_ids), 2)
        self.assertIn(DEPLOYMENT_ID, deployment_ids)

    def test_retrieve_deployment_ids_by_tags_multiple(self):
        tags = ["test", "deployment_1", "non_existent_tag"]
        deployment_ids = self.service.retrieve_deployment_ids_by_tags(tags)
        self.assertEqual(len(deployment_ids), 2)
        self.assertIn(DEPLOYMENT_ID, deployment_ids)

    def test_retrieve_deployment_ids_by_tags_empty(self):
        tags = ["non_existent_tag"]
        deployment_ids = self.service.retrieve_deployment_ids_by_tags(tags)
        self.assertEqual(len(deployment_ids), 0)

    def test_retrieve_single_deployment_id_by_tags(self):
        tags = ["deployment_1"]
        deployment_ids = self.service.retrieve_deployment_ids_by_tags(tags)
        self.assertEqual(len(deployment_ids), 1)

    def test_retrieve_user_consent(self):
        user_id = "5d386cc6ff885918d96edb2c"
        consent_id = "67726a09d370f8ba7c6f3d87"
        consents: list[ConsentLogDTO] = self.service.retrieve_consent_logs(user_id, 5)
        self.assertNotEqual(len(consents), 0)
        consent = consents[0]
        self.assertEqual(consent.userId, user_id)
        self.assertEqual(consent.consentId, consent_id)
