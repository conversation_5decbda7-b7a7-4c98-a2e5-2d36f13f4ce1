from io import BytesIO
from pathlib import Path

from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.authorization.dtos.role.role import RoleDTO, RoleName
from sdk.authorization.models import User
from sdk.authorization.tests.IntegrationTests.test_helpers import (
    DEPLOYMENT_ID,
    ORGANIZATION_ID,
)
from sdk.common.utils.validators import remove_none_values, validate_object_id
from sdk.deployment.component import DeploymentComponent
from sdk.deployment.use_case.file_library_metadata import LibraryType
from sdk.organization.component import OrganizationComponent
from sdk.storage.component import StorageComponentV1
from sdk.tests.application_test_utils.test_utils import SDK_CONFIG_PATH
from sdk.tests.extension_test_case import ExtensionTestCase

USER_ID = "5e8f0c74b50aa9656c34789c"
SAMPLE_FILE_NAME = "sample.png"


def get_sample_file_content(file_name=SAMPLE_FILE_NAME):
    with open(Path(__file__).parent.joinpath(f"fixtures/{file_name}"), "rb") as upload:
        return upload.read()


class FileLibraryTests(ExtensionTestCase):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        StorageComponentV1(),
        OrganizationComponent(),
    ]
    fixtures = [Path(__file__).parent.joinpath("fixtures/deployment_dump.json")]
    config_file_path = SDK_CONFIG_PATH

    def upload_sample_file(self, user_id=USER_ID, file_name=SAMPLE_FILE_NAME, file_content=None):
        if file_content is None:
            data = {
                "file": (BytesIO(get_sample_file_content(file_name)), file_name),
            }
        else:
            data = {
                "file": (BytesIO(file_content), file_name),
            }
        rsp = self.flask_client.post(
            "/api/storage/v1/upload",
            data=data,
            headers=self.get_headers_for_token(identity=user_id),
            content_type="multipart/form-data",
        )
        return rsp

    @staticmethod
    def sample_csv_data(num_rows=10):
        column_names = ["key, en"]
        rows = [f"key{row}, test{row}" for row in range(1, num_rows + 1)]
        return "\n".join(column_names + rows).encode("utf-8")

    def add_files_to_library(self, file_ids: list[str], library_id: LibraryType, deployment_id: str = None):
        data = {
            "fileIds": file_ids,
            "deploymentId": deployment_id,
            "libraryId": library_id.value,
        }
        rsp = self.flask_client.post(
            "/api/extensions/v1/deployment/file-library",
            json=remove_none_values(data),
            headers=self.get_headers_for_token(identity=USER_ID),
        )
        return rsp

    def retrieve_files_in_library(self, library_id):
        rsp = self.flask_client.get(
            f"/api/extensions/v1/deployment/file-library/{library_id}",
            headers=self.get_headers_for_token(identity=USER_ID),
        )
        return rsp

    def test_file_library_flow(self):
        rsp = self.upload_sample_file()
        self.assertEqual(rsp.status_code, 201)
        file_id = rsp.json.get("id")
        self.assertTrue(validate_object_id(file_id))

        rsp = self.add_files_to_library(
            file_ids=[file_id],
            library_id=LibraryType.HUMA_IMAGE_LIBRARY,
            deployment_id=DEPLOYMENT_ID,
        )
        self.assertEqual(rsp.status_code, 200)

        rsp = self.retrieve_files_in_library(LibraryType.HUMA_IMAGE_LIBRARY.value)
        self.assertEqual(rsp.status_code, 200)
        files = rsp.json.get("files")
        self.assertEqual(len(files), 1)
        self.assertEqual(files[0].get("fileName"), SAMPLE_FILE_NAME)
        self.assertEqual(
            files[0].get("metadata").get("libraryId"),
            LibraryType.HUMA_IMAGE_LIBRARY.value,
        )

    def test_file_library_csv_upload_success(self):
        csv_content = self.sample_csv_data()
        rsp = self.upload_sample_file(file_name="sample.csv", file_content=csv_content)
        self.assertEqual(rsp.status_code, 201)
        file_id = rsp.json.get("id")
        self.assertTrue(validate_object_id(file_id))

        rsp = self.add_files_to_library(
            file_ids=[file_id],
            library_id=LibraryType.HUMA_OPTIONS_LIBRARY,
            deployment_id=DEPLOYMENT_ID,
        )
        self.assertEqual(rsp.status_code, 200)

    def test_file_library_xlsx_upload_unsuccessful(self):
        xlsx_content = b"wrong xlsx content"
        rsp = self.upload_sample_file(file_name="sample.xlsx", file_content=xlsx_content)
        self.assertEqual(rsp.status_code, 201)
        file_id = rsp.json.get("id")
        self.assertTrue(validate_object_id(file_id))

        rsp = self.add_files_to_library(
            file_ids=[file_id],
            library_id=LibraryType.HUMA_OPTIONS_LIBRARY,
            deployment_id=DEPLOYMENT_ID,
        )
        self.assertEqual(rsp.status_code, 403)

    def test_add_file_as_org_owner(self):
        """
        Non-super admin user with organization owner role should be able to add files to library but only with
        "deployment_options_library" libraryId

        """
        self._update_user_role(USER_ID, ORGANIZATION_ID, RoleName.ORGANIZATION_OWNER)

        csv_content = self.sample_csv_data()
        rsp = self.upload_sample_file(file_name="sample.csv", file_content=csv_content)
        self.assertEqual(201, rsp.status_code)
        file_id = rsp.json.get("id")
        self.assertTrue(validate_object_id(file_id))

        rsp = self.add_files_to_library(
            file_ids=[file_id],
            library_id=LibraryType.DEPLOYMENT_OPTIONS_LIBRARY,
            deployment_id=DEPLOYMENT_ID,
        )
        self.assertEqual(200, rsp.status_code)

        rsp = self.add_files_to_library(
            file_ids=[file_id],
            library_id=LibraryType.HUMA_OPTIONS_LIBRARY,
        )
        self.assertEqual(403, rsp.status_code)

    @staticmethod
    def _update_user_role(user_id: str, org_id: str, role_name: str):
        roles = [
            {
                "roleId": role_name,
                "resource": f"organization/{org_id}",
                "isActive": True,
                "userType": RoleDTO.UserType.ADMIN,
            }
        ]
        User.objects.filter(mongoId=user_id).update(roles=roles)
