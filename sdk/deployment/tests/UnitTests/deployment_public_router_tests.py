import unittest
from unittest.mock import patch, MagicMock

from flask import Flask

from sdk.auth.tests.test_helpers import TEST_CLIENT_ID, get_server_project
from sdk.common.exceptions.exceptions import InvalidClientIdException
from sdk.common.utils import inject
from sdk.deployment.router.deployment_public_router import (
    get_region,
    Region,
)
from sdk.phoenix.config.server_config import PhoenixServerConfig

DEPLOYMENT_PUBLIC_ROUTER_PATH = "sdk.deployment.router.deployment_public_router"

DEFAULT_BUCKET = "integrationtests"

testapp = Flask(__name__)
testapp.app_context().push()

SAMPLE_PROJECT = get_server_project()
EXPECTED_RESULT = {
    Region.BUCKET: DEFAULT_BUCKET,
    Region.BUCKET_REGION: "us-west-2",
    Region.CLIENT_ID: TEST_CLIENT_ID,
    Region.COUNTRY_CODE: "gb",
    Region.END_POINT_URL: "https://localhost/",
    Region.PROJECT_ID: SAMPLE_PROJECT.id,
    Region.STAGE: "DYNAMIC",
}


class RegionTestCase(unittest.TestCase):
    def setUp(self):
        self._config = MagicMock()
        self._config.server.project = get_server_project()
        self._config.server.storage.defaultBucket = DEFAULT_BUCKET
        self._config.server.storage.defaultRegion = "us-west-2"
        self._config.server.countryCode = "gb"

        def bind_and_configure(binder):
            binder.bind(PhoenixServerConfig, self._config)

        inject.clear_and_configure(bind_and_configure)

    @patch(f"{DEPLOYMENT_PUBLIC_ROUTER_PATH}.jsonify")
    def test_success_region_with_valid_client_id(self, mock_jsonify):
        with testapp.test_request_context("/", method="GET") as req:
            req.request.args = {"clientId": TEST_CLIENT_ID}
            get_region()
            res = {
                **EXPECTED_RESULT,
                Region.MINIMUM_VERSION: str(SAMPLE_PROJECT.clients[0].minimumVersion),
            }
            mock_jsonify.assert_called_with(res)

    def test_failure_region_with_invalid_client_id(self):
        client_id = "invalid id"
        with testapp.test_request_context("/", method="GET") as req:
            req.request.args = {"clientId": client_id}
            with self.assertRaises(InvalidClientIdException):
                get_region()

    def test_failure_with_invalid_client_id(self):
        with self.assertRaises(InvalidClientIdException):
            Region.from_dict(
                {
                    Region.BUCKET: "bucket",
                    Region.CLIENT_ID: "invalid client id",
                    Region.PROJECT_ID: SAMPLE_PROJECT.id,
                    Region.END_POINT_URL: "https://google.com",
                }
            )

    def test_success_region_with_type_qr_code(self):
        with testapp.test_request_context("/", method="GET") as req:
            req.request.args = {"clientId": TEST_CLIENT_ID, "type": "qrCode"}
            res = get_region()
            self.assertEqual("image/png", res.mimetype)


if __name__ == "__main__":
    unittest.main()
