import unittest


# MONGO_REPO_PATH = "sdk.deployment.repository.mongo_custom_onboarding_repository"


class MongoCustomOnboardingConfigTestCase(unittest.TestCase):
    # TODO: #django-test
    pass
    # def setUp(self) -> None:
    #     self.db = MagicMock()
    #
    #     def bind(binder):
    #         binder.bind(Database, self.db)
    #         binder.bind(PhoenixServerConfig, MagicMock())
    #
    #     inject.clear_and_configure(bind)
    #
    # @patch(f"{MONGO_REPO_PATH}.MongoCustomOnboardingConfig")
    # def test_retrieve_user_econsent_forms(self, mongo_model_mock):
    #     mongo_model_mock.ORGANIZATION_ID = "organizationId"
    #     mongo_model_mock.UPDATE_DATE_TIME = "updateDateTime"
    #     sample_id = "123456789012345678901234"
    #
    #     PostgresCustomEConsentRepository().retrieve_econsent_forms(organization_id=sample_id)
    #
    #     called_pipeline = mongo_model_mock.objects().aggregate.call_args.args[0]
    #     match_stage = {
    #         "$match": {
    #             mongo_model_mock.ORGANIZATION_ID: ObjectId(sample_id),
    #         }
    #     }
    #     self.assertIn(match_stage, called_pipeline)
    #
    # @patch(f"{MONGO_REPO_PATH}.MongoCustomOnboardingConfig")
    # def test_retrieve_user_econsent_forms_by_id(self, mongo_model_mock):
    #     mongo_model_mock.ORGANIZATION_ID = "organizationId"
    #     mongo_model_mock.CONFIG_BODY = "configBody"
    #     mongo_model_mock.UPDATE_DATE_TIME = "updateDateTime"
    #     sample_id = "123456789012345678901234"
    #
    #     PostgresCustomEConsentRepository().retrieve_econsent_forms_by_id(organization_id=sample_id, econsent_id=sample_id)
    #
    #     called_pipeline = mongo_model_mock.objects().aggregate.call_args.args[0]
    #     match_stage = {
    #         "$match": {
    #             OnboardingModuleConfig.ORGANIZATION_ID: ObjectId(sample_id),
    #             f"{OnboardingModuleConfig.CONFIG_BODY}.{EConsent.ID}": ObjectId(sample_id),
    #         }
    #     }
    #     self.assertIn(match_stage, called_pipeline)


if __name__ == "__main__":
    unittest.main()
