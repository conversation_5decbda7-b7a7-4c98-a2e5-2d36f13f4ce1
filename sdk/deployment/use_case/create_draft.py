from sdk.common.adapter.event_bus_adapter import EventBusAdapter
from sdk.common.exceptions.exceptions import InvalidRequestException
from sdk.common.usecase.response_object import ResultIdResponseObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils import inject
from sdk.common.utils.inject import autoparams
from sdk.deployment.dtos.status import Status
from sdk.deployment.events import CreateDraftEvent
from sdk.deployment.exceptions import DraftAlreadyExistsException
from sdk.deployment.router.deployment_requests import CreateDraftRequestObject
from sdk.deployment.service.deployment_service import DeploymentService
from sdk.organization.repository import OrganizationRepository


class CreateDraftUseCase(UseCase):
    request_object: CreateDraftRequestObject

    def __init__(self):
        self._service = DeploymentService()

    def process_request(self, request_object: CreateDraftRequestObject):
        original = self._service.retrieve_deployment(deployment_id=request_object.deploymentId)
        if original.status is not Status.DEPLOYED:
            raise InvalidRequestException("Deployment is not live")

        if original.draftId:
            raise DraftAlreadyExistsException

        draft_id = self._service.create_draft(original)
        self._link_to_organization(original.id, draft_id)
        self._emit_event(original.id, draft_id)
        return ResultIdResponseObject(id=draft_id)

    @autoparams("event_bus")
    def _emit_event(self, deployment_id: str, draft_id: str, event_bus: EventBusAdapter):
        event_bus.emit(CreateDraftEvent(draft_id, deployment_id))

    @staticmethod
    def _link_to_organization(deployment_id: str, draft_id: str):
        repo = inject.instance(OrganizationRepository, safe=True)
        if not repo:
            return

        org = repo.retrieve_organization_by_deployment_id(deployment_id=deployment_id)
        if not org:
            return

        repo.link_deployment(organization_id=org.id, deployment_id=draft_id)
