from sdk.authorization.services.authorization import AuthorizationService
from sdk.common.usecase.use_case import UseCase
from sdk.deployment.router.deployment_requests import RetrieveDeploymentRequestObject
from sdk.deployment.router.deployment_response_objects import (
    Release,
    ReleasesResponseObject,
    VersionsResponseObject,
)
from sdk.deployment.service.draft import DeploymentVersionService


class RetrieveVersionsUseCase(UseCase):
    request_object: RetrieveDeploymentRequestObject

    def __init__(self):
        self._service = DeploymentVersionService()

    def process_request(self, request_object: RetrieveDeploymentRequestObject):
        versions = self._service.get_versions(request_object.deploymentId)
        return VersionsResponseObject(versions=versions)


class RetrieveReleasesUseCase(UseCase):
    request_object: RetrieveDeploymentRequestObject

    def __init__(self):
        self._service = DeploymentVersionService()

    def process_request(self, request_object: RetrieveDeploymentRequestObject):
        releases = self._service.get_releases(request_object.deploymentId)
        self._populate_submitters(releases)
        return ReleasesResponseObject(releases=releases)

    @staticmethod
    def _populate_submitters(releases: list[Release]):
        submitter_ids = {release.submitter for release in releases}
        service = AuthorizationService()
        submitters = service.retrieve_simple_user_profiles_by_ids(submitter_ids)
        submitters_map = {submitter.id: submitter.get_full_name() for submitter in submitters}
        for release in releases:
            release.submitter = submitters_map.get(release.submitter)
