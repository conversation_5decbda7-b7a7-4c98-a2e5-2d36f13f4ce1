from django.db import models

from sdk.common.utils.mongo_utils import generate_obj_id
from sdk.deployment.dtos.status import EnableStatus


class CustomOnboardingConfig(models.Model):
    class Meta:
        db_table = "custom_onboarding_config"
        app_label = "deployment"

    mongoId = models.CharField(max_length=24, unique=True, default=generate_obj_id)
    onboardingId = models.CharField(max_length=20)
    status = models.CharField(max_length=8, choices=[(e, e) for e in EnableStatus], null=True, blank=True)
    versionInt = models.JSONField()
    configBody = models.JSO<PERSON>ield(null=True, default=None)
    order = models.IntegerField()
    version = models.IntegerField()
    versionString = models.CharField(max_length=10)
    userTypes = models.JSONField()
    createDateTime = models.DateTimeField()
    updateDateTime = models.DateTimeField()
    organizationId = models.Char<PERSON><PERSON>(max_length=24)
    deploymentIds = models.J<PERSON><PERSON>ield(null=True, blank=True)
    reason = models.Char<PERSON>ield(max_length=40, null=True, blank=True)
