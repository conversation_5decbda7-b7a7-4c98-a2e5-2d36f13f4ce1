from sdk import convertibleclass
from sdk.common.adapter.file_storage_adapter import FileStorageAdapter
from sdk.common.utils.convertible import default_field, meta, required_field, url_field
from sdk.common.utils.inject import autoparams
from sdk.common.utils.validators import validate_len
from sdk.phoenix.config.server_config import PhoenixServerConfig
from sdk.storage.dtos import S3Object


@convertibleclass
class AuthorisedRep:
    """Authorised REP"""

    ICON_OBJECT = "iconObject"
    ADDRESS_TITLE = "addressTitle"
    ADDRESS = "address"
    SHOW_BUILD_NUMBER = "showBuildNumber"

    iconObject: S3Object = required_field()
    addressTitle: str = required_field(metadata=meta(validate_len(1, 100)))
    address: str = required_field(metadata=meta(validate_len(1, 600)))
    showBuildNumber: bool = required_field()


@convertibleclass
class LegalDocumentDTO:
    TERM_AND_CONDITION_URL = "termAndConditionUrl"
    PRIVACY_POLICY_URL = "privacyPolicyUrl"
    EULA_URL = "eulaUrl"
    TERM_AND_CONDITION_OBJECT = "termAndConditionObject"
    PRIVACY_POLICY_OBJECT = "privacyPolicyObject"
    EULA_OBJECT = "eulaObject"
    AUTHORISED_REP = "authorisedRep"

    termAndConditionUrl: str = url_field()
    privacyPolicyUrl: str = url_field()
    eulaUrl: str = url_field()
    privacyPolicyObject: S3Object = default_field()
    termAndConditionObject: S3Object = default_field()
    eulaObject: S3Object = default_field()
    authorisedRep: AuthorisedRep = default_field()

    @autoparams()
    def set_presigned_urls_for_legal_docs(self, file_storage: FileStorageAdapter, config: PhoenixServerConfig):
        legal_docs_fields_mapping = {
            self.PRIVACY_POLICY_URL: self.privacyPolicyObject,
            self.TERM_AND_CONDITION_URL: self.termAndConditionObject,
            self.EULA_URL: self.eulaObject,
        }
        for doc_url_field, file_obj in legal_docs_fields_mapping.items():
            url = getattr(self, doc_url_field)
            if not url and file_obj and file_obj.key:
                url = file_storage.get_pre_signed_url(config.server.storage.defaultBucket, file_obj.key)
                self.set_field_value(field=doc_url_field, field_value=url)
