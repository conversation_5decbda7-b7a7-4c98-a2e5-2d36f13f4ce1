from flasgger import swag_from
from flask import g

from sdk.appointment.dtos.appointment_dto import AppointmentDTO
from sdk.appointment.models.appointment import Appointment, AppointmentAction
from sdk.appointment.router.appointment_requests import (
    BulkDeleteAppointmentsRequestBody,
    BulkDeleteAppointmentsRequestObject,
    CreateAppointmentRequestObject,
    DeleteAppointmentRequestObject,
    RetrieveAppointmentRequestObject,
    RetrieveAppointmentsGetRequestObject,
    RetrieveAppointmentsGetRequestQuery,
    RetrieveAppointmentsRequestBody,
    RetrieveAppointmentsRequestObject,
    UpdateAppointmentRequestObject,
)
from sdk.appointment.router.appointment_response import (
    BulkDeleteAppointmentsResponseObject,
    RetrieveAppointmentsResponseObject,
)
from sdk.appointment.use_case.bulk_delete_appointments_use_case import (
    BulkDeleteAppointmentsUseCase,
)
from sdk.appointment.use_case.create_appointment_use_case import (
    CreateAppointmentUseCase,
)
from sdk.appointment.use_case.delete_appointment_use_case import (
    DeleteAppointmentUseCase,
)
from sdk.appointment.use_case.retrieve_appointment_use_case import (
    RetrieveAppointmentUseCase,
)
from sdk.appointment.use_case.retrieve_appointments_use_case import (
    RetrieveAppointmentsUseCase,
    RetrieveAppointmentsUseCaseV1,
)
from sdk.appointment.use_case.update_appointment_use_case import (
    UpdateAppointmentUseCase,
)
from sdk.authorization.dtos.authorized_user import AuthorizedUser
from sdk.common.constants import SWAGGER_DIR
from sdk.common.usecase.response_object import ResultIdResponseObject
from sdk.phoenix.audit_logger import audit
from sdk.security import Access, ProtectedBlueprint

api = ProtectedBlueprint(
    "appointment",
    __name__,
    url_prefix="/api/extensions/v1/user",
)

appointment_route = api


@api.post("/<user_id>/appointment", requires=Access.USER.CONTACT)
@audit(AppointmentAction.CreateAppointment)
@api.input(AppointmentDTO.Schema, arg_name="body")
@api.output(ResultIdResponseObject.Schema, 201)
@swag_from(f"{SWAGGER_DIR}/create_appointment.yml")
def create_appointment(user_id, body: Appointment):
    request_object: CreateAppointmentRequestObject = CreateAppointmentRequestObject.from_dict(
        {**body.to_dict(include_none=False), AppointmentDTO.MANAGER_ID: user_id}
    )
    request_object.check_permission(g.authz_user)
    return CreateAppointmentUseCase().execute(request_object)


@api.get("/<user_id>/appointment/<appointment_id>")
@api.requires(Access.SELF.VIEW_EVENTS | Access.USER.CONTACT)
@api.output(AppointmentDTO.Schema)
@swag_from(f"{SWAGGER_DIR}/retrieve_appointment.yml")
def retrieve_appointment(user_id, appointment_id):
    request_object = RetrieveAppointmentRequestObject.from_dict(
        {RetrieveAppointmentRequestObject.APPOINTMENT_ID: appointment_id}
    )
    return RetrieveAppointmentUseCase().execute(request_object)


@api.put("/<user_id>/appointment/search")
@api.requires(Access.SELF.VIEW_EVENTS | Access.USER.CONTACT)
@api.input(RetrieveAppointmentsRequestBody.Schema, arg_name="body")
@api.output(AppointmentDTO.Schema(many=True))
@swag_from(f"{SWAGGER_DIR}/retrieve_appointments.yml")
def retrieve_appointments(user_id, body: RetrieveAppointmentsRequestBody):
    req = RetrieveAppointmentsRequestObject.from_dict(
        {
            RetrieveAppointmentsRequestObject.REQUESTER_ID: g.auth_user.id,
            RetrieveAppointmentsRequestObject.USER_ID: user_id,
            **body.to_dict(include_none=False),
        }
    )
    return RetrieveAppointmentsUseCase().execute(req)


@api.get("/<user_id>/appointment/search")
@api.requires(Access.SELF.VIEW_EVENTS | Access.USER.CONTACT)
@api.input(RetrieveAppointmentsGetRequestQuery.Schema, location="query", arg_name="query")
@api.output(RetrieveAppointmentsResponseObject.Schema)
@swag_from(f"{SWAGGER_DIR}/retrieve_appointments_v1.yml")
def search_appointments(user_id, query: RetrieveAppointmentsGetRequestQuery):
    request_object = RetrieveAppointmentsGetRequestObject.from_dict(
        {
            RetrieveAppointmentsGetRequestObject.USER_ID: user_id,
            RetrieveAppointmentsGetRequestObject.REQUESTER_ID: g.auth_user.id,
            **query.to_dict(include_none=False),
        }
    )

    return RetrieveAppointmentsUseCaseV1().execute(request_object)


@api.put("/<user_id>/appointment/<appointment_id>")
@api.requires(Access.SELF.EDIT_EVENTS | Access.USER.CONTACT)
@audit(AppointmentAction.UpdateAppointment, target_key="appointment_id")
@api.input(AppointmentDTO.Schema, arg_name="body")
@api.output(ResultIdResponseObject.Schema)
@swag_from(f"{SWAGGER_DIR}/update_appointment.yml")
def update_appointment(user_id, appointment_id, body: Appointment):
    authz_path_user: AuthorizedUser = g.authz_path_user

    request_object = UpdateAppointmentRequestObject.from_dict(
        {
            **body.to_dict(include_none=False),
            UpdateAppointmentRequestObject.REQUESTER_ID: user_id,
            UpdateAppointmentRequestObject.ID: appointment_id,
            UpdateAppointmentRequestObject.IS_USER: authz_path_user.is_user(),
        }
    )
    request_object.check_permission(g.authz_user)
    return UpdateAppointmentUseCase().execute(request_object)


@api.delete("/<user_id>/appointment/<appointment_id>")
@api.requires(Access.USER.CONTACT)
@audit(AppointmentAction.DeleteAppointment, target_key="appointment_id")
@api.output({}, 204)
@swag_from(f"{SWAGGER_DIR}/delete_appointment.yml")
def delete_appointment(user_id, appointment_id):
    request_object = DeleteAppointmentRequestObject.from_dict(
        {
            DeleteAppointmentRequestObject.APPOINTMENT_ID: appointment_id,
            DeleteAppointmentRequestObject.SUBMITTER: g.authz_user,
        }
    )
    DeleteAppointmentUseCase().execute(request_object)


@api.delete("/<user_id>/appointment")
@api.requires(Access.USER.CONTACT)
@audit(AppointmentAction.BulkDeleteAppointments)
@api.input(BulkDeleteAppointmentsRequestBody.Schema, arg_name="body")
@api.output(BulkDeleteAppointmentsResponseObject.Schema)
@swag_from(f"{SWAGGER_DIR}/bulk_delete_appointments.yml")
def bulk_delete_appointments(user_id, body: BulkDeleteAppointmentsRequestBody):
    request_object = BulkDeleteAppointmentsRequestObject.from_dict(
        {
            **body.to_dict(include_none=False),
            BulkDeleteAppointmentsRequestObject.USER_ID: user_id,
            BulkDeleteAppointmentsRequestObject.SUBMITTER: g.authz_user,
        }
    )
    return BulkDeleteAppointmentsUseCase().execute(request_object)
