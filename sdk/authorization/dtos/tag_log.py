from sdk import convertibleclass
from sdk.common.utils.convertible import required_field, default_field


@convertibleclass
class TagLogDTO:
    """Model to store tag logs in db."""

    MODEL_NAME = "taglog"
    USER_ID = "userId"
    AUTHOR_ID = "authorId"

    id: str = default_field()
    userId: str = required_field()
    authorId: str = required_field()
    tags: dict = required_field()
    createDateTime: str = default_field()
