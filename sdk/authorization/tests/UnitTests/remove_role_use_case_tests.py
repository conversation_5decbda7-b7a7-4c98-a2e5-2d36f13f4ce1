import unittest
from unittest.mock import MagicMock, patch

from sdk.authorization.dtos.role.role import Role<PERSON><PERSON>
from sdk.authorization.dtos.user import RoleAssignmentDTO, UserDTO
from sdk.authorization.router.user_profile_request import RemoveRolesRequestObject
from sdk.authorization.tests.UnitTests.update_roles_tests import get_authz_user
from sdk.authorization.use_cases.remove_role_use_case import RemoveRolesUseCase
from sdk.common.utils import inject
from sdk.deployment.repository.consent_repository import ConsentRepository
from sdk.deployment.repository.deployment_repository import DeploymentRepository
from sdk.deployment.repository.econsent_repository import EConsentRepository
from sdk.organization.repository.organization_repository import OrganizationRepository

SAMPLE_VALID_OBJ_ID = "60a20766c85cd55b38c99e12"
DEPLOYMENT_1_ID = "501919b5c03550c421c075aa"


class RemoveRoleUseCaseTestCase(unittest.TestCase):
    @classmethod
    def setUpClass(cls) -> None:
        def bind(binder):
            binder.bind(DeploymentRepository, MagicMock())
            binder.bind(ConsentRepository, MagicMock())
            binder.bind(EConsentRepository, MagicMock())
            binder.bind(OrganizationRepository, MagicMock())

        inject.clear_and_configure(bind)

    @patch(
        "sdk.authorization.dtos.user.AuthService",
        return_value=MagicMock(get_user=MagicMock(return_value=MagicMock(notification_email=None))),
    )
    def test_success_remove_role_use_case(self, _):
        auth_repo = MagicMock()
        org_repo = MagicMock()
        use_case = RemoveRolesUseCase(repo=auth_repo, organization_repo=org_repo)
        user = self._sample_user()
        auth_repo.retrieve_simple_user_profile.return_value = user
        req_obj = RemoveRolesRequestObject.from_dict(
            {
                RemoveRolesRequestObject.USER_ID: user.id,
                RemoveRolesRequestObject.SUBMITTER: get_authz_user(RoleName.ACCESS_CONTROLLER, DEPLOYMENT_1_ID),
                RemoveRolesRequestObject.ROLES: [self._sample_role_assignment_dict()],
            }
        )
        use_case.execute(req_obj)
        user.roles = []
        user.email = None
        auth_repo.update_user_profile.assert_called_once_with(user)

    def _sample_user(self):
        return UserDTO.from_dict(
            {
                UserDTO.ID: SAMPLE_VALID_OBJ_ID,
                UserDTO.EMAIL: "<EMAIL>",
                UserDTO.ROLES: [self._sample_role_assignment_dict()],
            }
        )

    @staticmethod
    def _sample_role_assignment_dict():
        return {
            RoleAssignmentDTO.ROLE_ID: RoleName.ACCESS_CONTROLLER,
            RoleAssignmentDTO.RESOURCE: "deployment/resource",
        }


if __name__ == "__main__":
    unittest.main()
