import unittest
from datetime import datetime, timedelta
from unittest.mock import <PERSON><PERSON><PERSON>, patch

import flask
from flask import Flask, g, request

from sdk.authorization.middleware import AuthorizationMiddleware
from sdk.authorization.dtos.role.default_roles import DefaultRoles
from sdk.authorization.dtos.role.role import RoleD<PERSON>, RoleName
from sdk.authorization.dtos.user import RoleAssignmentD<PERSON>, UserDTO
from sdk.common.exceptions import PermissionDenied, UserWithoutAnyRoleException
from sdk.common.utils import inject
from sdk.organization.repository import OrganizationRepository
from sdk.phoenix.config.server_config import PhoenixServerConfig, Server
from .test_helpers import (
    SAMPLE_DEPLOYMENT_ID as DEPLOYMENT_ID,
    SAMPLE_DEPLOYMENT_ID_2 as DEPLOYMENT_ID_2,
    SAMPLE_USER_ID as USER_ID,
)

ORGANIZATION_A_ID = "5d386cc6ff885918d96edb2c"
ORGANIZATION_B_ID = "5d386cc6ff885918d96edb3c"


def authorize(fn):
    def wrapper(self, *args, **kwargs):
        service_mock, args = args[0], args[1:]
        service_mock().retrieve_simple_user_profile.return_value = self.user
        return fn(self, *args, **kwargs)

    return wrapper


@patch("sdk.authorization.middleware.authorization.AuthorizationService")
class AuthorizationMiddlewareTestCase(unittest.TestCase):
    org_repo: MagicMock = None

    @classmethod
    def setUpClass(cls):
        cls.org_repo = MagicMock()
        cls.org_repo.retrieve_organization_ids.return_value = [DEPLOYMENT_ID]

        def bind(binder):
            binder.bind(PhoenixServerConfig, PhoenixServerConfig(Server(testEnvironment=True)))
            binder.bind(DefaultRoles, DefaultRoles())
            binder.bind(OrganizationRepository, cls.org_repo)

        inject.clear_and_configure(bind)

    def setUp(self):
        self.app = Flask(__name__)
        self.ctx = self.app.app_context()
        self.ctx.push()
        flask.g.is_automation_request = False
        self.request_ctx = self.app.test_request_context(headers={})
        self.request_ctx.push()

        self.user = UserDTO(id=USER_ID, roles=[])

    def tearDown(self):
        self.ctx.pop()
        try:
            self.request_ctx.pop()
        except IndexError:
            pass

    @classmethod
    def tearDownClass(cls):
        inject.clear()

    @authorize
    def test_no_roles(self):
        with self.request_ctx, self.assertRaises(UserWithoutAnyRoleException):
            self.assertCorrectRole()

    @authorize
    def test_user_role(self):
        role = RoleAssignmentDTO(
            roleId=RoleName.USER,
            resource=f"deployment/{DEPLOYMENT_ID}",
            userType=RoleDTO.UserType.USER,
            isActive=True,
        )
        self.user.roles = [role]

        with self.request_ctx:
            self.assertCorrectRole(role)

    @authorize
    def test_user_role__inactive(self):
        active_role = RoleAssignmentDTO(
            roleId=RoleName.USER,
            resource=f"deployment/{DEPLOYMENT_ID}",
            userType=RoleDTO.UserType.USER,
            isActive=True,
        )
        inactive_role = RoleAssignmentDTO(
            roleId=RoleName.USER,
            resource=f"deployment/{DEPLOYMENT_ID_2}",
            userType=RoleDTO.UserType.USER,
            isActive=False,
        )
        self.user.roles = [active_role, inactive_role]
        self.assertCorrectRole(active_role)

    @authorize
    def test_user_role__no_match(self):
        headers = {AuthorizationMiddleware.DEPLOYMENT_HEADER_KEY: DEPLOYMENT_ID}
        self.request_ctx = self.app.test_request_context(headers=headers)
        active_role = RoleAssignmentDTO(
            roleId=RoleName.USER,
            resource=f"deployment/{DEPLOYMENT_ID_2}",
            userType=RoleDTO.UserType.USER,
            isActive=True,
        )
        self.user.roles = [active_role]
        with self.assertRaises(PermissionDenied):
            self.assertCorrectRole()

    @authorize
    def test_admin_role(self):
        headers = {AuthorizationMiddleware.DEPLOYMENT_HEADER_KEY: DEPLOYMENT_ID}
        self.request_ctx = self.app.test_request_context(headers=headers)
        active_role = RoleAssignmentDTO(
            roleId=RoleName.ORGANIZATION_OWNER,
            resource=f"deployment/{DEPLOYMENT_ID}",
            userType=RoleDTO.UserType.SUPER_ADMIN,
            isActive=True,
        )
        self.user.roles = [active_role]
        self.assertCorrectRole(active_role)

    @authorize
    def test_admin_role__organization_access(self):
        headers = {AuthorizationMiddleware.ORGANIZATION_HEADER_KEY: ORGANIZATION_A_ID}
        self.request_ctx = self.app.test_request_context(headers=headers)
        active_role = RoleAssignmentDTO(
            roleId=RoleName.ORGANIZATION_OWNER,
            resource=f"organization/{ORGANIZATION_A_ID}",
            userType=RoleDTO.UserType.SUPER_ADMIN,
            isActive=True,
        )
        self.user.roles = [active_role]
        self.assertCorrectRole(active_role)

    @authorize
    def test_admin_role__deployment_access(self):
        headers = {AuthorizationMiddleware.DEPLOYMENT_HEADER_KEY: DEPLOYMENT_ID}
        self.request_ctx = self.app.test_request_context(headers=headers)
        active_role = RoleAssignmentDTO(
            roleId=RoleName.ORGANIZATION_OWNER,
            resource=f"organization/{DEPLOYMENT_ID}",
            userType=RoleDTO.UserType.SUPER_ADMIN,
            isActive=True,
        )
        self.user.roles = [active_role]
        self.assertCorrectRole(active_role)

    @authorize
    def test_admin_role__organization_access_choice(self):
        headers = {AuthorizationMiddleware.ORGANIZATION_HEADER_KEY: ORGANIZATION_B_ID}
        self.request_ctx = self.app.test_request_context(headers=headers)
        correct_role = RoleAssignmentDTO(
            roleId=RoleName.ORGANIZATION_OWNER,
            resource=f"organization/{ORGANIZATION_B_ID}",
            userType=RoleDTO.UserType.SUPER_ADMIN,
            isActive=True,
        )
        out_of_context_role = RoleAssignmentDTO(
            roleId=RoleName.ORGANIZATION_OWNER,
            resource=f"organization/{ORGANIZATION_A_ID}",
            userType=RoleDTO.UserType.SUPER_ADMIN,
            isActive=True,
        )
        self.user.roles = [out_of_context_role, correct_role]
        self.assertCorrectRole(correct_role)

    @authorize
    def test_admin_manager_role__user_type(self):
        headers = {
            AuthorizationMiddleware.ORGANIZATION_HEADER_KEY: ORGANIZATION_A_ID,
            AuthorizationMiddleware.USER_TYPE_HEADER_KEY: RoleDTO.UserType.MANAGER,
        }
        self.request_ctx = self.app.test_request_context(headers=headers)
        out_of_context_role_1 = RoleAssignmentDTO(
            roleId=RoleName.ACCOUNT_MANAGER,
            resource="deployment/*",
            userType=RoleDTO.UserType.SUPER_ADMIN,
            isActive=True,
        )
        out_of_context_role_2 = RoleAssignmentDTO(
            roleId=RoleName.ORGANIZATION_OWNER,
            resource=f"organization/{ORGANIZATION_A_ID}",
            userType=RoleDTO.UserType.SUPER_ADMIN,
            isActive=True,
        )
        correct_role = RoleAssignmentDTO(
            roleId=RoleName.ADMINISTRATOR,
            resource=f"organization/{ORGANIZATION_A_ID}",
            userType=RoleDTO.UserType.MANAGER,
            isActive=True,
        )
        self.user.roles = [out_of_context_role_1, out_of_context_role_2, correct_role]
        self.assertCorrectRole(correct_role)

    def test_update_user_metadata(self, mock_service):
        g.user = self.user
        self.user.lastLoginDateTime = datetime.utcnow() - timedelta(hours=2)

        AuthorizationMiddleware(request)._update_user_metadata(self.user)
        mock_service().update_last_login_date_time.assert_called_once_with(USER_ID)
        mock_service().reset_mock()
        self.user.lastLoginDateTime = datetime.utcnow() - timedelta(minutes=30)

        AuthorizationMiddleware(request)._update_user_metadata(self.user)
        mock_service().update_last_login_date_time.assert_not_called()

    def assertCorrectRole(self, role: RoleAssignmentDTO = None):
        AuthorizationMiddleware(self.request_ctx.request)(USER_ID)
        self.assertEqual(role, self.ctx.g.authz_user.role_assignment)
