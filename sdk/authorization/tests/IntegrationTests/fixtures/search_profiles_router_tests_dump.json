{"organization": [{"_id": {"$oid": "5fde855f12db509a2785da11"}, "name": "<PERSON><PERSON>", "deploymentIds": ["5d386cc6ff885918d96edb2c", "5ed8ae76cf99540b259a7315", "5d386cc6ff885918d96eab2c"], "status": "DEPLOYED", "roles": [{"id": {"$oid": "6151051575fee50d15298adb"}, "name": "Custom org role", "permissions": ["VIEW_PATIENT_IDENTIFIER", "VIEW_PATIENT_DATA", "MANAGE_PATIENT_DATA", "EXPORT_PATIENT_DATA", "CONTACT_PATIENT", "ADD_STAFF_MEMBERS", "VIEW_STAFF_LIST"]}]}], "deployment": [{"_id": {"$oid": "5d386cc6ff885918d96edb2c"}, "name": "X", "status": "DRAFT", "extraCustomFields": {"phoneNumber": {"clinicianUpdate": true, "errorMessage": "hu_test_phone_number_error", "isPrimary": false, "onboardingCollectionText": "Please enter your phone number", "order": 2, "profileCollectionText": "Patient phone number", "required": true, "showClinicianHeader": true, "type": "TEXT", "validation": ""}, "mediclinicNumber": {"errorMessage": "Insurance Number is incorrect", "validation": "", "onboardingCollectionText": "Please enter mediclinic number", "profileCollectionText": "Patient Unique ID", "required": true, "clinicianUpdate": true, "showClinicianHeader": true, "type": "TEXT", "order": 2}}, "roles": [{"id": {"$oid": "5e8eeae1b707216625ca4202"}, "name": "User", "permissions": ["VIEW_PATIENT_IDENTIFIER", "VIEW_PATIENT_DATA", "MANAGE_PATIENT_DATA", "EXPORT_PATIENT_DATA", "CONTACT_PATIENT"], "userType": "Manager"}, {"id": {"$oid": "6009d18864a6786c2a2be181"}, "name": "Manager", "permissions": ["VIEW_PATIENT_IDENTIFIER"]}, {"id": {"$oid": "5e8eeae1b707216625ca4203"}, "name": "Custom Role", "userType": "Manager", "permissions": ["MANAGE_PATIENT_DATA", "VIEW_PATIENT_DATA", "CONTACT_PATIENT"]}], "moduleConfigs": [{"id": {"$oid": "633d85596b2b949838c406f0"}, "updateDateTime": {"$date": "2022-10-05T14:23:37"}, "createDateTime": {"$date": "2022-10-05T14:23:37"}, "moduleId": "SelfRatedHealth", "moduleName": "", "status": "ENABLED", "order": 70, "configBody": {"isForManager": false, "pages": []}, "about": "Self-Rated Health is a single question that captures how healthy you think you are.", "schedule": {"isoDuration": "P1D", "timesPerDuration": 1, "timesOfDay": []}, "ragThresholds": [{"color": "#FBCCD7", "enabled": true, "fieldName": "value", "severity": 3, "thresholdRange": [{"minValue": 5, "maxValue": 5}], "type": "VALUE"}, {"color": "#FFDA9F", "enabled": true, "fieldName": "value", "severity": 2, "thresholdRange": [{"minValue": 2, "maxValue": 4}], "type": "VALUE"}, {"color": "#CBEBF0", "enabled": true, "fieldName": "value", "severity": 1, "thresholdRange": [{"minValue": 1, "maxValue": 1}], "type": "VALUE"}], "version": 1, "localizationPrefix": "hu_srh"}]}, {"_id": {"$oid": "5d386cc6ff885918d96eab2c"}, "name": "Unnamed care plan", "status": "DRAFT"}], "huma_auth_user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34710b"}, "status": 1, "emailVerified": false, "phoneNumber": "+380999999998", "displayName": "test", "userAttributes": {"familyName": "test", "givenName": "test"}, "createDateTime": {"$date": "2020-04-09T09:52:20"}, "updateDateTime": {"$date": "2020-04-09T09:52:20"}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "test"}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789d"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "manager"}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "user", "userAttributes": {"familyName": "user", "givenName": "user", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": "2020-04-09T09:52:20"}, "updateDateTime": {"$date": "2020-04-09T09:52:20"}}, {"_id": {"$oid": "62569f29e427af0b5c12779a"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999953", "displayName": "user", "userAttributes": {"familyName": "oliver", "givenName": "mike", "dob": "1988-02-20", "gender": "Male"}, "createDateTime": {"$date": "2020-04-09T09:52:20"}, "updateDateTime": {"$date": "2020-04-09T09:52:20"}}, {"_id": {"$oid": "61fb852583e256e58e7ea9e1"}, "status": 1, "emailVerified": false, "email": "<EMAIL>", "displayName": "second", "createDateTime": {"$date": "2020-08-03T03:39:00"}, "updateDateTime": {"$date": "2020-08-03T03:39:00"}}, {"_id": {"$oid": "61fb852583e256e58e7ea9e3"}, "status": 1, "emailVerified": false, "email": "<EMAIL>", "displayName": "User four", "createDateTime": {"$date": "2020-08-03T03:39:00"}, "updateDateTime": {"$date": "2020-08-03T03:39:00"}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34788d"}, "status": 1, "displayName": "manager2", "userAttributes": {"familyName": "manager2", "givenName": "manager2", "dob": "1988-02-20", "gender": "Male"}, "email": "<EMAIL>", "emailVerified": true, "phoneNumber": "+380999995555", "createDateTime": {"$date": "2020-04-09T09:52:20"}, "updateDateTime": {"$date": "2020-04-09T09:52:20"}}, {"_id": {"$oid": "60071f359e7e44330f732037"}, "status": 1, "displayName": "contributor", "userAttributes": {"familyName": "contributor", "givenName": "contributor", "dob": "1988-02-20", "gender": "Male"}, "email": "<EMAIL>", "emailVerified": true, "phoneNumber": "+380999993452", "createDateTime": {"$date": "2020-04-09T09:52:20"}, "updateDateTime": {"$date": "2020-04-09T09:52:20"}}, {"_id": {"$oid": "600720843111683010a73b4e"}, "status": 1, "displayName": "custom", "userAttributes": {"familyName": "custom", "givenName": "custom", "dob": "1988-02-20", "gender": "Male"}, "email": "<EMAIL>", "emailVerified": true, "phoneNumber": "+380999923452", "createDateTime": {"$date": "2020-04-09T09:52:20"}, "updateDateTime": {"$date": "2020-04-09T09:52:20"}}, {"_id": {"$oid": "6009d2409b0e1f2eab20bbb3"}, "status": 1, "displayName": "custom2", "userAttributes": {"familyName": "custom2", "givenName": "custom2", "dob": "1988-02-20", "gender": "Male"}, "email": "<EMAIL>", "emailVerified": true, "phoneNumber": "+380999920452", "createDateTime": {"$date": "2020-04-09T09:52:20"}, "updateDateTime": {"$date": "2020-04-09T09:52:20"}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789e"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380999999944", "displayName": "test2", "userAttributes": {"familyName": "test2", "givenName": "test2", "dob": "1988-02-22", "gender": "Male"}, "createDateTime": {"$date": "2020-04-09T09:52:20"}, "updateDateTime": {"$date": "2020-04-09T09:52:20"}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34779c"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "phoneNumber": "+380989999974", "displayName": "test2", "userAttributes": {"familyName": "test2", "givenName": "test2", "dob": "1988-02-22", "gender": "Male"}, "createDateTime": {"$date": "2020-04-09T09:52:20"}, "updateDateTime": {"$date": "2020-04-09T09:52:20"}}, {"_id": {"$oid": "6070b68c698f355159e1b724"}, "status": 1, "emailVerified": true, "email": "<EMAIL>"}, {"_id": {"$oid": "5ed803dd5f2f99da73655513"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "Support"}, {"_id": {"$oid": "5ed803dd5f2f99da73654413"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "Organization"}, {"_id": {"$oid": "5ed803dd5f2f99da73654410"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "Access"}, {"_id": {"$oid": "5ed803dd5f2f99da73655514"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "Administrator"}, {"_id": {"$oid": "5ed803dd5f2f99da73655147"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "Clinician"}, {"_id": {"$oid": "61920aa2b39e8acfb70a761c"}, "status": 1, "emailVerified": true, "email": "<EMAIL>", "displayName": "Custom Role user"}], "user": [{"_id": {"$oid": "5e8f0c74b50aa9656c34789b"}, "givenName": "test", "familyName": "test", "email": "<EMAIL>", "nhsId": "nhs_id", "gender": "MALE", "finishedOnboarding": true, "dateOfBirth": {"$date": 923321600000}, "tags": {"inpatient": "true"}, "extraCustomFields": {"phoneNumber": "+327888777111", "mediclinicNumber": "123abcd"}, "boardingStatus": {"status": 0}, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": "2022-07-28T18:00:38.234Z"}, "surgeryDateTime": {"$date": "2003-12-16T14:53:42"}, "recentModuleResults": {"5f1824ba504787d8d89ebeca": [{"Weight": {"id": "5ff4b14e0b3791bf7278d9c6", "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "moduleId": "Weight", "moduleConfigId": "5f1824ba504787d8d89ebeca", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "version": 0, "deviceName": "iOS", "isAggregated": false, "startDateTime": {"$date": "2021-01-05T18:34:54.000Z"}, "createDateTime": {"$date": "2021-01-05T18:34:54.658Z"}, "submitterId": {"$oid": "5f914cb28ee0158d37d2cf9c"}, "value": 1000}}, {"HeartRate": {"id": "5ff4b14e0b3791bf7278d9c6", "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "moduleId": "HeartRate", "moduleConfigId": "5f1824ba504787d8d89ebeca", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "version": 0, "deviceName": "iOS", "isAggregated": false, "startDateTime": {"$date": "2021-01-05T18:34:54.000Z"}, "createDateTime": {"$date": "2021-01-05T18:34:54.658Z"}, "submitterId": {"$oid": "5f914cb28ee0158d37d2cf9c"}, "value": 100, "metadata": {"answers": [{"questionId": "5e94b2007773091c9a592651", "id": "5e94b2007773091c9a592651", "question": "Are you aware or has a medical doctor informed you of any developmental issues with your first child from this pregnancy?", "answerText": "Yes"}]}}}], "5f1824ba504787d8d89ebec7": [{"AirQualityIndex": {"id": "5ff4b14e0b3791bf7278d9c5", "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "moduleId": "Weight", "moduleConfigId": "5f1824ba504787d8d89ebeca", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "version": 0, "deviceName": "iOS", "isAggregated": false, "startDateTime": {"$date": "2021-01-05T18:34:54.000Z"}, "createDateTime": {"$date": "2021-01-05T18:34:54.658Z"}, "submitterId": {"$oid": "5f914cb28ee0158d37d2cf9c"}, "value": 95, "latitude": 81, "longitude": 0}}], "5f1824ba504787d8d89ebec8": [{"VR_12": {"id": "5ff4b14e0b3791bf7278d9c5", "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "moduleId": "VR_12", "moduleConfigId": "5f1824ba504787d8d89ebeca", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "version": 0, "deviceName": "iOS", "isAggregated": false, "startDateTime": {"$date": "2021-01-05T18:34:54.000Z"}, "createDateTime": {"$date": "2021-01-05T18:34:54.658Z"}, "submitterId": {"$oid": "5f914cb28ee0158d37d2cf9c"}, "physicalScore": 41.32, "mentalScore": 50.32}}], "633d85596b2b949838c406f0": [{"Questionnaire": {"id": "63482eefc7f7478a993746d0", "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "moduleId": "SelfRatedHealth", "moduleConfigId": {"$oid": "633d85596b2b949838c406f0"}, "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "version": 1, "deviceName": "Android", "isAggregated": false, "startDateTime": {"$date": "2022-08-16T12:03:03"}, "createDateTime": {"$date": "2022-10-13T16:29:51"}, "submitterId": {"$oid": "5f914cb28ee0158d37d2cf9c"}, "server": {"hostUrl": "vb-ppserver.ngrok.io", "server": "1.24.0", "api": "V1"}, "ragThreshold": {"SelfRatedHealth": {}}, "flags": {"gray": 1, "amber": 0, "red": 0}, "answers": [{"id": {"$oid": "63482eefc7f7478a993746c8"}, "answerText": "hu_srh_health_label1", "questionId": "hu_srh_health", "question": "", "format": "BOOLEAN", "choices": ["'1'"], "selectedChoices": ["'1'"], "selectionCriteria": "SINGLE"}], "questionnaireId": "8aba5e45-b6dc-46b4-9fe6-202df2c9c049", "questionnaireName": "SelfRatedHealth", "value": 1, "skipped": []}}]}, "lastSubmitDateTime": {"$date": "2021-01-05T18:34:54.658Z"}, "unreadMessagesCount": {"manager": 2, "user": 0}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789c"}, "givenName": "user", "familyName": "user", "email": "<EMAIL>", "nhsId": "nhs_id", "dateOfBirth": {"$date": 925521600000}, "gender": "MALE", "finishedOnboarding": true, "boardingStatus": {"status": 0}, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "recentModuleResults": {"5f1824ba504787d8d89ebeca": [{"Weight": {"id": "5ff4b14e0b3791bf7278d9c5", "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "moduleId": "Weight", "moduleConfigId": "5f1824ba504787d8d89ebeca", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "version": 0, "deviceName": "iOS", "isAggregated": false, "startDateTime": {"$date": "2021-01-05T18:34:54.000Z"}, "createDateTime": {"$date": "2021-01-05T18:34:54.658Z"}, "submitterId": {"$oid": "5f914cb28ee0158d37d2cf9c"}, "value": 85}}], "5f1824ba504787d8d89ebec7": [{"AirQualityIndex": {"id": "5ff4b14e0b3791bf7278d9c5", "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "moduleId": "AirQuality", "moduleConfigId": "5f1824ba504787d8d89ebeca", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "version": 0, "deviceName": "iOS", "isAggregated": false, "startDateTime": {"$date": "2021-01-05T18:34:54.000Z"}, "createDateTime": {"$date": "2021-01-05T18:34:54.658Z"}, "submitterId": {"$oid": "5f914cb28ee0158d37d2cf9c"}, "value": 64, "latitude": 81, "longitude": 0}}], "5f1824ba504787d8d89ebec8": [{"VR_12": {"id": "5ff4b14e0b3791bf7278d9c5", "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "moduleId": "VR_12", "moduleConfigId": "5f1824ba504787d8d89ebeca", "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "version": 0, "deviceName": "iOS", "isAggregated": false, "startDateTime": {"$date": "2021-01-05T18:34:54.000Z"}, "createDateTime": {"$date": "2021-01-05T18:34:54.658Z"}, "submitterId": {"$oid": "5f914cb28ee0158d37d2cf9c"}, "physicalScore": 35.32, "mentalScore": 21.32}}], "633d85596b2b949838c406f0": [{"Questionnaire": {"id": "6346915848c73a54e3f17f4c", "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}, "moduleId": "SelfRatedHealth", "moduleConfigId": {"$oid": "633d85596b2b949838c406f0"}, "deploymentId": {"$oid": "5d386cc6ff885918d96edb2c"}, "version": 1, "deviceName": "Android", "isAggregated": false, "startDateTime": {"$date": "2022-08-16T12:03:03"}, "createDateTime": {"$date": "2022-10-13T16:29:51"}, "submitterId": {"$oid": "5f914cb28ee0158d37d2cf9c"}, "server": {"hostUrl": "vb-ppserver.ngrok.io", "server": "1.24.0", "api": "V1"}, "ragThreshold": {"SelfRatedHealth": {}}, "flags": {"gray": 1, "amber": 0, "red": 0}, "answers": [{"id": {"$oid": "63482eefc7f7478a993746c8"}, "answerText": "hu_srh_health_label5", "questionId": "hu_srh_health", "question": "", "format": "BOOLEAN", "choices": ["'5'"], "selectedChoices": ["'5'"], "selectionCriteria": "SINGLE"}], "questionnaireId": "8aba5e45-b6dc-46b4-9fe6-202df2c9c049", "questionnaireName": "SelfRatedHealth", "value": 5, "skipped": []}}]}, "tagsAuthorId": {"$oid": "5e8f0c74b50aa9656c34789b"}, "timezone": "UTC", "createDateTime": {"$date": "2020-04-09T09:52:20"}, "surgeryDateTime": {"$date": "2016-08-19T07:00:22"}, "stats": {"taskCompliance": {"current": 4, "total": 14, "due": null, "percentage": 29}}, "tags": {"flagged": "true"}, "lastSubmitDateTime": {"$date": "2021-01-11T18:34:54.658Z"}, "unreadMessagesCount": {"manager": 0, "user": 1}}, {"_id": {"$oid": "61fb852583e256e58e7ea9e1"}, "updateDateTime": {"$date": "2020-08-03T03:39:00"}, "createDateTime": {"$date": "2020-08-03T03:39:00"}, "email": "<EMAIL>", "givenName": "su", "familyName": "su", "nhsId": "nob_id", "dateOfBirth": {"$date": "2002-07-01T12:06:40"}, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96eab2c", "userType": "User", "isActive": true}]}, {"_id": {"$oid": "62569f29e427af0b5c12779a"}, "createDateTime": {"$date": "2020-08-03T03:39:00"}, "email": "<EMAIL>", "givenName": "mike", "familyName": "oliver", "phoneNumber": "+380999999953", "nhsId": "nob_id", "dateOfBirth": {"$date": "2002-07-01T12:06:40"}, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "labels": [{"labelId": {"$oid": "5d386cc6ff885918d96edb2c"}, "assignedBy": {"$oid": "5e8f0c74b50aa9656c34788d"}, "assignDateTime": "2022-05-24T08:17:27.116055Z"}, {"labelId": {"$oid": "5e8eeae1b707216625ca4202"}, "assignedBy": {"$oid": "5e8f0c74b50aa9656c34788d"}, "assignDateTime": "2022-05-24T08:17:27.116055Z"}], "boardingStatus": {"status": 1, "reasonOffBoarded": 7}, "timezone": "UTC", "extraCustomFields": {}, "componentsData": {"billing": {"status": 1}}, "enrollmentId": 10, "language": "en", "surgeryDateTime": {"$date": "1970-04-09T09:52:20"}, "lastSubmitDateTime": {"$date": "2020-01-10T18:34:54.658Z"}}, {"_id": {"$oid": "61fb852583e256e58e7ea9e3"}, "createDateTime": {"$date": "2020-08-03T03:39:00"}, "email": "<EMAIL>", "givenName": "nob4", "familyName": "nob4", "phoneNumber": "+380999996677", "nhsId": "nob_id", "dateOfBirth": {"$date": "2002-07-01T12:06:40"}, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "labels": [{"labelId": {"$oid": "5d386cc6ff885918d96edb2c"}, "assignedBy": {"$oid": "5e8f0c74b50aa9656c34788d"}, "assignDateTime": "2022-05-24T08:17:27.116055Z"}], "timezone": "UTC", "extraCustomFields": {}, "componentsData": {"billing": {"status": 0}}, "enrollmentId": 4, "language": "en", "lastSubmitDateTime": {"$date": "2020-01-10T18:34:54.658Z"}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789e"}, "givenName": "test2", "familyName": "test2", "email": "<EMAIL>", "phoneNumber": "+380999998844", "nhsId": "nhs_id", "dateOfBirth": {"$date": 723321600000}, "tags": {}, "boardingStatus": {"status": 1, "reasonOffBoarded": 4, "detailsOffBoarded": "Deceased"}, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "extraCustomFields": {"phoneNumber": "+380999999944"}, "timezone": "UTC", "stats": {"taskCompliance": {"current": 3, "total": 4, "due": null, "percentage": 75}}, "labels": [], "lastSubmitDateTime": {"$date": "2020-11-10T18:34:54.658Z"}, "unreadMessagesCount": {"manager": 1, "user": 0}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34779c"}, "givenName": "test2", "familyName": "test2", "email": "<EMAIL>", "phoneNumber": "+380989999944", "nhsId": "nhs_id", "dateOfBirth": {"$date": 723321600000}, "tags": {}, "boardingStatus": {"status": 1, "reasonOffBoarded": 3, "detailsOffBoarded": "Monitoring inappropriate"}, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "extraCustomFields": {"phoneNumber": "+380999999974"}, "timezone": "UTC", "stats": {"taskCompliance": {"current": 4, "total": 4, "due": null, "percentage": 100}}, "lastSubmitDateTime": {"$date": "2020-11-10T18:34:54.658Z"}, "createDateTime": {"$date": "2020-04-09T09:52:20"}, "surgeryDateTime": {"$date": "2003-12-16T14:53:42"}, "unreadMessagesCount": {"manager": 0, "user": 0}}, {"_id": {"$oid": "5e8f0c74b50aa9656c34789d"}, "givenName": "manager", "familyName": "manager", "email": "<EMAIL>", "roles": [{"roleId": "Admin", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "Manager", "isActive": true}]}, {"_id": {"$oid": "5e8f0c74b50aa9656c34788d"}, "givenName": "manager2", "familyName": "manager2", "email": "<EMAIL>", "phoneNumber": "+380999995555", "roles": [{"roleId": "Admin", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "Manager", "isActive": true}, {"roleId": "Admin", "resource": "deployment/5d386cc6ff885918d96eab2c", "userType": "Manager", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "60071f359e7e44330f732037"}, "givenName": "contributor", "familyName": "contributor", "email": "<EMAIL>", "phoneNumber": "+380999993452", "roles": [{"roleId": "Contributor", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "Manager", "isActive": true}, {"roleId": "Contributor", "resource": "deployment/5ed8ae76cf99540b259a7315", "userType": "Manager", "isActive": true}], "timezone": "UTC"}, {"_id": {"$oid": "600720843111683010a73b4e"}, "givenName": "custom", "familyName": "custom", "email": "<EMAIL>", "roles": [{"roleId": "5e8eeae1b707216625ca4202", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "Manager", "isActive": true}]}, {"_id": {"$oid": "6009d2409b0e1f2eab20bbb3"}, "givenName": "custom2", "familyName": "custom2", "email": "<EMAIL>", "roles": [{"roleId": "6009d18864a6786c2a2be181", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "Manager", "isActive": true}]}, {"_id": {"$oid": "6070b68c698f355159e1b724"}, "createDateTime": {"$date": "2021-04-09T20:18:20.996Z"}, "givenName": "parth", "familyName": "tank", "email": "<EMAIL>", "roles": [{"roleId": "6151051575fee50d15298adb", "resource": "organization/5fde855f12db509a2785da11", "userType": "Manager", "isActive": true}], "timezone": "Asia/Calcutta", "language": "en", "finishedOnboarding": true}, {"_id": {"$oid": "5e8f0c74b50aa9656c34710b"}, "givenName": "test", "familyName": "test", "phoneNumber": "+38099999998", "finishedOnboarding": true, "boardingStatus": {"status": 0}, "roles": [{"roleId": "User", "resource": "deployment/5d386cc6ff885918d96edb2c", "userType": "User", "isActive": true}], "timezone": "UTC", "createDateTime": {"$date": "2022-07-28T18:00:38.234Z"}, "surgeryDateTime": {"$date": "1970-04-09T09:52:20"}, "lastSubmitDateTime": {"$date": "2022-07-29T18:00:00.234Z"}}, {"_id": {"$oid": "5ed803dd5f2f99da73655513"}, "email": "<EMAIL>", "givenName": "Support", "familyName": "Org", "roles": [{"roleId": "Support", "resource": "organization/5fde855f12db509a2785da11", "userType": "Manager", "isActive": true}]}, {"_id": {"$oid": "5ed803dd5f2f99da73654413"}, "email": "<EMAIL>", "givenName": "Organization", "familyName": "Staff", "roles": [{"roleId": "OrganizationStaff", "resource": "organization/5fde855f12db509a2785da11", "userType": "Manager", "isActive": true}]}, {"_id": {"$oid": "5ed803dd5f2f99da73654410"}, "email": "<EMAIL>", "givenName": "Access", "familyName": "Controller", "roles": [{"roleId": "AccessController", "resource": "organization/5fde855f12db509a2785da11", "userType": "Manager", "isActive": true}]}, {"_id": {"$oid": "5ed803dd5f2f99da73655514"}, "email": "<EMAIL>", "givenName": "Administrator", "familyName": "Org", "roles": [{"roleId": "Administrator", "resource": "organization/5fde855f12db509a2785da11", "userType": "Manager", "isActive": true}]}, {"_id": {"$oid": "5ed803dd5f2f99da73655147"}, "email": "<EMAIL>", "givenName": "Clinician", "familyName": "Org", "roles": [{"roleId": "Clinician", "resource": "organization/5fde855f12db509a2785da11", "userType": "Manager", "isActive": true}]}, {"_id": {"$oid": "61920aa2b39e8acfb70a761c"}, "givenName": "Custom", "familyName": "User", "email": "<EMAIL>", "roles": [{"roleId": "6151051575fee50d15298adb", "resource": "organization/5fde855f12db509a2785da11", "userType": "Manager", "isActive": true}]}], "patient_manager_assignment": [{"_id": {"$oid": "5f9947c3a0ac1d90f817d570"}, "_cls": "MongoManagerAssignment", "createDateTime": {"$date": "2020-10-29T16:34:13.067Z"}, "managerIds": [{"$oid": "5f9c36e07e985e9dd3187932"}, {"$oid": "5f87199add28f8686d75c7d1"}, {"$oid": "5e8f0c74b50aa9656c34789d"}], "submitterId": {"$oid": "5f71a83d191e5b7fae3c1a7e"}, "updateDateTime": {"$date": "2020-10-31T00:30:04.644Z"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789b"}}, {"_id": {"$oid": "5f9947c3a0ac1d90f817d571"}, "_cls": "MongoManagerAssignment", "createDateTime": {"$date": "2020-10-29T16:34:13.067Z"}, "managerIds": [{"$oid": "5e8f0c74b50aa9656c34789d"}], "submitterId": {"$oid": "5f71a83d191e5b7fae3c1a7e"}, "updateDateTime": {"$date": "2020-10-31T00:30:04.644Z"}, "userId": {"$oid": "5e8f0c74b50aa9656c34789c"}}]}