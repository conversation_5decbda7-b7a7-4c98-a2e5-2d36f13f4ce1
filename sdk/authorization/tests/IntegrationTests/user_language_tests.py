from pathlib import Path

from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.authorization.dtos.user import UserDTO
from sdk.deployment.component import DeploymentComponent
from .abstract_permission_test_case import AbstractPermissionTestCase
from .test_helpers import USER_1_ID_DEPLOYMENT_X


class UserLanguageTestCase(AbstractPermissionTestCase):
    SERVER_VERSION = "1.3.1"
    API_VERSION = "v1"
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
    ]
    fixtures = [Path(__file__).parent.joinpath("fixtures/language_test_dump.json")]

    def setUp(self):
        super().setUp()
        self.base_path = "/api/extensions/v1/user"

    def test_success_update_user_language_with_configuration_api(self):
        headers = self.get_headers_for_token(USER_1_ID_DEPLOYMENT_X)
        headers["x-hu-locale"] = "ar"

        rsp = self.flask_client.get(
            f"{self.base_path}/{USER_1_ID_DEPLOYMENT_X}",
            headers=headers,
        )
        self.assertEqual("ar", rsp.json[UserDTO.LANGUAGE])

    def test_success_get_user_configuration_with_invalid_lang(self):
        headers = self.get_headers_for_token(USER_1_ID_DEPLOYMENT_X)
        headers["x-hu-locale"] = "invalid-lang"

        rsp = self.flask_client.get(
            f"{self.base_path}/{USER_1_ID_DEPLOYMENT_X}",
            headers=headers,
        )
        self.assertEqual("en", rsp.json[UserDTO.LANGUAGE])
