from datetime import datetime, <PERSON><PERSON><PERSON>
from pathlib import Path

from mohawk import Sender

from sdk.auth.use_case import CreateServiceAccountRequestObject
from sdk.authorization.dtos.user import AdditionalContactDetails, UserDTO
from sdk.authorization.router.user_profile_request import MoveUserDetails
from sdk.common.utils.validators import remove_none_values, utc_str_field_to_val
from sdk.deployment.dtos.deployment import MoveUserReasonDetails
from sdk.deployment.dtos.learn import LearnArticle, LearnArticleContent
from sdk.module_result.modules.ecg_module import ECGReading
from sdk.storage.dtos import FlatBufferS3Object, S3Object

TEST_FILE_NAME = "test_file.txt"

# super admins
SUPER_ADMIN_ID = "5ed803dd5f2f99da73684413"
ACCOUNT_MANAGER_WITH_ORG_ID = "61cb194c630781b664bf8eb5"
ACCOUNT_MANAGER_ID = "61cb194c630781b664bf8ec5"
ORG_OWNER_ID = "61cb194c630781b664bc7eb5"
ORG_EDITOR_ID = "61e6a8e2d9681a389f060848"
MANAGER_ADMIN = "449213d10b6d06b35e133b71"

# deployment X
DEPLOYMENT_ID = "5d386cc6ff885918d96edb2c"
DEPLOYMENT_CODE = "AU15"

CORRECT_MASTER_KEY = "********"
WRONG_MASTER_KEY = "1234567"

USER_1_ID_DEPLOYMENT_X = "5e8f0c74b50aa9656c34789b"
USER_1_ID_DEPLOYMENT_X_PROXY = "606eba3a2c94383d620b52ad"
USER_2_ID_DEPLOYMENT_X = "5e8f0c74b50aa9656c34789c"
USER_NON_PRIMITIVE_RESULTS = "5e8f0c74b50aa9656c34562b"
MANAGER_1_ID_DEPLOYMENT_X = "5e8f0c74b50aa9656c34789d"
MANAGER_2_ID_DEPLOYMENT_X = "5e8f0c74b50aa9656c34788d"
CONTRIBUTOR_1_ID_DEPLOYMENT_X = "60071f359e7e44330f732037"
CUSTOM_ROLE_1_ID_DEPLOYMENT_X = USER_ID_WITH_VIEW_IDENTIFIER = "600720843111683010a73b4e"
CUSTOM_ROLE_2_ID_DEPLOYMENT_X = "6009d2409b0e1f2eab20bbb3"
USER_ID_WITH_OUT_VIEW_IDENTIFIER = "602fa576c06fe59e3556171c"
USER_WITHOUT_ROLE = "607eb479a73c845d84e2f53b"
DEPLOYMENT_ADMINISTRATOR_ID = "5ed803dd5f2f99da73666614"
EXPORTER_ID = "5eda5eb1c460d4c74b162a89"

# deployment Y
DEPLOYMENT_2_ID = "5ed8ae76cf99540b259a7315"
USER_1_ID_DEPLOYMENT_Y = "5eda5e367adadfb46f7ff71f"
MANAGER_2_ID_DEPLOYMENT_Y = "5eda5db67adadfb46f7ff71d"

# organization
ORGANIZATION_ID = "5fde855f12db509a2785da06"
ORGANIZATION_STAFF_ID = "5ed803dd5f2f99da73654413"
ACCESS_CONTROLLER_ID = "5ed803dd5f2f99da73654410"
DEPLOYMENT_STAFF_ID = "5ed803dd5f2f99da73654411"
HUMA_SUPPORT_ID = "5ed803dd5f2f99da73675513"
SUPPORT_ID = "5ed803dd5f2f99da73655513"
SUPERVISOR_ID = "5ed803dd5f2f99da73655524"
ORGANIZATION_ADMINISTRATOR_ID = "5ed803dd5f2f99da73655514"

TEST_FILE_PATH = Path(__file__).parent.joinpath("fixtures/test_file.txt")


def now_str(delta=timedelta(days=0)):
    return utc_str_field_to_val(datetime.utcnow().replace(microsecond=0) + delta)


def content_test_file():
    with open(Path(__file__).with_name(TEST_FILE_NAME), "rb") as upload:
        return upload.read()


def get_user(name: str, validation_data: dict, email: str = None, phone_number: str = None):
    return remove_none_values(
        {
            "method": 0 if email else 1,
            "email": email,
            "phoneNumber": phone_number,
            "displayName": name,
            "validationData": validation_data,
            "userAttributes": {
                "familyName": name,
                "givenName": name,
            },
            "clientId": "ctest1",
            "projectId": "ptest1",
        }
    )


def get_invitation_sign_up_data(
    name: str,
    invitation_code: str = None,
    shortened_invitation_code: str = None,
    email: str = None,
    phone_number: str = None,
):
    validation_data = (
        {"shortenedCode": shortened_invitation_code}
        if shortened_invitation_code
        else {"invitationCode": invitation_code}
    )
    return get_user(
        email=email,
        name=name,
        validation_data=validation_data,
        phone_number=phone_number,
    )


def get_service_account_signup_data(service_account_name: str, role_id: str, resource_id: str, master_key: str) -> dict:
    return {
        CreateServiceAccountRequestObject.SERVICE_ACCOUNT_NAME: service_account_name,
        CreateServiceAccountRequestObject.VALIDATION_DATA: {"masterKey": master_key},
        CreateServiceAccountRequestObject.ROLE_ID: role_id,
        CreateServiceAccountRequestObject.RESOURCE_ID: resource_id,
    }


def get_deployment(name: str):
    return {
        "name": name,
        "status": "DEPLOYED",
        "color": "0x007AFF",
        "icon": {"bucket": "test", "key": "test", "region": "eu"},
    }


def get_consent():
    return {
        "enabled": "ENABLED",
        "instituteFullName": "string",
        "instituteName": "string",
        "instituteTextDetails": "string",
        "signature": {
            "signatureTitle": "Signature",
            "signatureDetails": "Please sign using your finger in the box below",
            "nameTitle": "Medopad Consent",
            "nameDetails": "Type your full name in text fields below",
            "hasMiddleName": True,
        },
        "review": {
            "title": "Review",
            "details": "Please review the form below, and tap Agree if you are ready to continue. If you have any "
            "questions or queries, please contact <NAME_EMAIL>",
        },
        "sections": [
            {
                "type": "OVERVIEW",
                "title": "Welcome",
                "details": "The following screens explain how Medopad works, the data it collects and privacy.",
                "reviewDetails": "Medopad helps to shar",
            },
            {
                "type": "DATA_GATHERING",
                "title": "Data gathering",
                "details": "Medopad collects information which ",
                "reviewDetails": "Medopad collects information",
            },
            {
                "type": "PRIVACY",
                "title": "Privacy",
                "details": "Your Child's clinical team can acces",
                "reviewDetails": "Medopad will always use a code",
            },
            {
                "type": "DATA_USE",
                "title": "Data Use",
                "details": "Data that You or ",
                "reviewDetails": "Medopad ",
            },
            {
                "type": "WITHDRAWING",
                "title": "Withdrawing",
                "details": "Medopad is her",
                "reviewDetails": "Medopad w",
            },
            {
                "type": "SHARING",
                "title": "Sharing Options",
                "details": "The clinical team ",
                "reviewDetails": "The clinical t",
                "options": [
                    {
                        "type": 0,
                        "text": "Share my Child's data with the clinical team and researchers",
                    },
                    {
                        "type": 1,
                        "text": "Only share my Child's data with the clinical team",
                    },
                ],
            },
        ],
    }


def get_learn_section():
    return {"title": "Test section", "order": 1}


def get_article():
    return {
        LearnArticle.TITLE: "article_ss three",
        LearnArticle.ORDER: 10,
        LearnArticle.TYPE: "SMALL",
        LearnArticle.THUMBNAIL_URL: {
            S3Object.REGION: "us-west-1",
            S3Object.KEY: "my.png",
            S3Object.BUCKET: "admin_bucket",
        },
        LearnArticle.CONTENT: {
            LearnArticleContent.TYPE: "VIDEO",
            LearnArticleContent.TIME_TO_READ: "20m",
            LearnArticleContent.TEXT_DETAILS: "Here what you read",
            LearnArticleContent.VIDEO_URL: {
                S3Object.BUCKET: "integrationtests",
                S3Object.KEY: "shared/5ded7cfa844317000162d5e7/logo/Screenshot_1572653613.png",
                S3Object.REGION: "cn",
            },
            LearnArticleContent.CONTENT_OBJECT: {
                S3Object.BUCKET: "integrationtests",
                S3Object.KEY: "shared/5ded7cfa844317000162d5e7/logo/Screenshot_1572653613.png",
                S3Object.REGION: "cn",
            },
        },
    }


def get_journal(deployment_id):
    return {
        "type": "Journal",
        "moduleId": "Journal",
        "deviceName": "iOS",
        "deploymentId": deployment_id,
        "startDateTime": "2020-04-28T21:13:07Z",
        "value": "Test journal",
    }


def weight_result(value, start_date_time=None, deployment_id=None):
    return {
        "type": "Weight",
        "deviceName": "iOS",
        "deploymentId": deployment_id or DEPLOYMENT_ID,
        "startDateTime": start_date_time or now_str(),
        "value": value,
    }


def ecg_result(value):
    return {
        "type": "ECGHealthKit",
        "deviceName": "iOS",
        "deploymentId": DEPLOYMENT_ID,
        "startDateTime": now_str(),
        "value": value,
        "ecgReading": {
            ECGReading.AVERAGE_HEART_RATE: 150.4,
            ECGReading.DATA_POINTS: {
                FlatBufferS3Object.KEY: "ecg_sample",
                FlatBufferS3Object.BUCKET: "integrationtests",
                FlatBufferS3Object.REGION: "sample",
                FlatBufferS3Object.FBS_VERSION: 1,
            },
        },
    }


def observation_note(deployment_id=None, start_date_time=None) -> dict:
    return {
        "type": "Questionnaire",
        "deviceName": "iOS",
        "deploymentId": deployment_id or DEPLOYMENT_ID,
        "startDateTime": start_date_time or now_str(),
        "questionnaireId": "e932907c-e2c6-4fc7-88a9-72e2bba65431",
        "questionnaireName": "Observation Notes",
        "isForManager": True,
        "answers": [
            {
                "answerText": "Answer",
                "answerScore": 55,
                "id": "5ee0d29a58e7994d8633037c",
                "questionId": "5ee0d29a58e7994d8633037c",
                "question": "Simple Question 1",
            },
            {
                "answerText": "Answer",
                "answerScore": 55,
                "id": "5ee0d29a58e7994d8633037c",
                "questionId": "5ee0d29a58e7994d8633037d",
                "question": "Simple Question 2",
            },
        ],
    }


def get_sample_preferred_units() -> dict:
    return {"preferredUnits": {"BloodGlucose": "mg/dL", "Weight": "kg", "Temperature": "oC"}}


def get_sample_additional_contact_details() -> dict:
    return {
        UserDTO.ADDITIONAL_CONTACT_DETAILS: {
            AdditionalContactDetails.REGULAR_CONTACT_NAME: "Emmanuel",
            AdditionalContactDetails.REGULAR_CONTACT_NUMBER: "+2347063335480",
            AdditionalContactDetails.EMERGENCY_CONTACT_NAME: "David",
            AdditionalContactDetails.EMERGENCY_CONTACT_NUMBER: "+2347063335480",
        }
    }


def get_request_header_hawk(
    user_key: str,
    auth_key: str,
    url: str,
    method: str,
    content: bytes,
    content_type: str,
) -> str:
    sender = Sender(
        {"id": user_key, "key": auth_key, "algorithm": "sha256"},
        url=url,
        method=method,
        content=content,
        content_type=content_type,
    )

    return sender.request_header


def get_sample_labels():
    return [
        {
            "id": "5e8eeae1b707216625ca4202",
            "text": "RECOVERED",
            "authorId": "5e8f0c74b50aa9656c34789b",
            "createDateTime": "2022-04-12T11:35:21.435000Z",
        },
        {
            "id": "5d386cc6ff885918d96edb2c",
            "text": "IN TREATMENT",
            "authorId": "5e8f0c74b50aa9656c34789b",
            "createDateTime": "2022-04-12T11:35:21.435000Z",
        },
    ]


def move_user_details(reason: str = MoveUserReasonDetails.DIFFERENTIAL_DIAGNOSIS):
    return {
        MoveUserDetails.REASON: reason,
        MoveUserDetails.SITUATION: "Nothing Special",
        MoveUserDetails.BACKGROUND: "Was Healthy",
        MoveUserDetails.ASSESSMENT: "Should be fine",
        MoveUserDetails.MESSAGE: "You were moved to the new deployment",
        MoveUserDetails.RECOMMENDATION: "Stay Healthy",
    }


def simple_econsent() -> dict:
    return {
        "enabled": "ENABLED",
        "title": "Informed consent form",
        "overviewText": "To participate in the trial study, please read  the consent form through in detail. \n\nIf you have any questions, please contact your study <NAME_EMAIL> or +44 1234 567 890 providing your consent to participate.",
        "contactText": "Please contact your study <NAME_EMAIL> if you have any questions.",
        "instituteFullName": "string",
        "instituteName": "string",
        "instituteTextDetails": "string",
        "signature": {
            "signatureTitle": "Signature",
            "signatureDetails": "Please sign using your finger in the box below",
            "nameTitle": "Medopad Consent",
            "nameDetails": "Type your full name in text fields below",
            "hasMiddleName": True,
            "showFirstLastName": True,
        },
        "additionalConsentQuestions": _additional_consent_questions(),
        "sections": [
            {
                "type": "INTRODUCTION",
                "title": "Introduction",
                "details": "INTRODUCTION",
                "reviewDetails": "You have been asked to participate in a clinical research study initiated, managed, and financed by ABC LAbs, who is the Sponsor of this study. Before your decide, it is important for you to understand why the research is being done and what it will involve. This informed consent form will provide you with essential information about this study and your rights as a study participant so that you can make an informed decision about your participation. Y \nYour decision to participate in this study is entirely voluntary. You will not lose any benefits to which you would otherwise be entitled if you refuse to participant. In addition, you may withdraw from the study at any time without penality or loss of benefits to which you are otherwise entitled. You will be informed in a timely manner, if any relevant new information about this drug or this study becomes available that may alter your willingness to continue to participate. If you agree, your General Practitioner will be told that you are taking part in this study.",
                "contentType": "IMAGE",
                "thumbnailLocation": {
                    "key": "deployment/<deployment_id>/<image_location>",
                    "region": "eu",
                    "bucket": "ppdeveufrankfurt",
                },
            },
            {
                "type": "PURPOSE",
                "title": "PURPOSE",
                "reviewDetails": "You are being asked to participate in this clinical research study because you have high blood pressure and are already taking prescribed commercial Cligoliob for an approved dose and indication in your countr.\nYou are eligible to participate in this study because following discussions with your own doctor you have decided to continue taking Cigoliob.\nInformation regarding the use of Cigoliob may be obtained from the patient information leaflet which accompanies your supply of Cigoliob and from your own treating physician.\nThe purpose of this study is to assess the levels of Cigoliob in blood across the course of the study in study participants with high blood pressure.\nThis study is expected to enroll approximately 100 women who have high blood pressure while takingcommercial Cigoliob across approximately 13 centers throughout Canada, USA, Swizerland and selected countries in the European Union (possible including but not necessarily limited to France, Germany, Spain or Italy).",
                "contentType": "IMAGE",
                "thumbnailUrl": "https://www.roadrunnerrecords.com/sites/g/files/g2000005056/f/sample-4.jpg",
            },
            {
                "type": "REVIEW_TO_SIGN",
                "title": "REVIEW_TO_SIGN",
                "reviewDetails": "I have read and understood this consent document. By signing this: \n• I confirm that I have had time to read carefully and understand the study participant informed consent provided for this study.\n• I confirm that I have had the opportunity to discuss the study and ask questions, and I am satisfied with the answers and explanations that I have been provided.\n• I give permission for my medical records to be reviewed by the Sponsor or designee and/or representatives of any Drug Reculatory Authorities such as the U.S. FDA and Insitutional Review Boards.\n• I understand that my participation is voluntary and that I am free to withdraw at any time without giving any reason and without my medical care or legal rights being affected.. I agree that the Sponsor can continue to use the information about my health collected during the study to preserve the integrity of the study, even if I withdraw from the study.",
                "contentType": "VIDEO",
                "videoLocation": {
                    "key": "deployment/<deployment_id>/<video_location>",
                    "region": "eu",
                    "bucket": "ppdeveufrankfurt",
                },
                "thumbnailLocation": {
                    "key": "deployment/<deployment_id>/<thumbnail_location>",
                    "region": "eu",
                    "bucket": "ppdeveufrankfurt",
                },
            },
            {
                "type": "DURING_THE_TRIAL",
                "title": "DURING_THE_TRIAL",
                "reviewDetails": "During the course of this trial, you’ll be asked to complete a series of task such as:\n• Entering your blood pressure in the morning every day in the Huma app\n•Recording your medication intake in the Huma app\n• Attending telemedicine video conferences with your study care team in the Huma app\n• Attending face-to-face appointments with your study care team every 3 months\nThere are some acitivites that you would not be able to do during the course of the trial. They are:\n• Donating blood\n• Traveling via plane or helicopter",
                "contentType": "VIDEO",
                "videoUrl": "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4",
                "thumbnailUrl": "https://www.roadrunnerrecords.com/sites/g/files/g2000005056/f/sample-4.jpg",
            },
        ],
        "admissionText": "Admission text",
    }


def simple_econsent_log() -> dict:
    return {
        "givenName": "givenName",
        "middleName": "middleName",
        "familyName": "familyName",
        "signature": {
            "key": "econsent_images/signature.png",
            "region": "eu",
            "bucket": "ppdeveufrankfurt",
        },
        "sharingOption": 1,
        "additionalConsentAnswers": {
            "isDataSharedForFutureStudies": False,
            "isDataSharedForResearch": True,
        },
    }


def _additional_consent_questions() -> list:
    return [
        {
            "type": "isDataSharedForFutureStudies",
            "enabled": "ENABLED",
            "format": "BOOLEAN",
            "text": "Would you like your data to be used in future studies?",
            "description": 'If you give consent, the following information will be shared: your first and last name, date of birth, sex, and postcode.\n\nFull information on how Discover handles your data, including how to withdraw consent, <a href="https://sample.org">can be found here</a>.',
            "order": 2,
        },
        {
            "type": "isDataSharedForResearch",
            "enabled": "ENABLED",
            "format": "BOOLEAN",
            "text": "Do you allow the research investigators to retrieve your data from the vaccine registry?",
            "description": 'If you give consent, the following information will be shared: your first and last name, date of birth, sex, and postcode.\n\nFull information on how Discover handles your data, including how to withdraw consent, <a href="https://sample.org">can be found here</a>.',
            "order": 1,
        },
    ]


def simple_consent() -> dict:
    return {
        "enabled": "ENABLED",
        "instituteFullName": "string",
        "instituteName": "string",
        "instituteTextDetails": "string",
        "signature": {
            "signatureTitle": "Signature",
            "signatureDetails": "Please sign using your finger in the box below",
            "nameTitle": "Medopad Consent",
            "nameDetails": "Type your full name in text fields below",
            "hasMiddleName": True,
            "showFirstLastName": True,
        },
        "additionalConsentQuestions": _additional_consent_questions(),
        "review": {
            "title": "Review",
            "details": "Please review the form below, and tap Agree if you are ready to continue. If you have any "
            "questions or queries, please contact <NAME_EMAIL>",
        },
        "sections": [
            {
                "type": "OVERVIEW",
                "title": "Welcome",
                "details": "The following screens explain how Medopad works, the data it collects and privacy.",
                "reviewDetails": "Medopad helps to shar",
            },
            {
                "type": "DATA_GATHERING",
                "title": "Data gathering",
                "details": "Medopad collects information which ",
                "reviewDetails": "Medopad collects information",
            },
            {
                "type": "PRIVACY",
                "title": "Privacy",
                "details": "Your Child's clinical team can acces",
                "reviewDetails": "Medopad will always use a code",
            },
            {
                "type": "DATA_USE",
                "title": "Data Use",
                "details": "Data that You or ",
                "reviewDetails": "Medopad ",
            },
            {
                "type": "TIME_COMMITMENT",
                "title": "Time Commitment",
                "details": "Time commitment",
                "reviewDetails": "Medopad ",
            },
            {
                "type": "STUDY_SURVEY",
                "title": "Study Survey",
                "details": "Study Survey",
                "reviewDetails": "Medopad ",
            },
            {
                "type": "STUDY_TASKS",
                "title": "Study Tasks",
                "details": "Study Tasks",
                "reviewDetails": "Medopad ",
            },
            {
                "type": "WITHDRAWING",
                "title": "Withdrawing",
                "details": "Medopad is her",
                "reviewDetails": "Medopad w",
            },
            {
                "type": "SHARING",
                "title": "Sharing Options",
                "details": "The clinical team ",
                "reviewDetails": "The clinical t",
                "options": [
                    {
                        "type": 0,
                        "text": "Share my Child's data with the clinical team and researchers",
                    },
                    {
                        "type": 1,
                        "text": "Only share my Child's data with the clinical team",
                    },
                ],
            },
            {
                "type": "DATA_PROCESSING",
                "title": "Data Processing",
                "details": "Date processing",
                "reviewDetails": "Medopad w",
            },
            {
                "type": "FEEDBACK",
                "title": "Feedback",
                "reviewDetails": "As part of this study, you may be contracted to provide feedback on your experiences either using the Huma app.",
            },
            {
                "type": "AGREEMENT",
                "title": "Agreement",
                "reviewDetails": "In order for you to register and use the Huma app, your consent is required.",
                "options": [
                    {
                        "type": 0,
                        "order": 0,
                        "text": 'I consent to Huma processing my personal information, including my health information, as described in the <a href="https://storage.googleapis.com/hu-deployment-static-content/afib/Huma_US_Privacy_Policy_incl_CCPA.pdf">Privacy Policy</a>',
                    },
                    {
                        "type": 1,
                        "order": 1,
                        "text": 'I agree to the terms of the <a href="https://storage.googleapis.com/hu-deployment-static-content/afib/Huma_EULA_Bayer_AFib.pdf">Huma End User Licence Agreement</a>',
                    },
                ],
            },
            {
                "type": "DATA_GATHERING",
                "title": "Your Data",
                "reviewDetails": "As part of this study, you may be contracted to provide feedback on your experiences either using the Huma app.",
            },
        ],
        "admissionText": "Admission text",
    }
