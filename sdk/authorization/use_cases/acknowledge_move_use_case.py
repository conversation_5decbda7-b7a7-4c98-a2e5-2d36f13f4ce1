from sdk.authorization.exceptions import MoveAlreadyAcknowledgedException
from sdk.authorization.repository.user_move_history_repository import (
    UserMoveHistoryRepository,
)
from sdk.authorization.router.user_profile_request import (
    AcknowledgeMoveRequestObject,
)
from sdk.common.usecase.response_object import ResultIdResponseObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.inject import autoparams


class AcknowledgeMoveUseCase(UseCase):
    request_object: AcknowledgeMoveRequestObject

    @autoparams()
    def __init__(self, repo: UserMoveHistoryRepository):
        self._repo = repo

    def process_request(self, request_object: AcknowledgeMoveRequestObject) -> ResultIdResponseObject:
        result_id = self._repo.acknowledge_user_move(
            user_id=self.request_object.userId, history_id=request_object.acknowledgeId
        )
        if not result_id:
            raise MoveAlreadyAcknowledgedException

        return ResultIdResponseObject(result_id)
