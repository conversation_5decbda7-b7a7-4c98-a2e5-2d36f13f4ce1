from sdk.authorization.exceptions import RoleDoesNotExistException
from sdk.authorization.dtos.user import RoleAssignmentDTO, UserDTO
from sdk.authorization.router.user_profile_request import (
    RemoveRolesRequestObject,
)
from sdk.common.usecase.response_object import ResultIdResponseObject
from sdk.common.utils.common_functions_utils import find
from .base_authorization_use_case import BaseAuthorizationUseCase


class RemoveRolesUseCase(BaseAuthorizationUseCase):
    def process_request(self, request_object: RemoveRolesRequestObject) -> ResultIdResponseObject:
        user = self.auth_repo.retrieve_simple_user_profile(user_id=request_object.userId)

        self._check_roles_exist(user.roles)
        roles = user.roles[:] if user.roles else []
        for role in request_object.roles:
            for user_role in roles:
                if role == user_role:
                    roles.remove(role)

        self._update_user_roles(user.id, roles)
        return ResultIdResponseObject(user.id)

    def _check_roles_exist(self, roles: list[RoleAssignmentDTO]):
        for role in self.request_object.roles:
            if not find(lambda r: r == role, roles):
                raise RoleDoesNotExistException(role.roleId)

    def _update_user_roles(self, user_id: str, roles) -> str:
        body = {UserDTO.ID: user_id, UserDTO.ROLES: roles}
        req_obj = UserDTO.from_dict(body, ignored_fields=(UserDTO.ROLES,))
        return self.auth_repo.update_user_profile(req_obj)
