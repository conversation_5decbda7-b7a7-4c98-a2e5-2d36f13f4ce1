import logging

from sdk.common.exceptions.exceptions import DetailedException, ErrorCodes


class UserErrorCodes(ErrorCodes):
    INVALID_SURGERY_DATE = 1000001
    INVALID_USER_ID = 1000002
    OUTDATED_ECONSENT = 1000003
    WITHDRAWN_ECONSENT = 1000004


class AuthorizationErrorCodes:
    WRONG_ACTIVATION_OR_MASTER_KEY = 1000003
    INVITATION_DOES_NOT_EXIST = 1000004
    CANT_RESEND_INVITATION = 1000005
    INVITATION_IS_EXPIRED = 1000006
    MAX_LABELS_ASSIGNED = 1000007
    MOVE_ALREADY_ACKNOWLEDGED = 1000008
    INVALID_RECIPIENT = 1000009
    USER_IS_OFF_BOARDED = 200007


class OnboardingErrorCodes:
    MOVE_ACKNOWLEDGED = 1002009


class WrongActivationOrMasterKeyException(DetailedException):
    def __init__(self, message=None):
        super().__init__(
            code=AuthorizationErrorCodes.WRONG_ACTIVATION_OR_MASTER_KEY,
            debug_message=message or "Activation Code or Master Key is wrong.",
            status_code=400,
        )


class InvitationDoesNotExistException(DetailedException):
    def __init__(self, message=None, **kwargs):
        super().__init__(
            code=AuthorizationErrorCodes.INVITATION_DOES_NOT_EXIST,
            debug_message=message or "Invitation does not exist",
            status_code=404,
            **kwargs,
        )


class CantResendInvitation(DetailedException):
    def __init__(self, message=None):
        super().__init__(
            code=AuthorizationErrorCodes.CANT_RESEND_INVITATION,
            debug_message=message or "Can't resend invitation",
            status_code=400,
        )


class InvitationIsExpiredException(DetailedException):
    def __init__(self, message=None):
        super().__init__(
            code=AuthorizationErrorCodes.INVITATION_IS_EXPIRED,
            debug_message=message or "Invitation is expired",
            status_code=400,
        )


class MaxLabelsAssigned(DetailedException):
    def __init__(self, message=None):
        super().__init__(
            code=AuthorizationErrorCodes.MAX_LABELS_ASSIGNED,
            debug_message=message or "User already has maximum number of labels",
            status_code=400,
        )


class OnboardingError(DetailedException):
    """Raise an error when user needs to complete actions on onboarding"""

    def __init__(self, config_id=None, code: int = None, **kwargs):
        super().__init__(
            code=code,
            debug_message=f"{config_id} is required to be finished.",
            status_code=428,
            **kwargs,
        )


class MoveAlreadyAcknowledgedException(DetailedException):
    """Raise an error when user needs to complete actions on onboarding"""

    def __init__(self):
        super().__init__(
            code=AuthorizationErrorCodes.MOVE_ALREADY_ACKNOWLEDGED,
            debug_message="Move already acknowledged.",
            status_code=400,
        )


class InvalidRecipientException(DetailedException):
    """Raise an error when wrong user's contact data is passed"""

    def __init__(self):
        super().__init__(
            code=AuthorizationErrorCodes.INVALID_RECIPIENT,
            debug_message=(
                "Invalid recipient. User should have at least one of: " + "phone number, email, contactEmail"
            ),
            status_code=400,
        )


class InvalidSurgeryDateError(DetailedException):
    def __init__(self, message=False):
        super().__init__(
            code=UserErrorCodes.INVALID_SURGERY_DATE,
            debug_message=message or "Invalid Surgery Date",
            status_code=400,
        )


class EconsentWithdrawalError(DetailedException):
    def __init__(self, message=False, code: int = None):
        super().__init__(
            code=code or UserErrorCodes.OUTDATED_ECONSENT,
            debug_message=message or "Error while withdrawing Econsent",
            status_code=400,
        )


class UserWithdrewEconsent(DetailedException):
    def __init__(self, message=False, code: int = None):
        super().__init__(
            code=code or UserErrorCodes.WITHDRAWN_ECONSENT,
            debug_message=message or "Cant reactivate user with withdrawn econsent",
            status_code=400,
        )


class UserDoesNotExist(DetailedException):
    """If user doesn't exist."""

    def __init__(self, message=False, log_level: int = logging.INFO):
        super().__init__(
            code=UserErrorCodes.INVALID_USER_ID,
            debug_message=message or "User Doesn't Exist.",
            status_code=404,
            log_level=log_level,
        )


class UserAttributeMissingError(DetailedException):
    def __init__(self, field_name):
        super().__init__(
            code=ErrorCodes.INVALID_REQUEST,
            debug_message=f"User attribute [{field_name}] is missing",
            status_code=400,
        )


class EConsentInvalidAdditionalConsentAnswersException(DetailedException):
    def __init__(self, question_types=None):
        super().__init__(
            code=ErrorCodes.INVALID_REQUEST,
            debug_message=f"EConsent Additional Consent Question type [{question_types}] is/are not valid.",
            status_code=400,
        )


class ProxyAlreadyLinkedException(DetailedException):
    def __init__(self, message=None):
        super().__init__(
            code=RoleErrorCodes.PROXY_ALREADY_LINKED,
            debug_message=message or "Proxy user already linked",
        )


class RoleDoesNotExistException(DetailedException):
    def __init__(self, role: str, message=None):
        super().__init__(
            code=RoleErrorCodes.ROLE_DOES_NOT_EXIST,
            debug_message=message or f"Role {role} does not exist for this user",
        )


class UserIsOffBoardedException(DetailedException):
    def __init__(self, message=False):
        super().__init__(
            code=AuthorizationErrorCodes.USER_IS_OFF_BOARDED,
            debug_message=message or "User is off boarded",
            status_code=400,
        )


class InvalidModuleForPreferredUnitException(DetailedException):
    def __init__(self, module):
        super().__init__(
            code=ErrorCodes.INVALID_REQUEST,
            debug_message=f"Module [{module}] does not accept PreferredUnit.",
            status_code=400,
        )


class ProxyUnlinkedException(DetailedException):
    def __init__(self, message=False):
        super().__init__(
            code=RoleErrorCodes.PROXY_UNLINKED,
            debug_message=message or "Proxy unlinked",
            status_code=403,
        )


class RoleErrorCodes:
    PROXY_ALREADY_LINKED = 800001
    PROXY_UNLINKED = 800002
    ROLE_DOES_NOT_EXIST = 800003
    ROLE_ALREADY_EXISTS = 800004
