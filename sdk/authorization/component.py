import logging
import os

from sdk.auth.events.check_attributes_event import Check<PERSON><PERSON><PERSON>ttributesEvent
from sdk.auth.events.delete_user_event import DeleteUserEvent
from sdk.auth.events.generate_token_event import GenerateTokenEvent
from sdk.auth.events.post_sign_in_event import PostSignInEvent
from sdk.auth.events.post_sign_up_event import PostSignUpCompleteEvent, PostSignUpEvent
from sdk.auth.events.pre_sign_up_event import PreSignUpEvent
from sdk.auth.events.set_auth_attributes_events import (
    PostSetAuthAttributesEvent,
    PreRequestPasswordResetEvent,
    PreSetAuthAttributesEvent,
    RetrieveUsernameEvent,
)
from sdk.auth.events.token_extraction_event import TokenExtractionEvent
from sdk.auth.events.verify_credentials_event import PreVerifyCredentialsEvent
from sdk.authorization.callbacks import (
    allow_ip_callback,
    create_assign_label_logs,
    create_tag_log,
    on_token_extraction_callback,
    register_user_with_role,
    remove_document_from_storage,
    update_calendar_on_profile_update,
)
from sdk.authorization.callbacks.callbacks import (
    assign_new_static_label_to_user,
    assign_organization_owner,
    check_policy_for_generate_token,
    check_set_auth_attributes,
    check_valid_client_used,
    delete_manager_assignment_log_after_off_boarding,
    delete_move_user_history,
    delete_patient_manager_assignment,
    delete_patient_manager_assignment_log_on_user_delete,
    delete_related_role,
    delete_user_helper_agreement_log_on_user_delete_event,
    delete_user_label_log_on_user_delete_event,
    delete_user_role,
    delete_user_snapshot,
    delete_user_tag_logs_on_user_delete_event,
    get_unread_messages_badge,
    get_user_name,
    notify_clinician_on_user_demographics_change,
    off_board_proxy,
    on_calendar_view_users_data_callback,
    on_change_role_calculate_events,
    on_user_delete_callback,
    post_order_send_invitation,
    retrieve_users_timezones,
    send_move_user_case_notifications,
    send_user_off_board_notifications,
    send_user_reactivation_notification,
    set_enrollment_id,
    update_recent_module_results,
    update_user_profile_email,
    update_user_profile_last_login,
    update_user_stats,
    update_users_stats_in_deployment_without_key_actions,
    user_add_role_send_email,
    validate_and_populate_sign_up_data_from_invitation,
)
from sdk.authorization.callbacks.node_callbacks import LastLoginDate, filter_users_on
from sdk.authorization.config.config import AuthorizationComponentOptions, AuthorizationConfig
from sdk.authorization.di.components import (
    PostCreateUserEvent,
    bind_appsflyer_deferred_link_adapter,
    bind_authorization_repository,
    bind_default_roles,
    bind_email_invitation_adapter,
    bind_invitation_repository,
    bind_roles_manager,
    bind_user_move_history_repository,
    bind_user_revision_repository,
    bind_user_stats_calculator,
)
from sdk.authorization.events.get_badges_event import GetUserBadgesEvent
from sdk.authorization.events.get_custom_role_event import GetDeploymentCustomRoleEvent
from sdk.authorization.events.post_assign_label_event import PostAssignLabelEvent
from sdk.authorization.events.post_create_tag_event import PostCreateTagEvent
from sdk.authorization.events.post_move_user_event import PostMoveUserEvent
from sdk.authorization.events.post_order_send_invitation_event import PostOrderSendInvitationEvent
from sdk.authorization.events.post_personal_document_delete_event import PostPersonalDocumentDeleteEvent
from sdk.authorization.events.post_unlink_proxy_user_event import PostUnlinkProxyUserEvent
from sdk.authorization.events.post_user_add_role_event import PostUserAddRoleEvent
from sdk.authorization.events.post_user_off_board_event import PostUserOffBoardEvent
from sdk.authorization.events.post_user_onboard_event import UserOnboardedEvent
from sdk.authorization.events.post_user_profile_update_event import PostUserProfileUpdateEvent
from sdk.authorization.events.post_user_reactivation_event import PostUserReactivationEvent
from sdk.authorization.events.update_stats_event import UpdateUserStatsEvent
from sdk.authorization.exceptions import AuthorizationErrorCodes
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.authorization.router.admin_router import api as admin_invitation_router
from sdk.authorization.router.invitation_router import api as invitation_router, api_v1 as invitation_router_v1
from sdk.authorization.router.invitation_status_router import api as invitation_status_router
from sdk.authorization.router.public_invitation_router import api as public_invitation_router
from sdk.authorization.router.user_profile_router import api_v1 as profile_api_v1, api_v2 as profile_api_v2
from sdk.calendar.events import CalendarViewUserDataEvent, CreateCalendarLogEvent, RequestUsersTimezonesEvent
from sdk.common.adapter.event_bus_adapter import EventBusAdapter
from sdk.common.exceptions.exceptions import ErrorCodes
from sdk.common.utils.inject import Binder, autoparams
from sdk.deployment.callbacks import deployment_custom_role_callback
from sdk.deployment.events.delete_custom_roles_event import DeleteDeploymentCustomRolesEvent
from sdk.key_action.events.events import PostDeleteKeyActionsEvent
from sdk.module_result.event_bus.post_create_module_results_batch_event import PostCreateModuleResultBatchEvent
from sdk.organization.events import PostCreateOrganizationEvent, PostDeleteOrganizationEvent
from sdk.phoenix.component_manager import EventSubscription, EventSubscriptions, PhoenixBaseComponent
from sdk.phoenix.config.server_config import PhoenixServerConfig
from sdk.rule_engine.nodes.node_manager import Node, NodeManager
from sdk.rule_engine.workflow import WorkflowManager

logger = logging.getLogger(__name__)


class AuthorizationComponent(PhoenixBaseComponent):
    config: AuthorizationConfig
    config_class = AuthorizationConfig
    component_options = AuthorizationComponentOptions
    component_type = "sdk"
    tag_name = "authorization"
    tasks = ["sdk.authorization"]
    dedicated_task_queues = {
        "sdk.authorization.tasks.async_send_invitation_email": {PhoenixBaseComponent.QUEUE: "notification"}
    }
    localization_path = os.path.dirname(os.path.realpath(__file__)) + "/localization"
    _ignored_error_codes = (
        AuthorizationErrorCodes.CANT_RESEND_INVITATION,
        AuthorizationErrorCodes.INVITATION_DOES_NOT_EXIST,
        AuthorizationErrorCodes.WRONG_ACTIVATION_OR_MASTER_KEY,
        AuthorizationErrorCodes.MAX_LABELS_ASSIGNED,
        ErrorCodes.PHONE_NUMBER_NOT_SET,
        ErrorCodes.INVALID_CLIENT_ID,
        ErrorCodes.INVALID_PROJECT_ID,
        ErrorCodes.USER_MOVE_LIMIT_REACHED,
    )
    routers = [
        profile_api_v1,
        profile_api_v2,
        invitation_router,
        invitation_router_v1,
        admin_invitation_router,
        invitation_status_router,
        public_invitation_router,
    ]

    def __init__(
        self,
        component_options: AuthorizationComponentOptions = None,
        additional_event_callbacks: tuple[EventSubscription] = None,
    ):
        self.component_options = component_options or AuthorizationComponentOptions()
        super().__init__(additional_event_callbacks)

    @autoparams()
    def post_setup(self, event_bus: EventBusAdapter):
        self._create_metabase_view()
        self._add_workflow_nodes(event_bus)

        super().post_setup(event_bus)

    @property
    def callbacks(self) -> EventSubscriptions:
        callbacks = [
            # create extension user on sdk user create
            (PostSignUpEvent, register_user_with_role),
            (PostSignUpCompleteEvent, set_enrollment_id),
            (UserOnboardedEvent, assign_new_static_label_to_user),
            # add extension user instance to each request (g.user) with jwt token
            (TokenExtractionEvent, on_token_extraction_callback),
            # create tag log after user tag created
            (PostCreateTagEvent, create_tag_log),
            # create label log after user label assigned
            (PostAssignLabelEvent, create_assign_label_logs),
            # save recent module results to user profile
            (PostCreateModuleResultBatchEvent, update_recent_module_results),
            (PostOrderSendInvitationEvent, post_order_send_invitation),
            # return user timezones for calendar periodic task
            (RequestUsersTimezonesEvent, retrieve_users_timezones),
            (PostUserProfileUpdateEvent, update_calendar_on_profile_update),
            (PostUserProfileUpdateEvent, notify_clinician_on_user_demographics_change),
            (CalendarViewUserDataEvent, on_calendar_view_users_data_callback),
            (PostSignInEvent, update_user_profile_last_login),
            # check that app client is allowed for user's role
            (PostSignInEvent, check_valid_client_used),
            (CheckAuthAttributesEvent, check_valid_client_used),
            (PreVerifyCredentialsEvent, check_valid_client_used),
            (PreRequestPasswordResetEvent, check_valid_client_used),
            (PostSetAuthAttributesEvent, update_user_profile_email),
            (DeleteDeploymentCustomRolesEvent, delete_user_role),
            (PreSetAuthAttributesEvent, check_set_auth_attributes),
            (DeleteUserEvent, on_user_delete_callback),
            (PostUserReactivationEvent, send_user_reactivation_notification),
            (PostUnlinkProxyUserEvent, off_board_proxy),
            (PostUserAddRoleEvent, user_add_role_send_email),
            (PostUserAddRoleEvent, on_change_role_calculate_events),
            (GenerateTokenEvent, check_policy_for_generate_token),
            (GetDeploymentCustomRoleEvent, deployment_custom_role_callback),
            (UpdateUserStatsEvent, update_user_stats),
            (CreateCalendarLogEvent, update_user_stats),
            (PostCreateOrganizationEvent, assign_organization_owner),
            (PostDeleteOrganizationEvent, delete_related_role),
            (GetUserBadgesEvent, get_unread_messages_badge),
            (RetrieveUsernameEvent, get_user_name),
            (PostPersonalDocumentDeleteEvent, remove_document_from_storage),
            (DeleteUserEvent, delete_user_tag_logs_on_user_delete_event),
            (DeleteUserEvent, delete_patient_manager_assignment_log_on_user_delete),
            (DeleteUserEvent, delete_user_label_log_on_user_delete_event),
            (DeleteUserEvent, delete_user_helper_agreement_log_on_user_delete_event),
            (DeleteUserEvent, delete_move_user_history),
            (DeleteUserEvent, delete_user_snapshot),
            (PreSignUpEvent, validate_and_populate_sign_up_data_from_invitation),
            (
                PostMoveUserEvent,
                (send_move_user_case_notifications, delete_patient_manager_assignment),
            ),
            (
                PostUserOffBoardEvent,
                (
                    delete_manager_assignment_log_after_off_boarding,
                    send_user_off_board_notifications,
                    off_board_proxy,
                ),
            ),
            (PostDeleteKeyActionsEvent, update_users_stats_in_deployment_without_key_actions),
        ]
        if self.config.checkAdminIpAddress:
            callbacks.append((PreSignUpEvent, allow_ip_callback))
            logger.info("Admin IP address has been enabled")
        return callbacks

    def bind(self, binder: Binder, config: PhoenixServerConfig):
        bind_default_roles(binder)
        bind_roles_manager(binder)
        bind_authorization_repository(binder)
        bind_invitation_repository(binder)
        bind_email_invitation_adapter(binder)
        bind_user_stats_calculator(binder)
        bind_user_revision_repository(binder)
        bind_user_move_history_repository(binder)
        binder.bind(AuthorizationComponentOptions, self.component_options)
        bind_appsflyer_deferred_link_adapter(binder, config)

    def setup_auth(self):
        """Auth is handled by the authorization blueprints."""
        pass

    @autoparams()
    def _create_metabase_view(self, repo: AuthorizationRepository):
        repo.create_metabase_user_view()

    @autoparams("node_manager", "workflow_manager", safe=True)
    def _add_workflow_nodes(self, event_bus, node_manager: NodeManager, workflow_manager: WorkflowManager):
        if not node_manager or not workflow_manager:
            return
        class_name = self.__class__.__name__
        node_manager.add_node(
            Node(
                name=f"{class_name}.filter_users",
                callback=filter_users_on,
                parameters=("resource", "field_name", "field_value", "operator"),
                output=("user_ids",),
            )
        )
        create_user_node_name = f"{class_name}.on_user_create"
        node_manager.add_node(
            Node(
                name=create_user_node_name,
                callback=lambda event: (event.user, event.user.roles[0].resource),
                output=("user", "resource"),
                parameters=("event",),
                is_trigger=True,
            )
        )

        node_manager.add_node(LastLoginDate())

        event_bus.subscribe(
            PostCreateUserEvent,
            lambda event: workflow_manager.trigger(
                create_user_node_name, event.user.roles[0].resource, {"event": event}
            ),
        )
