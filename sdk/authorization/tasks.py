import logging
from datetime import datetime

from celery.schedules import crontab

from sdk.authorization import helpers
from sdk.authorization.adapters.email_invitation_adapter import EmailInvitationAdapter
from sdk.authorization.dtos.invitation import InvitationDTO
from sdk.authorization.dtos.role.default_roles import DefaultRoles
from sdk.authorization.dtos.role.role import RoleDTO
from sdk.authorization.dtos.stats_calculator import UserStatsCalculator
from sdk.authorization.dtos.user import RoleAssignmentDTO, UserDTO
from sdk.authorization.exceptions import UserDoesNotExist
from sdk.authorization.repository.invitation_repository import InvitationRepository
from sdk.calendar.utils import get_end_of_today_by_timezone
from sdk.celery.app import celery_app
from sdk.common.adapter.deferred_link_adapter import DeferredLinkAdapter
from sdk.common.constants import SEC_IN_HOUR
from sdk.common.utils import inject
from sdk.phoenix.config.project_config import Client

logger = logging.getLogger(__name__)
stats_logger = logging.getLogger("CELERY_STATS_CALCULATOR")

CHUNK_SIZE = 1000


@celery_app.on_after_finalize.connect
def setup_periodic_tasks(sender, **kwargs):
    sender.add_periodic_task(
        crontab(minute=30),
        calculate_stats_per_user.s(),
        name="User stats calculator",
    )

    sender.add_periodic_task(
        crontab(minute="*"),
        send_invitation_reminder.s(),
        name="Invitation Reminder",
    )

    sender.add_periodic_task(
        crontab(hour=4, minute=0),
        update_deferred_links_ttl.s(),
        name="Deferred Link TTL Updater",
    )


@celery_app.task(queue="notification")
def send_invitation_reminder():
    """Reminder to email invitations which will expire in next 24 hours."""
    from sdk.authorization.use_cases.invitation_use_cases import (
        ReminderInvitationsUseCase,
    )

    ReminderInvitationsUseCase().send_email_invitation_reminders()


def calculate_stats_for_user(task):
    user_id, AuthorizationService = task

    service = AuthorizationService()
    users = service.retrieve_users_with_user_role_including_only_fields(
        (
            UserDTO.TIMEZONE,
            UserDTO.ROLES,
            UserDTO.COMPONENTS_DATA,
            UserDTO.CREATE_DATE_TIME,
            UserDTO.BOARDING_STATUS,
        ),
        user_id=user_id,
    )
    if not users:
        return stats_logger.info("User has been deleted.")

    user: UserDTO = next(iter(users))

    calculator = inject.instance(UserStatsCalculator)
    end_of_day = get_end_of_today_by_timezone(user.timezone).replace(tzinfo=None)
    template = f"stats for user {user.id} in {user.timezone}."
    stats_logger.info(f"Calculating {template} DT: {end_of_day}")
    stats, component_stats = calculator.run(user)
    service.update_user_stats(user, stats, component_stats)
    stats_logger.info(f"Calculated {template} New stats {stats}")


@celery_app.task(expires=SEC_IN_HOUR)
def calculate_stats_per_user(*args, **kwargs):
    from sdk.authorization.services.authorization import AuthorizationService
    from sdk.deployment.service.deployment_service import DeploymentService

    deployment_ids = DeploymentService().retrieve_deployment_ids_with_key_actions()
    stats_logger.info(f"Retrieved {len(deployment_ids)} deployment IDs with key actions")

    service = AuthorizationService()
    user_ids = service.retrieve_user_ids_by_deployment_ids(deployment_ids)
    total_users = len(user_ids)
    stats_logger.info(f"Calculating stats for {total_users} users")

    if len(user_ids) > 50000:
        mid_point = len(user_ids) // 2
        first_half = user_ids[:mid_point]
        second_half = user_ids[mid_point:]
        stats_logger.info(
            f"Splitting {total_users} users into two chunks of {len(first_half)} and {len(second_half)} users"
        )

        task1 = process_user_chunk.delay(first_half)
        task2 = process_user_chunk.delay(second_half)
        stats_logger.info(f"Launched parallel tasks with IDs: {task1.id} and {task2.id}")
    else:
        stats_logger.info(f"Processing {total_users} users in single task")
        for user_id in user_ids:
            calculate_stats_for_user((user_id, AuthorizationService))


@celery_app.task(expires=SEC_IN_HOUR)
def process_user_chunk(users_chunk: list[str]):
    from sdk.authorization.services.authorization import AuthorizationService

    stats_logger.info(f"Processing chunk with {len(users_chunk)} users")
    for user_id in users_chunk:
        calculate_stats_for_user((user_id, AuthorizationService))


@celery_app.task()
def async_send_invitation_email(invitations: list[dict], client: dict, submitter: dict, language: str):
    from sdk.authorization.router.invitation_request_objects import (
        SendInvitationRequestObject,
    )
    from sdk.authorization.use_cases.invitation_use_cases import (
        SendInvitationUseCase,
    )
    from sdk.authorization.dtos.authorized_user import AuthorizedUser

    for invitation_data in invitations:
        invitation = InvitationDTO.from_dict(invitation_data)
        user = UserDTO.from_dict(submitter, use_validator_field=False)
        invitation_adapter = inject.instance(EmailInvitationAdapter)
        if invitation.role.userType == RoleDTO.UserType.ADMIN:
            invitation_adapter.send_admin_invitation_email(
                invitation.email,
                Client.from_dict(client),
                language,
                invitation.shortenedCode,
                user.get_full_name(),
            )
            continue
        elif invitation.role.userType == RoleDTO.UserType.MANAGER:
            invite_role: RoleAssignmentDTO = invitation.role
            role: RoleDTO = inject.instance(DefaultRoles).get(invite_role.roleId)
            if not role:
                role = helpers.get_custom_role(
                    invitation.role.roleId,
                    invite_role.resource_id(),
                    invite_role.resource_name(),
                )

            invitation_adapter.send_invitation_email(
                invitation.email,
                role.name,
                Client.from_dict(client),
                language,
                invitation.shortenedCode,
                user.get_full_name(),
            )
            continue

        data = {
            SendInvitationRequestObject.INVITATION: invitation,
            SendInvitationRequestObject.SENDER: AuthorizedUser(user),
            SendInvitationRequestObject.CLIENT: client,
            SendInvitationRequestObject.LANGUAGE: language,
        }
        request_obj = SendInvitationRequestObject.from_dict(data)
        use_case = SendInvitationUseCase(invitation_adapter=invitation_adapter)
        use_case.execute(request_obj)


@celery_app.task()
def update_enrollment_counter(user_id: str):
    from sdk.authorization.repository.auth_repository import AuthorizationRepository
    from sdk.authorization.dtos.authorized_user import AuthorizedUser

    repo = inject.instance(AuthorizationRepository)
    try:
        user = repo.retrieve_user(user_id=user_id)
    except UserDoesNotExist:
        return

    if not AuthorizedUser(user).is_user():
        return

    from sdk.deployment.service.deployment_service import DeploymentService

    deployment_id = user.roles[0].resource_id()

    deployment = DeploymentService().update_enrollment_counter(deployment_id)
    data = UserDTO(id=user.id, enrollmentId=deployment.enrollmentCounter)
    repo.update_user_profile(data)


@celery_app.task()
def update_deferred_links_ttl():
    link_adapter = inject.instance(DeferredLinkAdapter, safe=True)
    if not link_adapter or not link_adapter.is_ttl_updater_enabled:
        return

    repo = inject.instance(InvitationRepository)
    invitations = repo.get_universal_invitations_with_deferred_links_about_to_expire()
    if not invitations:
        return

    for inv in invitations:
        updated = link_adapter.update_link_ttl(inv.id, inv.deferredLink, inv.deepLinkValue)
        if updated:
            now = datetime.utcnow()
            inv.deferredLinkExpiresAt = now + link_adapter.LINK_TTL_TIMEDELTA
            repo.update_invitation(inv)
