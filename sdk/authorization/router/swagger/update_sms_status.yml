Update Sms Invitation Status - endpoint for Twilio callback
---
tags:
  - invitation
parameters:
  - in: body
    name: body
    required: true
    schema:
      $ref: '#/definitions/UpdateStatusRequest'
responses:
  200:
    description: Update Sms Status Response
    schema:
      $ref: '#/definitions/UpdateStatusResponse'

definitions:
  UpdateStatusRequest:
    type: object
    required:
      - SmsStatus
      - To
    properties:
      SmsStatus:
        type: string
        enum:
          - 'accepted'
          - 'scheduled'
          - 'canceled'
          - 'queued'
          - 'sending'
          - 'sent'
          - 'failed'
          - 'delivered'
          - 'undelivered'
          - 'receiving'
          - 'received'
      To:
        type: string
        example: "+380509999999"
  UpdateStatusResponse:
    type: object
    properties:
      ok:
        type: boolean
