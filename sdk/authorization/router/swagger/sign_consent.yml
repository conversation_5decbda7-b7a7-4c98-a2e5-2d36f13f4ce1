Sign Consent
---
tags:
  - user
security:
  - Bearer: []
parameters:
  - in: path
    name: user_id
    type: string
    required: true
  - in: path
    name: consent_id
    type: string
    required: true
    description: ID of the consent to sign.
  - name: body
    in: body
    description: body
    required: true
    schema:
      $ref: '#/definitions/SignConsentRequest'
responses:
  200:
    description: Created object ID
    schema:
      $ref: '#/definitions/ObjIdResponse'
definitions:
  SignConsentRequest:
    type: object
    properties:
      givenName:
        type: string
      middleName:
        type: string
      familyName:
        type: string
      signature:
        $ref: "#/definitions/S3Object"
      sharingOption:
        type: integer
        enum: [0, 1, 2, 3, 4]
      agreement:
        type: boolean
        description: True if consent contains AGREEMENT section
      privacy:
        type: boolean
      feedback:
        type: boolean
