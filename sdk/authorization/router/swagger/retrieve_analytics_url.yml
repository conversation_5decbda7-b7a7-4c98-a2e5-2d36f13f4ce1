Retrieve Analytics URL
---
tags:
  - user
security:
  - Bearer: []
parameters:
  - name: body
    in: body
    description: "Json body with filter details"
    required: true
    schema:
      $ref: "#/definitions/AnalyticsURLRequestObject"
responses:
  200:
    description: 200 successful
definitions:
  AnalyticsURLRequestObject:
    type: object
    required:
      - startDate
      - endDate
    properties:
      startDate:
        type: string
        example: "2023-06-29"
      endDate:
        type: string
        example: "2023-06-29"
      deploymentIds:
        type: array
        items:
          type: string
          example: "5e84b0dab8dfa268b1180536"
