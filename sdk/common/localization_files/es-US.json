{"ConfirmationEmail.body": "Verifique su dirección de correo electrónico haciendo clic en el siguiente enlace:", "ConfirmationEmail.buttonText": "Verifique el correo electrónico", "ConfirmationEmail.subject": "Confirmación de correo electrónico", "ConfirmationEmail.subtitle": "Bienvenido a Huma", "ConfirmationEmail.title": "<PERSON><PERSON>, %{username}", "EmailVerification.cp.body": "Verifique su dirección de correo electrónico haciendo clic en el siguiente enlace:", "EmailVerification.cp.buttonText": "Verifique el correo electrónico", "EmailVerification.cp.subject": "Confirmación de correo electrónico", "EmailVerification.cp.title": "<PERSON><PERSON>, %{username}", "EmailVerification.mobile.body": "Ingrese este código en la aplicación Huma para continuar el proceso de inicio de sesión.", "EmailVerification.mobile.buttonText": "<PERSON><PERSON><PERSON>", "EmailVerification.mobile.subject": "Su código de verificación", "EmailVerification.mobile.title": "Este es su código de verificación de Huma.", "MFAPhoneNumberChanged.body": "Si no ha sido usted, le recomendamos cambiar la contraseña de su cuenta y comunicarse con el soporte de aplicaciones o con su centro de soporte dedicado.", "MFAPhoneNumberChanged.subject": "Se actualizó su número de verificación de dos pasos", "MFAPhoneNumberChanged.title": "Se actualizó su número de verificación de dos pasos", "MailGunVerificationTemplateSubject": "Confirmación de correo electrónico", "PasswordChangedEmail.body": "Si no ha sido usted, le recomendamos cambiar la contraseña de su cuenta y comunicarse con el soporte de aplicaciones o con su centro de soporte dedicado.", "PasswordChangedEmail.subject": "Su contraseña ha sido actualizada", "PasswordChangedEmail.title": "Su contraseña ha sido actualizada", "ResetPassword.body": "Hemos recibido una solicitud para restablecer la contraseña de esta cuenta. Para crear una nueva contraseña, haga clic a continuación.<br><br>Si no ha solicitado este enlace de restablecimiento, puede ignorar este correo electrónico.", "ResetPassword.buttonText": "Cree una nueva contraseña", "ResetPassword.mobile.body": "Hemos recibido una solicitud para restablecer la contraseña de esta cuenta. Para crear una nueva contraseña, haga clic a continuación.<br><br>Si no ha solicitado este enlace de restablecimiento, puede ignorar este correo electrónico.", "ResetPassword.subject": "Enlace para restablecer la contraseña", "ResetPassword.subtitle": "Restablezca su contraseña", "SMSAndroidVerificationTemplate": "Su código de verificación %{serviceName} del servicio es: %{verificationCode} de verificación. %{smsRetrieverCode}", "SMSVerificationTemplate": "Su código de verificación %serviceName es: %verificationCode."}