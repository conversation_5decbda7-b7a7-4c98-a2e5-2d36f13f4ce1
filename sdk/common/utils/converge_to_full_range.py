from typing import Union


def string_to_range(str_range: str) -> list[int]:
    """
    @param str_range: Any string value with 2 numbers connected with dash.
    Example: 1-25, 400-812, 13-16
    @return: list of numbers in provided range
    """
    step = 1
    try:
        start, end = str_range.split("-")
        start, end = int(start), int(end)
        if start > end:
            step = -1
        return list(range(start, end + step, step))
    except Exception:
        return []


def converge_to_full_range(question_numbers: list) -> list[Union[int, str]]:
    """
    @param question_numbers: a list containing str_range -
    Any string value with 2 numbers connected with dash.
    Example: ["1-25", "400-812"]
    @return: list of numbers in provided ranges
    """
    numbers = []
    for item in question_numbers:
        if isinstance(item, int):
            numbers.append(item)
            continue
        numbers += list(string_to_range(item))

    return numbers
