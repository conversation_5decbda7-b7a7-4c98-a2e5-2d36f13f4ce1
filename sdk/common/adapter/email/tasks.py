import logging

from celery.schedules import crontab

from sdk.celery.app import celery_app
from sdk.common.adapter.email.repo.email_log_repository import EmailLogRepository
from sdk.common.utils.inject import autoparams

logger = logging.getLogger(__name__)


@celery_app.task
@autoparams("repo")
def clean_email_logs(repo: EmailLogRepository):
    deleted_count = repo.delete_old_logs()
    logger.info(f"Deleted {deleted_count} Email logs")


@celery_app.on_after_finalize.connect
def setup_periodic_tasks(sender, **kwargs):
    sender.add_periodic_task(
        crontab(hour=4, minute=35),
        clean_email_logs.s(),
        name="Clear Email Logs older than 2 weeks",
    )
