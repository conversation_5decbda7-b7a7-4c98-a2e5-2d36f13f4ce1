import unittest
import urllib.parse
from unittest.mock import MagicMock, patch

from sdk.common.adapter.appsflyer import (
    AppsflyerAdapterConfig,
    AppsflyerDeferredLinkAdapter,
)
from sdk.common.exceptions.exceptions import AppsflyerAdapterException

TEST_EMAIL = "<EMAIL>"
TEST_CODE = "12345"
TEST_LINK_ID = "test_link_id"
TEST_DEFERRED_LINK = f"test_deferred_link/{TEST_LINK_ID}"
SAMPLE_ID = "sample_id"
TEST_CONF = {
    "enabled": True,
    "apiKey": "api_key",
    "baseUrl": "base_url",
    "oneLinkId": "app_id",
}
HEADERS = {
    "accept": "application/json",
    "content-type": "application/json",
    "authorization": TEST_CONF["apiKey"],
}
APPSFLYER_ADAPTER_PATH = "sdk.common.adapter.appsflyer._appsflyer_adapter"
DEEPLINK_VALUE = "deep_link_base_url/signup?shortenedCode=12345"


class AppsflyerDeferredLinkAdapterTestCase(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        config = AppsflyerAdapterConfig.from_dict(TEST_CONF)
        cls.adapter = AppsflyerDeferredLinkAdapter(config)

    def setUp(self):
        self.client = MagicMock()
        self.client.deepLinkBaseUrl = "deep_link_base_url"
        self.mock_response = MagicMock()

    @patch(f"{APPSFLYER_ADAPTER_PATH}.request")
    def test_create_link_success(self, mock_post):
        self.mock_response.status_code = 200
        self.mock_response.text = TEST_DEFERRED_LINK
        mock_post.return_value = self.mock_response
        expected_body = {
            "ttl": "365d",
            "data": {"deep_link_value": urllib.parse.quote(DEEPLINK_VALUE), "pid": "huma_media_source"},
        }
        expected_url = f"{TEST_CONF['baseUrl']}/{TEST_CONF['oneLinkId']}"
        generated_url = self.adapter.create_link(DEEPLINK_VALUE, SAMPLE_ID)
        self.assertEqual(generated_url, TEST_DEFERRED_LINK)
        mock_post.assert_called_with("POST", expected_url, headers=HEADERS, json=expected_body, params=None)

    @patch(f"{APPSFLYER_ADAPTER_PATH}.request")
    @patch("sdk.common.adapter.appsflyer._appsflyer_adapter.report_exception")
    @patch("sdk.common.adapter.appsflyer._appsflyer_adapter.AppsflyerAdapterException")
    def test_create_link_failure(self, mock_error, mock_report, mock_post):
        self.mock_response.status_code = 400
        self.mock_response.text.return_value = "errorMessage"
        mock_post.return_value = self.mock_response
        expected_msg = "Failed to create deferred link. Error: errorMessage"
        error = AppsflyerAdapterException(expected_msg)
        mock_error.side_effect = error
        self.adapter.create_link(DEEPLINK_VALUE, SAMPLE_ID)
        mock_report.assert_called_once_with(
            error,
            context_name="AppsflyerDeferredLinkAdapter",
            context_content={"invitation_id": SAMPLE_ID},
        )

    @patch(f"{APPSFLYER_ADAPTER_PATH}.request")
    def test_delete_link_success(self, mock_delete):
        self.mock_response.status_code = 200
        self.mock_response.text.return_value = "ok"
        mock_delete.return_value = self.mock_response
        link = TEST_DEFERRED_LINK
        expected_url = f"{TEST_CONF['baseUrl']}/{TEST_CONF['oneLinkId']}"
        expected_params = {"id": TEST_LINK_ID}
        self.adapter.delete_link(link)
        mock_delete.assert_called_once_with("DELETE", expected_url, headers=HEADERS, json=None, params=expected_params)

    @patch(f"{APPSFLYER_ADAPTER_PATH}.request")
    @patch("sdk.common.adapter.appsflyer._appsflyer_adapter.report_exception")
    @patch("sdk.common.adapter.appsflyer._appsflyer_adapter.AppsflyerAdapterException")
    def test_delete_link_failure(self, mock_error, mock_report, mock_delete):
        self.mock_response.status_code = 400
        self.mock_response.text.return_value = "errorMessage"
        mock_delete.return_value = self.mock_response
        link = TEST_DEFERRED_LINK
        expected_msg = "Failed to delete deferred link. Error: errorMessage"
        error = AppsflyerAdapterException(expected_msg)
        mock_error.side_effect = error
        self.adapter.delete_link(link)
        mock_report.assert_called_once_with(
            error,
            context_name="AppsflyerDeferredLinkAdapter",
            context_content={"deferredLink": link},
        )

    @patch(f"{APPSFLYER_ADAPTER_PATH}.request")
    def test_update_link_ttl_success(self, mock_request):
        self.mock_response.status_code = 200
        self.mock_response.text = TEST_DEFERRED_LINK
        mock_request.return_value = self.mock_response
        body = {
            "ttl": "365d",
            "data": {"deep_link_value": DEEPLINK_VALUE, "pid": "huma_media_source"},
        }
        expected_url = f"{TEST_CONF['baseUrl']}/{TEST_CONF['oneLinkId']}"
        expected_params = {"id": TEST_LINK_ID}
        updated = self.adapter.update_link_ttl(SAMPLE_ID, TEST_DEFERRED_LINK, DEEPLINK_VALUE)
        self.assertEqual(updated, True)
        mock_request.assert_called_with("PUT", expected_url, headers=HEADERS, json=body, params=expected_params)

    @patch(f"{APPSFLYER_ADAPTER_PATH}.request")
    @patch("sdk.common.adapter.appsflyer._appsflyer_adapter.report_exception")
    @patch("sdk.common.adapter.appsflyer._appsflyer_adapter.AppsflyerAdapterException")
    def test_update_link_ttl_failure(self, mock_error, mock_report, mock_delete):
        self.mock_response.status_code = 400
        self.mock_response.text.return_value = "errorMessage"
        mock_delete.return_value = self.mock_response
        link = TEST_DEFERRED_LINK
        expected_msg = "Failed to update deferred link TTL. Error: errorMessage"
        error = AppsflyerAdapterException(expected_msg)
        mock_error.side_effect = error
        self.adapter.update_link_ttl(SAMPLE_ID, TEST_DEFERRED_LINK, DEEPLINK_VALUE)
        mock_report.assert_called_once_with(
            error,
            context_name="AppsflyerDeferredLinkAdapter",
            context_content={"invitation_id": SAMPLE_ID, "deferredLink": link},
        )


if __name__ == "__main__":
    unittest.main()
