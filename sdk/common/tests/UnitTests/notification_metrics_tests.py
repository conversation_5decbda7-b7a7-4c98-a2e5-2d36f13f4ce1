import unittest
from unittest.mock import MagicMock, patch

import boto3
import requests
from aioapns import APNs
from aliyunsdkcore.acs_exception.exceptions import ClientException, ServerException
from aliyunsdkcore.client import AcsClient
from botocore.exceptions import ClientError
from firebase_admin import messaging

from sdk.common.adapter.alibaba.ali_cloud_push_adaptor import AliCloudPushAdapter
from sdk.common.adapter.alibaba.ali_cloud_sms_adapter import AliCloudSmsAdapter
from sdk.common.adapter.apns import APNSPushAdapter
from sdk.common.adapter.email.aws_email_adapter import AWSEmailAdapter
from sdk.common.adapter.email.mailgun_email_adapter import MailgunEmailAdapter
from sdk.common.adapter.email.smtp_email_adapter import SMTPEmailAdapter
from sdk.common.adapter.fcm.fcm_push_adapter import FC<PERSON>ushAdapter
from sdk.common.adapter.twilio.sms_adapter import TwilioSmsAdapter
from sdk.common.exceptions.exceptions import InternalServerErrorException, InvalidRequestException

TWILIO_SMS_ADAPTER_PATH = "sdk.common.adapter.twilio.sms_adapter"
ACS_SMS_ADAPTER_PATH = "sdk.common.adapter.alibaba.ali_cloud_sms_adapter"


class BaseMetricsTestCase(unittest.TestCase):
    def setUp(self):
        self.mocked_config = MagicMock()


class SmsMetricsCalledTestCase(BaseMetricsTestCase):
    @patch(f"{TWILIO_SMS_ADAPTER_PATH}.report_exception")
    @patch(f"{TWILIO_SMS_ADAPTER_PATH}.create_sms_log")
    @patch.object(TwilioSmsAdapter, "_send_total_metric")
    @patch.object(TwilioSmsAdapter, "_send_failed_metric")
    def test_twilio_sms_metrics_sent(
        self,
        mocked_failed,
        mocked_total,
        mocked_create_sms_log,
        mocked_report_exception,
    ):
        with self.assertRaises(InvalidRequestException):
            TwilioSmsAdapter(self.mocked_config).send_sms(phone_number="+380660233999", text="test")
            mocked_failed.assert_called_once()
            mocked_total.assert_called_once()
            mocked_create_sms_log.assert_called_once()
            mocked_report_exception.assert_called_once()

    @patch(f"{ACS_SMS_ADAPTER_PATH}.create_sms_log")
    @patch.object(AliCloudSmsAdapter, "_send_total_metric")
    @patch.object(AliCloudSmsAdapter, "_send_failed_metric")
    def test_alicloud_sms_metrics_sent(self, mocked_failed, mocked_total, mocked_create_sms_log):
        self.mocked_config.params.domain = "localhost"
        self.mocked_config.params.fromId = "testId"
        self.mocked_config.params.templateCode = "template"
        self.mocked_config.accessKeySecret = "secret"
        with self.assertRaises(ClientException):
            AliCloudSmsAdapter(self.mocked_config).send_sms(phone_number="123", text="test")
            mocked_failed.assert_called_once()
            mocked_total.assert_called_once()
            mocked_create_sms_log.assert_called_once()


class EmailMetricsCalledTestCase(BaseMetricsTestCase):
    def _send_data(self):
        return {
            "from_": "<EMAIL>",
            "to": "<EMAIL>",
            "subject": "test subj",
            "html": "text",
        }

    @patch.object(requests, "post")
    @patch.object(MailgunEmailAdapter, "_create_email_log")
    @patch.object(MailgunEmailAdapter, "_send_total_metric")
    @patch.object(MailgunEmailAdapter, "_send_failed_metric")
    def test_mailgun_email_metrics_sent(
        self,
        mocked_failed,
        mocked_total,
        mocked_log,
        mocked_post,
    ):
        mocked_post.return_value = MagicMock(status_code=400)
        self.mocked_config.domainUrl = "localhost"
        self.mocked_config.apiKey = "testKey"
        self.mocked_config.mailgunApiUrlTemplate = "http://localhost/v3/{0}/messages"
        MailgunEmailAdapter(self.mocked_config, "<EMAIL>").send_html_email(**self._send_data())
        mocked_failed.assert_called_once()
        mocked_total.assert_called_once()
        mocked_log.assert_called_once()

    @patch.object(boto3, "client")
    @patch.object(AWSEmailAdapter, "_create_email_log")
    @patch.object(AWSEmailAdapter, "_send_total_metric")
    @patch.object(AWSEmailAdapter, "_send_failed_metric")
    def test_aws_email_metrics_sent(self, mocked_failed, mocked_total, mocked_log, mocked_client):
        self.mocked_config.region = "en"
        self.mocked_config.accessKey = "testKey"
        self.mocked_config.secretKey = "testSecretKey"
        self.mocked_config.email = "<EMAIL>"

        mocked_client().send_raw_email.side_effect = ClientError(
            error_response={"Error": {"Message": "test error"}}, operation_name="test"
        )
        with self.assertRaises(InternalServerErrorException):
            AWSEmailAdapter(self.mocked_config).send_html_email(**self._send_data())
            mocked_failed.assert_called_once()
            mocked_total.assert_called_once()
            mocked_log.assert_called_once()

    @patch.object(SMTPEmailAdapter, "_create_email_log")
    @patch.object(SMTPEmailAdapter, "_send_total_metric")
    @patch.object(SMTPEmailAdapter, "_send_failed_metric")
    def test_smtp_email_metrics_sent(self, mocked_failed, mocked_total, mocked_log):
        with self.assertRaises(InternalServerErrorException):
            SMTPEmailAdapter(self.mocked_config).send_html_email(**self._send_data())
            mocked_failed.assert_called_once()
            mocked_total.assert_called_once()
            mocked_log.assert_called_once()


class PushNotificationMetricsTestCase(BaseMetricsTestCase):
    def _identities_data(self):
        return {
            "identities": ["id1"],
            "message": MagicMock(),
        }

    @patch.object(APNs, "send_notification")
    @patch.object(APNSPushAdapter, "_send_total_metric")
    @patch.object(APNSPushAdapter, "_send_failed_metric")
    def test_apns_push_metrics_sent(self, mocked_failed, mocked_total, mocked_send_msg):
        mocked_send_msg.side_effect = Exception
        with self.assertRaises(Exception):
            APNSPushAdapter(self.mocked_config).send_message_to_identities(**self._identities_data())
            mocked_failed.assert_called_once()
            mocked_total.assert_called_once()

    @patch.object(messaging, "send")
    @patch.object(FCMPushAdapter, "_send_total_metric")
    @patch.object(FCMPushAdapter, "_send_failed_metric")
    def test_fcm_push_metrics_sent(self, mocked_failed, mocked_total, mocked_send_msg):
        mocked_send_msg.side_effect = Exception
        with self.assertRaises(Exception):
            FCMPushAdapter(self.mocked_config).send_message_to_identities(**self._identities_data())
            mocked_failed.assert_called_once()
            mocked_total.assert_called_once()

    @patch.object(AcsClient, "do_action_with_exception")
    @patch.object(AliCloudPushAdapter, "_send_total_metric")
    @patch.object(AliCloudPushAdapter, "_send_failed_metric")
    def test_alicloud_push_metrics_sent(self, mocked_failed, mocked_total, mocked_send_msg):
        mocked_send_msg.side_effect = ServerException
        AliCloudPushAdapter(self.mocked_config).send_message_to_identities(**self._identities_data())
        mocked_failed.assert_called_once()
        mocked_total.assert_called_once()
