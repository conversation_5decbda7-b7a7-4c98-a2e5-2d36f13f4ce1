from unittest import TestCase

from sdk.common.exceptions.exceptions import (
    DetailedException,
    ErrorCodes,
    InternalServerErrorException,
    UnexpectedImplementationError,
)


class TestUnexpectedImplementationError(TestCase):
    def test_annsesters(self):
        exc = UnexpectedImplementationError()
        self.assertIsInstance(exc, InternalServerErrorException)
        self.assertIsInstance(exc, DetailedException)
        self.assertIsInstance(exc, Exception)
        self.assertEqual(
            exc.code,
            ErrorCodes.INVALID_EVENT_IMPLEMENTATION_ERROR,
        )
        self.assertEqual(exc.status_code, 500)
        self.assertEqual(exc.debug_message, "Unexpected implementation of event subscriptions")

    def test_initialization_with_message(self):
        """Test initializing with a custom message."""
        message = "Custom event subscription error"
        exc = UnexpectedImplementationError(message=message)
        self.assertEqual(exc.debug_message, message)
        self.assertEqual(exc.code, ErrorCodes.INVALID_EVENT_IMPLEMENTATION_ERROR)
        self.assertEqual(str(exc), message)

    def test_initialization_with_message_and_reason(self):
        """Test initializing with a custom message and a reason."""
        message = "Custom event subscription error"
        reason = "Invalid handler"
        exc = UnexpectedImplementationError(message=message, reason=reason)
        self.assertEqual(exc.debug_message, f"{message}: {reason}")
        self.assertEqual(exc.code, ErrorCodes.INVALID_EVENT_IMPLEMENTATION_ERROR)
        self.assertEqual(str(exc), f"{message}: {reason}")

    def test_initialization_with_default_message(self):
        """Test initializing with default message."""
        exc = UnexpectedImplementationError()
        default_message = "Unexpected implementation of event subscriptions"
        self.assertEqual(exc.debug_message, default_message)
        self.assertEqual(exc.code, ErrorCodes.INVALID_EVENT_IMPLEMENTATION_ERROR)
        self.assertEqual(str(exc), default_message)

    def test_initialization_with_reason_only(self):
        """Test initializing with only a reason."""
        reason = "Invalid payload structure"
        exc = UnexpectedImplementationError(reason=reason)
        expected_message = f"Unexpected implementation of event subscriptions: {reason}"
        self.assertEqual(exc.debug_message, expected_message)
        self.assertEqual(exc.code, ErrorCodes.INVALID_EVENT_IMPLEMENTATION_ERROR)
        self.assertEqual(str(exc), expected_message)
