from abc import ABC, abstractmethod

from sdk import convertibleclass
from sdk.common.utils.convertible import meta, required_field


@convertibleclass
class CMSSchemaData:
    """
    A data class that represents schema information for the CMS.

    Attributes:
        id (str): The unique identifier for the schema (e.g., "articles").
            - Example: "articles"
        name (str): The display name of the schema, typically used in the UI (e.g., "Featured Articles").
            - Example: "Featured Articles"
        schema (dict): The schema definition as a dictionary, typically in JSON Schema format.
    """

    id: str = required_field(metadata=meta(example="articles"))
    name: str = required_field(metadata=meta(example="Featured Articles"))
    schema: dict = required_field()


class ICMSManager(ABC):
    """
    Abstract base class for managing a CMS and its schema definitions.

    This class serves as a template for defining a CMS manager interface.
    It includes methods for schema registration, retrieval, validation,
    and other schema-related operations. Subclasses must implement the
    abstract methods to provide specific functionality.

    Attributes:
        None
    """

    def __str__(self):
        """
        Returns a string representation of the CMS Manager.

        The representation includes the class name and all registered schemas.
        """
        return f"{self.__class__.__name__} {self.get_all_schemas()}"

    @abstractmethod
    def register_schema(self, schema: CMSSchemaData, safe: bool = True) -> bool:
        """
        Registers a new schema with the CMS.
        """
        raise NotImplementedError

    @abstractmethod
    def get_schema(self, schema_id: str) -> CMSSchemaData | None:
        """
        Retrieves the schema for the given schema ID.

        Args:
            schema_id (str): The unique identifier of the schema.

        Returns:
            CMSSchemaData | None: The schema data if found, otherwise None.

        Raises:
            NotImplementedError: This is an abstract method and must be implemented by subclasses.
        """
        raise NotImplementedError

    @abstractmethod
    def get_all_schemas(self) -> dict[str, CMSSchemaData]:
        """
        Retrieves all registered schemas.

        Returns:
            dict[str, CMSSchemaData]: A dictionary containing all schemas, with schema IDs as keys.

        Raises:
            NotImplementedError: This is an abstract method and must be implemented by subclasses.
        """
        raise NotImplementedError

    @abstractmethod
    def validate_content(self, content: dict, schema_id: str):
        """
        Validates the given content against the schema identified by schema_id.

        Args:
            content (dict): The content to validate.
            schema_id (str): The unique identifier of the schema to validate against.

        Raises:
            NotImplementedError: This is an abstract method and must be implemented by subclasses.
        """
        raise NotImplementedError

    def is_collection_registered(self, collection_id: str) -> bool:
        """
        Checks if a collection is registered.

        This method determines whether a collection with the specified
        collection ID has been registered in the system. It provides a
        way to verify the existence and registration status of the
        collection.

        Parameters:
        collection_id: str
            The unique identifier of the collection to check.

        Returns:
        bool
            True if the collection is registered, otherwise False.
        """
        raise NotImplementedError

    def get_registered_collection_names(self) -> list[str]:
        """
        Retrieves the names of all registered collections.

        This method is intended to provide a list of collection names that are
        currently registered. The specific implementation should define how the
        registration system operates and what constitutes a "registered" collection.

        Returns:
            list[str]: A list of names of collections that are registered.

        Raises:
            NotImplementedError: This method is not implemented and should be
            overridden in a subclass.
        """
        raise NotImplementedError
