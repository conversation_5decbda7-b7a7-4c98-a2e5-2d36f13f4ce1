from sdk.organization.events import PostCreateOrganizationEvent
from sdk.plugin_configuration.use_case import (
    CreatePluginConfigContainerUseCase,
)
from sdk.plugin_configuration.use_case.request_objects import (
    CreatePluginConfigContainerRequestObject,
)


def create_plugin_on_organization_creation_event(event: PostCreateOrganizationEvent):
    req_obj = CreatePluginConfigContainerRequestObject.from_dict(
        {CreatePluginConfigContainerRequestObject.ORGANIZATION_ID: event.organization_id}
    )
    CreatePluginConfigContainerUseCase().execute(req_obj)
