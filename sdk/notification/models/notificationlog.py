from datetime import datetime

from django.db import models

from sdk.notification.constants import PushIdType, NotificationStatus


class NotificationLog(models.Model):
    MONGO_ID = "mongoId"

    class Meta:
        db_table = "notifications"
        indexes = [
            models.Index(fields=["userId"]),
            models.Index(fields=["createDateTime"]),
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    id = models.AutoField(primary_key=True)
    mongoId = models.CharField(max_length=24, unique=True, default=None)
    userId = models.CharField(max_length=24, null=False, blank=False)
    devicePushId = models.CharField(max_length=255, null=False, blank=False)
    devicePushIdType = models.CharField(
        max_length=20,
        null=False,
        blank=False,
        choices=[(tag.value, tag.value) for tag in PushIdType],
    )
    deviceDetails = models.TextField(null=True, blank=True)
    payload = models.JSONField()
    status = models.CharField(
        max_length=20,
        null=False,
        blank=False,
        choices=[(tag.value, tag.value) for tag in NotificationStatus],
    )
    createDateTime = models.DateTimeField(default=datetime.utcnow)
