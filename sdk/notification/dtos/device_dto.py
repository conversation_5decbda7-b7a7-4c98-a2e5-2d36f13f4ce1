from datetime import datetime

from sdk.common.utils.convertible import (
    convertibleclass,
    default_field,
    meta,
    required_field,
)
from sdk.common.utils.validators import default_datetime_meta, validate_id, validate_len
from sdk.notification.constants import PushIdType


@convertibleclass
class DeviceBaseDTO:
    DEVICE_PUSH_ID = "devicePushId"
    DEVICE_PUSH_ID_TYPE = "devicePushIdType"
    DEVICE_DETAILS = "deviceDetails"

    devicePushId: str = required_field(metadata=meta(validate_len(min=1)))
    devicePushIdType: PushIdType = required_field()
    deviceDetails: str = default_field()


@convertibleclass
class DeviceDTO(DeviceBaseDTO):
    MODEL_NAME = "device"

    ID = "id"
    USER_ID = "userId"
    CREATE_DATE_TIME = "createDateTime"
    UPDATE_DATE_TIME = "updateDateTime"

    id: str = default_field(metadata=meta(validate_id))
    userId: str = default_field()
    createDateTime: datetime = default_field(metadata=default_datetime_meta())
    updateDateTime: datetime = default_field(metadata=default_datetime_meta())
