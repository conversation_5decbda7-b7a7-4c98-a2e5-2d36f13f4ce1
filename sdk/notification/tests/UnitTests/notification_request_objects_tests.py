import unittest

from marshmallow import ValidationError

from sdk.common.utils.convertible import ConvertibleClassValidationError
from sdk.notification.dtos.device_dto import DeviceDTO
from sdk.notification.models.device import PushIdType
from sdk.notification.router.notification_requests import (
    RegisterDeviceRequestObject,
    UnRegisterDeviceRequestObject,
)

SAMPLE_VALID_OBJ_ID = "60a20766c85cd55b38c99e12"


class RegisterDeviceRequestObjectTestCase(unittest.TestCase):
    @staticmethod
    def _sample_register_device_req_object(**kwargs):
        req_obj_dict = {
            RegisterDeviceRequestObject.DEVICE_PUSH_ID: "111",
            RegisterDeviceRequestObject.DEVICE_PUSH_ID_TYPE: PushIdType.IOS_VOIP.value,
            **kwargs,
        }
        return RegisterDeviceRequestObject.load(req_obj_dict)

    def test_success_all_fields_are_valid(self):
        try:
            self._sample_register_device_req_object()
        except ConvertibleClassValidationError:
            self.fail()

    def test_failure_must_not_be_present_fields(self):
        must_not_be_present_fields = {
            DeviceDTO.ID: SAMPLE_VALID_OBJ_ID,
            DeviceDTO.CREATE_DATE_TIME: "2020-01-01T10:00:00Z",
            DeviceDTO.UPDATE_DATE_TIME: "2020-01-01T10:00:00Z",
        }
        for field in must_not_be_present_fields:
            obj = self._sample_register_device_req_object(**{field: must_not_be_present_fields[field]})
            self.assertIsNone(getattr(obj, field, None))

    def test_failure_must_be_present_fields_missed(self):
        with self.assertRaises(ValidationError) as e:
            RegisterDeviceRequestObject.load({})
        self.assertIn(RegisterDeviceRequestObject.DEVICE_PUSH_ID_TYPE, e.exception.messages)
        self.assertIn(RegisterDeviceRequestObject.DEVICE_PUSH_ID, e.exception.messages)


class UnregisterDeviceRequestObjectTestCase(unittest.TestCase):
    def test_success_create_unregister_device_req_obj(self):
        obj = UnRegisterDeviceRequestObject.load(
            {
                UnRegisterDeviceRequestObject.DEVICE_PUSH_ID: SAMPLE_VALID_OBJ_ID,
            }
        )
        self.assertEqual(SAMPLE_VALID_OBJ_ID, obj.devicePushId)

    def test_failure_required_fields_missed(self):
        with self.assertRaises(ValidationError) as e:
            UnRegisterDeviceRequestObject.load({})
        self.assertIn(UnRegisterDeviceRequestObject.DEVICE_PUSH_ID, e.exception.messages)


if __name__ == "__main__":
    unittest.main()
