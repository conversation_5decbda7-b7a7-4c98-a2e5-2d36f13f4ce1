# Generated by Django 5.1.5 on 2025-01-16 15:28

import datetime

from django.db import migrations, models

from sdk.common.utils.file_utils import move_mongo_collection_to_postgres_table
from sdk.notification.dtos.device_dto import DeviceDTO
from sdk.notification.dtos.notification_log_dto import NotificationLogDTO


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    @staticmethod
    def migrate_data(apps, schema_editor):
        move_mongo_collection_to_postgres_table(
            apps,
            "notification",
            migrations=[
                {
                    "model_name": "Device",
                    "mongo_model": DeviceDTO.MODEL_NAME,
                },
                {
                    "model_name": "NotificationLog",
                    "mongo_model": NotificationLogDTO.MODEL_NAME,
                },
            ],
        )

    operations = [
        migrations.CreateModel(
            name="Device",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("mongoId", models.Char<PERSON>ield(default=None, max_length=24, unique=True)),
                ("userId", models.CharField(max_length=24)),
                ("devicePushId", models.CharField(max_length=255)),
                (
                    "devicePushIdType",
                    models.CharField(
                        choices=[
                            ("TWILIO", "TWILIO"),
                            ("ANDROID_FCM", "ANDROID_FCM"),
                            ("IOS_APNS", "IOS_APNS"),
                            ("IOS_VOIP", "IOS_VOIP"),
                            ("ALI_CLOUD", "ALI_CLOUD"),
                        ],
                        max_length=20,
                    ),
                ),
                ("deviceDetails", models.TextField(blank=True, null=True)),
                (
                    "updateDateTime",
                    models.DateTimeField(default=datetime.datetime.utcnow),
                ),
                (
                    "createDateTime",
                    models.DateTimeField(default=datetime.datetime.utcnow),
                ),
            ],
            options={
                "db_table": "device",
                "indexes": [models.Index(fields=["userId"], name="device_userId_c6a6a2_idx")],
                "constraints": [models.UniqueConstraint(fields=("userId", "devicePushId"), name="unique_device")],
            },
        ),
        migrations.CreateModel(
            name="NotificationLog",
            fields=[
                ("id", models.AutoField(primary_key=True, serialize=False)),
                ("mongoId", models.CharField(default=None, max_length=24, unique=True)),
                ("userId", models.CharField(max_length=24)),
                ("devicePushId", models.CharField(max_length=255)),
                (
                    "devicePushIdType",
                    models.CharField(
                        choices=[
                            ("TWILIO", "TWILIO"),
                            ("ANDROID_FCM", "ANDROID_FCM"),
                            ("IOS_APNS", "IOS_APNS"),
                            ("IOS_VOIP", "IOS_VOIP"),
                            ("ALI_CLOUD", "ALI_CLOUD"),
                        ],
                        max_length=20,
                    ),
                ),
                ("deviceDetails", models.TextField(blank=True, null=True)),
                ("payload", models.JSONField()),
                (
                    "status",
                    models.CharField(
                        choices=[("success", "success"), ("failure", "failure")],
                        max_length=20,
                    ),
                ),
                (
                    "createDateTime",
                    models.DateTimeField(default=datetime.datetime.utcnow),
                ),
            ],
            options={
                "db_table": "notifications",
                "indexes": [
                    models.Index(fields=["userId"], name="notificatio_userId_0b98a6_idx"),
                    models.Index(fields=["createDateTime"], name="notificatio_createD_f32191_idx"),
                ],
            },
        ),
        migrations.RunPython(migrate_data, lambda *args, **kwargs: 0, atomic=True),
    ]
