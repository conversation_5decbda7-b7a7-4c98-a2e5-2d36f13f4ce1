from contextlib import contextmanager

import typer

from . import DEBUG


@contextmanager
def wrap_exception():
    try:
        yield
    except (typer.Exit, typer.Abort):
        raise
    except Exception as e:
        typer.echo(f"An error occurred: {e}", err=True)
        raise typer.Exit(code=1)


def catch_and_exit(func):
    def wrapper(*args, **kwargs):
        if DEBUG is True:
            return func()
        try:
            with wrap_exception():
                func()
            return 0
        except typer.Exit as e:
            return e.exit_code

    return wrapper


def raise_exit():
    raise typer.Exit()
