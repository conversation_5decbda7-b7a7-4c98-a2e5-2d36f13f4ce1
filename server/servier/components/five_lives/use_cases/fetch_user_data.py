from ._base import FetchUserDataRequestObject, FiveLivesGameUseCase
from ._response import UserDataResponse


class FetchUserDataUseCase(FiveLivesGameUseCase):
    request_object: FetchUserDataRequestObject
    response_object = UserDataResponse.Schema

    def process_request(self, request_object: FetchUserDataRequestObject) -> UserDataResponse:
        games_progress = self._repo.retrieve_progress(request_object.userId)
        return self._build_response(games_progress, self._repo.retrieve_instructions(request_object.userId))
