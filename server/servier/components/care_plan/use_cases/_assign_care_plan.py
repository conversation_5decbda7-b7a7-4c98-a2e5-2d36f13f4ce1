import logging

from sdk.common.usecase.request_object import RequestObject
from sdk.common.usecase.response_object import ResultIdResponseObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.convertible import meta
from sdk.common.utils.fields import id_field
from servier.components.care_plan.dtos import Care<PERSON>lanDTO
from servier.components.care_plan.service import CarePlanService


class AssignCarePlanRequestObject(CarePlanDTO, RequestObject):
    userId: str = id_field(metadata=meta(dump_only=True, view_arg="user_id"))
    # authzUser: AuthorizedUser = default_field(metadata=meta(dump_only=True))

    # @post_load
    # def load_auth_user(self: Schema, data: dict, **kwargs) -> dict:
    #     data["authzUser"] = g.authz_path_user
    #     return data


class AssignCarePlanUseCase(UseCase):
    request_object: AssignCarePlanRequestObject

    def process_request(self, request_object: AssignCarePlanRequestObject):
        logging.info(f"Assigning care plan to user {request_object.userId}")
        result_id = CarePlanService().create_care_plan(request_object)
        return ResultIdResponseObject(result_id)
