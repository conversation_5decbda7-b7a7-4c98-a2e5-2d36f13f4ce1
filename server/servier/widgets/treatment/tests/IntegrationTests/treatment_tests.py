from pathlib import Path

from flask import url_for

from huma_plugins.tests.plugin_test_case import PluginsTestCase
from sdk.auth.component import AuthComponent
from sdk.authorization.component import AuthorizationComponent
from sdk.builder.component import BuilderComponent
from sdk.builder.tests.IntegrationTests import BuilderTestMixin
from sdk.deployment.component import DeploymentComponent
from servier.components.care_plan.component import CarePlanComponent
from servier.widgets.treatment import TreatmentWidget

DEPLOYMENT = "60d9b623b07e15e833eae4a5"
TAB = "60d9b623b07e15e833eae4a6"
WIDGET = "60d9b623b07e15e833eae4a7"
USER = "60d9b676b07e15e833eae4b5"
SUPER_ADMIN = "60d9b5b9b07e15e833eae4a2"


class TreatmentWidgetTestCase(PluginsTestCase, BuilderTestMixin):
    components = [
        AuthComponent(),
        AuthorizationComponent(),
        DeploymentComponent(),
        CarePlanComponent(),
        BuilderComponent(widgets=[TreatmentWidget()]),
    ]
    fixtures = [
        Path(__file__).parent / "fixtures" / "treatment_dump.json",
    ]

    def test_create_treatment_widget(self):
        widget = {
            "type": "com.huma.widget.treatment",
            "order": 1,
            "enabled": True,
            "config": {},
        }
        self._create_widget(widget, DEPLOYMENT, TAB, as_=SUPER_ADMIN)

    def test_get_treatment_data(self):
        data = self._get_widget_data()
        self.assertEqual(0, data["totalCount"])
        self.assertEqual(0, data["completedCount"])

    def _get_widget_data(self, code: int = 200):
        rsp = self.flask_client.get(
            url_for("widgets.get_widget_data_com_huma_widget_treatment", user_id=USER, widget_id=WIDGET),
            headers=self.get_headers_for_token(USER),
        )
        self.assertEqual(code, rsp.status_code)
        return rsp.json
