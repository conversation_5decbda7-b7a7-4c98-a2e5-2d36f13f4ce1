{"deployment": [{"_id": {"$oid": "60d9b623b07e15e833eae4a5"}, "name": "Treatment Test", "status": "DRAFT", "createDateTime": {"$date": "2021-06-28T11:44:35.252Z"}, "builder": {"tabs": [{"id": {"$oid": "60d9b623b07e15e833eae4a6"}, "order": 1, "name": "Orders", "icon": "BULLET_NOTES", "status": "ENABLED", "background": {"value": "#ffffff", "type": "COLOR"}, "widgets": [{"id": {"$oid": "60d9b623b07e15e833eae4a7"}, "type": "com.huma.widget.treatment", "order": 1, "status": "ENABLED", "config": {}}]}]}}], "huma_auth_user": [{"_id": {"$oid": "60d9b5b9b07e15e833eae4a2"}, "status": 1, "emailVerified": true, "email": "<EMAIL>"}, {"_id": {"$oid": "60d9b676b07e15e833eae4b5"}, "status": 1, "emailVerified": true, "email": "<EMAIL>"}], "user": [{"_id": {"$oid": "60d9b5b9b07e15e833eae4a2"}, "email": "<EMAIL>", "roles": [{"roleId": "SuperAdmin", "resource": "deployment/*", "userType": "SuperAdmin", "isActive": true}]}, {"_id": {"$oid": "60d9b676b07e15e833eae4b5"}, "email": "<EMAIL>", "roles": [{"roleId": "User", "resource": "deployment/60d9b623b07e15e833eae4a5", "userType": "User", "isActive": true}], "timezone": "Europe/London", "finishedOnboarding": true}]}