from dataclasses import field
from functools import cached_property

import i18n
from huma_plugins.widgets.vitals import VitalsDataResponseObject
from sdk import convertibleclass, meta
from sdk.authorization.repository.auth_repository import AuthorizationRepository
from sdk.builder.models import GetWidgetDataRequestObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils import inject
from sdk.common.utils.validators import utc_str_field_to_val
from sdk.module_result.modules.questionnaire import QuestionnaireDTO
from sdk.module_result.repository.module_result_repository import ModuleResultRepository
from servier.modules.who import WHOModule

from ._who import _WHOWidgetConfig


@convertibleclass
class WHOWidgetResponse(VitalsDataResponseObject):
    data: list = field(
        default_factory=list,
        metadata=meta(description="Always empty. No chart data supported."),
    )
    value: list[str] = field(
        default_factory=lambda: ["--"],
        metadata=meta(description="Count of submitted questionnaires."),
    )
    seen: bool = field(
        default=False,
        metadata=meta(description="Always false. showSeen not supported."),
    )


class WHOWidgetUseCase(UseCase):
    request_object: GetWidgetDataRequestObject
    response_schema = WHOWidgetResponse.Schema

    def process_request(self, request_object: GetWidgetDataRequestObject):
        return self.build_response()

    def _translate_unit(self, unit: str) -> str:
        return i18n.t(
            f"Questionnaire.General.Unit.{unit}",
            locale=self.request_object.authzUser.get_language(),
            default=unit,
        )

    @property
    def unit(self) -> list[str]:
        return [self._translate_unit("response(s)")]

    def build_response(self) -> WHOWidgetResponse:
        date = None
        if self._last_questionnaire:
            date = utc_str_field_to_val(self._last_questionnaire.startDateTime)

        return WHOWidgetResponse(
            value=[self._submitted_count],
            unit=self.unit,
            seen=False,
            date=date,
            description=self._widget_config.card.description,
        )

    @cached_property
    def _last_questionnaire(self) -> QuestionnaireDTO | None:
        if not (results := self._recent_results):
            return None

        if not (result := results.get(self._widget_config.moduleConfigId)):
            return None

        return result[0][QuestionnaireDTO.get_primitive_name()]

    @cached_property
    def _recent_results(self) -> dict[str, list[dict]]:
        repo = inject.instance(AuthorizationRepository)
        user = repo.retrieve_user(user_id=self.request_object.authzUser.id)
        return user.recentModuleResults

    @property
    def _submitted_count(self) -> str:
        repo = inject.instance(ModuleResultRepository)
        primitive_count = repo.count_primitives(
            user_id=self.request_object.authzUser.id,
            module_id=WHOModule.moduleId,
            primitive_class=QuestionnaireDTO,
            module_config_id=self._widget_config.moduleConfigId,
        )
        if not primitive_count:
            return "--"
        return str(primitive_count)

    @property
    def _widget_config(self) -> _WHOWidgetConfig:
        return self.request_object.widget.config  # noqa
