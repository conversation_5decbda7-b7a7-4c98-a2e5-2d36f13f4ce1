from sdk import convertibleclass
from sdk.builder.models import GetWidgetDataRequestObject
from sdk.common.usecase.response_object import ResponseObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils.convertible import default_field
from sdk.common.utils.inject import autoparams
from servier.components.order_management.dtos import OrderDTO, ProductDTO
from servier.components.order_management.repository import OrderRepository


@convertibleclass
class OMSWidgetResponse(ResponseObject):
    products: list = default_field()
    orders: list = default_field()


class OMSWidgetUseCase(UseCase):
    request_object: GetWidgetDataRequestObject
    response_schema = OMSWidgetResponse.Schema

    @autoparams("repo")
    def __init__(self, repo: OrderRepository):
        self._repo = repo

    def process_request(self, request_object: GetWidgetDataRequestObject):
        products = self._repo.retrieve_products()
        orders = self._repo.retrieve_orders(user_id=request_object.authzUser.id)
        return self.build_response(products, orders)

    @staticmethod
    def build_response(products: list[ProductDTO], orders: list[OrderDTO]) -> OMSWidgetResponse:
        return OMSWidgetResponse(products=products, orders=orders)
