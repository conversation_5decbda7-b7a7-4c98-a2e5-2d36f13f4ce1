from functools import cached_property

import i18n
from boltons.iterutils import first

from huma_plugins.widgets.vitals import VitalsDataResponseObject
from sdk.builder.models import GetWidgetDataRequestObject
from sdk.common.usecase.use_case import UseCase
from sdk.common.utils import inject
from sdk.module_result.exceptions import InvalidModuleConfigurationException
from sdk.module_result.modules import BloodPressureModule
from sdk.module_result.modules.blood_pressure import BloodPressureDTO
from sdk.module_result.repository.module_result_repository import ModuleResultRepository
from ._ai_scan import AIScanUIWidget, ModuleInfo


class AIScanWidgetUseCase(UseCase):
    request_object: GetWidgetDataRequestObject
    response_schema = VitalsDataResponseObject.Schema

    def process_request(self, request_object: GetWidgetDataRequestObject) -> VitalsDataResponseObject:
        return VitalsDataResponseObject(
            value=[str(self._submitted_count or "--")],
            unit=[self.unit],
            description=self._widget_config.card.description,
        )

    @property
    def unit(self) -> str:
        return i18n.t(
            "AIScan.Unit.measurement(s)",
            locale=self.request_object.authzUser.get_language(),
            default="measurement(s)",
        )

    @cached_property
    def _submitted_count(self) -> int:
        module_info = self._find_blood_pressure_module_info()
        repo = inject.instance(ModuleResultRepository)
        primive_count = repo.count_primitives(
            user_id=self.request_object.authzUser.id,
            module_id=module_info.moduleId,
            primitive_class=BloodPressureDTO,
            field_filter={"source": "Lifelight"},
            module_config_id=module_info.id,
        )
        return primive_count

    def _find_blood_pressure_module_info(self) -> ModuleInfo:
        module_info = first(
            self._widget_config.modules,
            key=lambda m: m.moduleId == BloodPressureModule.moduleId,
        )
        if not module_info:
            raise InvalidModuleConfigurationException(
                f"Blood pressure module not found in widget config: {self._widget_config}"
            )
        return module_info

    @cached_property
    def _widget_config(self):
        return AIScanUIWidget.from_dict(
            self.request_object.widget.to_dict(include_none=False),
            use_validator_field=False,
        ).config
