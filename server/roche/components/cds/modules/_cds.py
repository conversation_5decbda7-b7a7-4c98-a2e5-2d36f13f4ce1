from sdk.module_result.modules import QuestionnaireModule
from sdk.module_result.modules.questionnaire import QuestionnaireDTO
from ._cds_calculator import CDSRiskCalculator


class PreEclampsiaRiskModule(QuestionnaireModule):
    """
    Pre-eclampsia risk module is designed to help determine pre-eclampsia risk by collecting user input on
    specific conditions and blood pressure readings.
    """

    moduleId = "PreEclampsiaRisk"
    primitives = [QuestionnaireDTO]
    inputPrimitives = (QuestionnaireDTO,)
    calculator = CDSRiskCalculator
    ragEnabled = False
    visualizers = []
    diaryEnabled = False
